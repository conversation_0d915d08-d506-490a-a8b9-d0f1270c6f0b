# keeps-smartzap-server

[![Quality Gate Status](https://sonar.keepsdev.com/api/project_badges/measure?project=Keeps-Learn_keeps-smartzap-server_AYm9l2639dESJCsQS4C2&metric=alert_status&token=c003f66840a2b0e2ab5217c9a9da919f952ac1bf)](https://sonar.keepsdev.com/dashboard?id=Keeps-Learn_keeps-smartzap-server_AYm9l2639dESJCsQS4C2)
[![Coverage](https://sonar.keepsdev.com/api/project_badges/measure?project=Keeps-Learn_keeps-smartzap-server_AYm9l2639dESJCsQS4C2&metric=coverage&token=c003f66840a2b0e2ab5217c9a9da919f952ac1bf)](https://sonar.keepsdev.com/dashboard?id=Keeps-Learn_keeps-smartzap-server_AYm9l2639dESJCsQS4C2)
[![Maintainability Rating](https://sonar.keepsdev.com/api/project_badges/measure?project=Keeps-Learn_keeps-smartzap-server_AYm9l2639dESJCsQS4C2&metric=sqale_rating&token=c003f66840a2b0e2ab5217c9a9da919f952ac1bf)](https://sonar.keepsdev.com/dashboard?id=Keeps-Learn_keeps-smartzap-server_AYm9l2639dESJCsQS4C2)
[![Security Rating](https://sonar.keepsdev.com/api/project_badges/measure?project=Keeps-Learn_keeps-smartzap-server_AYm9l2639dESJCsQS4C2&metric=security_rating&token=c003f66840a2b0e2ab5217c9a9da919f952ac1bf)](https://sonar.keepsdev.com/dashboard?id=Keeps-Learn_keeps-smartzap-server_AYm9l2639dESJCsQS4C2)
[![Reliability Rating](https://sonar.keepsdev.com/api/project_badges/measure?project=Keeps-Learn_keeps-smartzap-server_AYm9l2639dESJCsQS4C2&metric=reliability_rating&token=c003f66840a2b0e2ab5217c9a9da919f952ac1bf)](https://sonar.keepsdev.com/dashboard?id=Keeps-Learn_keeps-smartzap-server_AYm9l2639dESJCsQS4C2)
[![Vulnerabilities](https://sonar.keepsdev.com/api/project_badges/measure?project=Keeps-Learn_keeps-smartzap-server_AYm9l2639dESJCsQS4C2&metric=vulnerabilities&token=c003f66840a2b0e2ab5217c9a9da919f952ac1bf)](https://sonar.keepsdev.com/dashboard?id=Keeps-Learn_keeps-smartzap-server_AYm9l2639dESJCsQS4C2)
[![Bugs](https://sonar.keepsdev.com/api/project_badges/measure?project=Keeps-Learn_keeps-smartzap-server_AYm9l2639dESJCsQS4C2&metric=bugs&token=c003f66840a2b0e2ab5217c9a9da919f952ac1bf)](https://sonar.keepsdev.com/dashboard?id=Keeps-Learn_keeps-smartzap-server_AYm9l2639dESJCsQS4C2)
[![Lines of Code](https://sonar.keepsdev.com/api/project_badges/measure?project=Keeps-Learn_keeps-smartzap-server_AYm9l2639dESJCsQS4C2&metric=ncloc&token=c003f66840a2b0e2ab5217c9a9da919f952ac1bf)](https://sonar.keepsdev.com/dashboard?id=Keeps-Learn_keeps-smartzap-server_AYm9l2639dESJCsQS4C2)
![example branch parameter](https://github.com/Keeps-Learn/keeps-smartzap-server/actions/workflows/master.yml/badge.svg)

## Overview
keeps-smartzap-server is a backend service designed to manage user enrollments and course interactions via WhatsApp messages and web.

## Features
- **Twilio Studio Integration**: Utilizes Twilio Studio to manage dynamic flows related to course enrollment, content delivery, and completion.
- **Twilio Message Integration**: Utilizes Twilio to send messages and reply interactions related to course enrollment, content delivery, and completion.
- **Database Migrations**: Handles schema changes with Alembic for both SQLite and PostgreSQL.
- **Environment Setup**: Configures local and staging environments via Docker Compose.
- **Task Management**: Uses Celery for asynchronous task execution.

## Twilio Studio and FlowsService
The `FlowsService` module plays a crucial role in interfacing with Twilio Studio to trigger predefined communication flows. It supports various operations such as triggering flows, stopping active flows, and handling exceptions specifically related to active flow conflicts.

### Key Components
- **Flow Management**: Manages different types of flows (enrollment_intro, enrollment_content, and enrollment_finish) essential for the user journey in courses.
- **Exception Handling**: Robust handling of Twilio exceptions, especially to manage active flows, ensuring smooth user experience.
- **Configuration**: Flows are configured and loaded from a JSON file, allowing easy management of Twilio SIDs and related details.
- **Studio Doc**: https://www.twilio.com/docs/studio

## Database migration

#### It's necessary the environment variable: `DATABASE_URL`

New migration:

```
alembic revision -m 'create_tables' --rev-id='001' --autogenerate
```

Run migration to head revision:

```
alembic upgrade head
```

Run migration to previous revision:

```
alembic downgrade -1
```

## Local SQLite:

#### Create a local file: `storage.db`

New migration:

```
make migration msg="create_tables" rev="001"
```

Run migration to head revision:

```
make migration-head
```

Run migration to previous revision:

```
make migration-downgrade
```

## Env Variables(local and stage)

```shell script
DATABASE_URL=postgresql://smartzap:smartzap123@127.0.0.1:5432/smartzap_local_db
MYACC_URL=https://learning-platform-api-stage.keepsdev.com/myaccount
KEYCLOAK_SERVER_URL=https://iam.keepsdev.com/auth/
KEYCLOAK_REALM=keeps-dev
KEEPS_SECRET_TOKEN_INTEGRATION=
KONTENT_URL=https://learning-platform-api-stage.keepsdev.com/kontent
GCM_FIREBASE_KEY=
GCM_FIREBASE_URL=https://firebasedynamiclinks.googleapis.com
SMARTVIEW_URL=https://smartview-stage.keepsdev.com/contents
SCHEDULE_CALLBACK_URL=https://smartzap.requestcatcher.com
DEFAULT_IMAGE_END_COURSE=https://i.ibb.co/ChbFNL5/end.jpg
SCHEDULER_INTERVAL_MINUTES=10
COURSE_PROCESSOR_INTERVAL_MINUTES=12
AWS_BUCKET_NAME=keeps-smartzap-medias-stage
AWS_BASE_S3_URL=https://s3.amazonaws.com
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION_NAME=sa-east-1
CELERY_BROKER_URL=amqp://guest:guest@localhost:5672//
SLACK_LOG_CHANNEL_WEBHOOK=
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_PHONE_NUMBER=
CELERY_BROKER_URL=amqp://guest:guest@localhost:5672//
APP_NAME=smartzap_local_dev
KONTENT_DATABASE_URL=
CELERY_QUEUE=smartzaspion
WHATSAPP_BROKER_SERVICE=twilio
QUIZ_REPORT_PAGE_SIZE=100
BILLING_HOUR=0
ENROLLMENT_WAITING_HOUR=1
ENROLLMENT_LIMIT_DAYS_WAITING=30
```

## Celery

```shell script
celery -A smartzap.scheduler worker -l info
```

## Executar via Docker Compose
1. Instale o [docker-compose](https://docs.docker.com/compose/install/)
2. Baixe o projeto do repositório: ```<NAME_EMAIL>:Keeps-Learn/keeps-smartzap-server.git```<br>
Para clonar o projeto localmente, você precisa adicionar uma chave SSH ao seu perfil no Github.
Caso ainda não tenha feito isso, siga os passos descritos em [Adding a new SSH key to your GitHub account](https://docs.github.com/en/authentication/connecting-to-github-with-ssh/adding-a-new-ssh-key-to-your-github-account)

3. Acesse a pasta ```keeps-smartzap-server```
4. Crie um arquivo **.env.dev** contendo as variáveis de ambiente.<br>
*Solicite ao seu team leader um arquivo já configurado.*
5. Descomente o service ```db``` no arquivo docker-compose.yml caso esteja comentado.<br>
*Se o banco smartzap_dev_db não for criado automaticamente, você poderá fazê-lo manualmente*
*utilizando alguma ferramenta de banco, lembrando que deverá cria-lo no container ```db```*.
5. Execute o comando ```docker-compose up --build```

