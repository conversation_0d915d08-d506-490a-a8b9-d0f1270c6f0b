[coverage:run]
source = .
omit =
    */app.py,
    */application.py,
    */gunicorn_config.py,
    */cron_schedulers.py,
    */smartzap/wsgi.py,
    */scripts/*,
    */smartzap/helper_certificate.py,
    */smartzap/helper_schedulers_simulation.py,
    */smartzap/helper_script_manual.py,
    */smartzap/rounding_performance.py,
    */smartzap/domain/config.py,
    */smartzap/domain/database.py,
    */smartzap/domain/di.py,
    */smartzap/tasks/scheduler.py,
    */smartzap/flask_extensions/auth.py,
    */smartzap/flask_extensions/flask_ext.py,
    */smartzap/flask_extensions/swagger.py,
    */smartzap/flask_extensions/webargs.py,
    */smartzap/flask_extensions/webfilters.py,
    */smartzap/__init__.py,
    */smartzap/flask_extensions/__init__.py,
    */smartzap/tasks/__init__.py,
    */smartzap/tests/__init__.py,
    */smartzap/domain/clients/__init__.py,
    */smartzap/domain/services/__init__.py,
    */temp/__init__.py,
    */smartzap/integration/*,
    */templates/*,
    */venv/*


[coverage:report]
fail_under = 69

[coverage:xml]
output = .reports/coverage/coverage.xml

[coverage:html]
directory = .reports/coverage/
