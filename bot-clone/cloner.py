import copy
import json
import os
import re
import subprocess
import tempfile
import threading

from google.cloud import translate
from google.oauth2 import service_account


class Config:
    current_dir = os.path.dirname(__file__)
    gcloud_credentials_filename = os.path.join(current_dir, 'gcloud.json')
    gcloud_project_id = '<project_id>'

    master_untouchable_project = 'smartzap-bot-prod'
    cli_command_timeout_in_seconds = 600
    source_project = 'smartzap-bot-prod'
    target_project = ''
    source_lang = 'pt-BR'
    target_lang = 'es'


class TwilioCliService:
    def __init__(self, config):
        self.process = None
        self.config = config
        if not config.target_project:
            raise RuntimeError('Empty target project')
        if config.target_project.lower() == config.master_untouchable_project:
            raise RuntimeError('Forbidden target project')

        self.source_project_data = None

    def _run_command(self, command, parameters, directory=None):
        console_output = []

        def command_target():
            args = ['twilio', command, *parameters]
            self.process = subprocess.Popen(args, stdout=subprocess.PIPE, cwd=directory)
            while True:
                output = self.process.stdout.readline()
                if not output:
                    break
                if output:
                    o = output.decode().strip()
                    console_output.append(o)
                    self._log(o)

        thread = threading.Thread(target=command_target)
        thread.start()

        thread.join(self.config.cli_command_timeout_in_seconds)
        if thread.is_alive():
            self.process.kill()
            thread.join()
            raise RuntimeError('Timeout...')

        code = self.process.returncode
        self.process.terminate()
        if code:
            raise RuntimeError('Command Error, Aborting...')
        return console_output

    @staticmethod
    def _log(message):
        if message and message.strip():
            print(message.strip())

    def load_source_project(self):
        directory = tempfile.gettempdir()
        _ = self._run_command('autopilot:export', [f'--unique-name={self.config.source_project}'], directory)
        exported_filename = os.path.join(directory, f'{self.config.source_project}.json')
        with open(exported_filename) as f:
            return json.load(f)

    def can_create_target_project(self):
        output = self._run_command('autopilot:list', [])
        return all(self.config.target_project not in o for o in output)

    def create_target_project(self, data):
        f = tempfile.NamedTemporaryFile('w', suffix='.json', dir=self.config.current_dir, encoding='utf8', delete=False)
        json.dump(data, f, ensure_ascii=False, indent=4)
        base_filename = os.path.basename(f.name)
        f.close()
        _ = self._run_command('autopilot:create', ['-s', base_filename], self.config.current_dir)
        os.remove(f.name)


class TwilioDataService:
    """
    file format:
    tasks = [
       {
          actions = {
             actions = [
                {
                   say = <translate>
                }
                   redirect = {
                      uri = <uri>?lang=target_lang
                   }
             ]
          }
          samples = [
             {
                taggedText = <translate>
             }
          ]
       }
    ]
    """

    @staticmethod
    def extract_texts(data):
        texts = []
        tasks = data.get('tasks', [])
        for task in tasks:
            actions_attribute = task.get('actions', {})
            actions = actions_attribute.get('actions', [])
            for action in actions:
                text = action.get('say')
                if text:
                    texts.append(text)
            samples = task.get('samples', [])
            for sample in samples:
                text = sample.get('taggedText')
                if text:
                    texts.append(text)
        return texts

    def update_texts(self, data, texts, target_lang):
        data_texts = self.extract_texts(data)
        assert len(data_texts) == len(texts)

        i = 0
        updated_data = copy.deepcopy(data)
        tasks = updated_data.get('tasks', [])
        for task in tasks:
            actions_attribute = task.get('actions', {})
            actions = actions_attribute.get('actions', [])
            for action in actions:
                say = action.get('say')
                if say:
                    action['say'] = texts[i]
                    i += 1
                redirect = action.get('redirect')
                if redirect:
                    new_uri = self.get_uri_with_lang(redirect.get('uri'), target_lang)
                    if new_uri:
                        action['redirect']['uri'] = new_uri
            samples = task.get('samples', [])
            for sample in samples:
                text = sample.get('taggedText')
                if text:
                    sample['taggedText'] = texts[i]
                    i += 1
        return updated_data

    @staticmethod
    def update_unique_name(data, unique_name):
        data['uniqueName'] = unique_name
        model_build = data.get('modelBuild', {})
        if model_build:
            model_build['uniqueName'] = 'v0.1'

    @staticmethod
    def get_uri_with_lang(uri, lang):
        if uri:
            return f'{uri}?lang={lang}'
        return None


class GoogleTranslateService:
    token_default = '##########'

    def __init__(self, config):
        self.config = config
        self.client = translate.TranslationServiceClient(credentials=self._load_gcloud_credentials())

    def _load_gcloud_credentials(self):
        return service_account.Credentials.from_service_account_file(self.config.gcloud_credentials_filename)

    def translate(self, texts):
        location = 'global'
        parent = f'projects/{self.config.gcloud_project_id}/locations/{location}'

        originals_tokens = self._save_tokens(texts)
        request = self._build_translate_request(parent, texts)
        response = self.client.translate_text(request=request)
        translations = [translation.translated_text for translation in response.translations]
        translations = self._restore_tokens(originals_tokens, translations)
        return translations

    def _save_tokens(self, texts):
        texts_tokens = []
        for i, t in enumerate(texts):
            tokens_results = re.finditer(r'{.*?}', t)
            tokens = [item.group(0) for item in tokens_results]
            texts_tokens.append(tokens)
            texts[i] = re.sub(r'{.*?}', self.token_default, t)
        return texts_tokens

    def _restore_tokens(self, originals_tokens, texts):
        restored_texts = []
        for i, t in enumerate(texts):
            tokens = originals_tokens[i]
            for token in tokens:
                t = t.replace(self.token_default, token, 1)

            restored_texts.append(t)
        return restored_texts

    def _build_translate_request(self, parent, texts):
        request = translate.TranslateTextRequest()
        request.parent = parent
        request.contents = texts
        request.mime_type = 'text/plain'
        request.source_language_code = self.config.source_lang
        request.target_language_code = self.config.target_lang
        return request


def run(config, cli_service, data_service, translate_service):
    if not cli_service.can_create_target_project():
        print('Target project already exists.')
        return

    print('Exporting project for clone')
    project_data = cli_service.load_source_project()
    project_texts = data_service.extract_texts(project_data)
    print('Translating')
    translations = translate_service.translate(project_texts)
    target_project_data = data_service.update_texts(project_data, translations, config.target_lang)
    data_service.update_unique_name(target_project_data, config.target_project)
    print('Creating target project')
    cli_service.create_target_project(target_project_data)
    print('Done')


def main():
    config = Config()
    twilio_cli_service = TwilioCliService(config)
    data_service = TwilioDataService()
    google_translate_service = GoogleTranslateService(config)
    run(config, twilio_cli_service, data_service, google_translate_service)


if __name__ == '__main__':
    main()
