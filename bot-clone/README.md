# El Copiadoro de Bots
Esse script faz um clone de um projeto de assistant do Twilio e já traduz usando google translate (por API).
### Requisitos para Rodar
1. Instalar o twilio-cli
1. Fazer o login no twilio-cli
1. Instalar o plugin do assistant no twilio-cli.
1. Ter o Json com as permissões de API do Google Cloud. Esse json está nessa mesma pasta como `gcloud.json`.
1. Configurar o script `cloner.py`

Não foi criado estilo `"cli"`, deve alterar o código e rodar.
### Configurações
1. Id do projeto do glcoud, vai estar no próprio json de credenciais. Exemplo: `"project198421"`
1. Nome do projeto de cópia. Atributo `source_project`. Exemplo: `smartzap-bot-prod`
1. Nome para o novo projeto. Atributo `target_project`.
1. Idioma do projeto de origem (source_project). Exemplo: `pt-BR`
1. Idioma do novo projeto (target_project). Exemplo: `es`

### Rodar
1. Instalar os requirements. Atualmente apenas a lib de acesso ao google translate.
1. Rodar o módulo `cloner.py`. Exemplo: `python cloner.py`
1. Vai demorar, o processo de download do projeto base e principlamente o upload (criação do projeto) demoram muuuito. Por isso timeout está em 10min.