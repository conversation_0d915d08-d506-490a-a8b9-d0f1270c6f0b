#!/bin/bash

export DATABASE_URL=postgres://[user]:[password]@rds-postgres-stage.cpd3dmaosiyq.us-east-1.rds.amazonaws.com/smartzap_dev_db
export KEYCLOAK_SERVER_URL=https://iam.keepsdev.com/auth/
export KEYCLOAK_REALM=keeps-dev
export MYACC_URL=https://learning-platform-api-stage.keepsdev.com/myaccount
export KONTENT_URL=https://learning-platform-api-stage.keepsdev.com/kontent

# Declare empty Slack channel to avoid reporting errors in dev
export SLACK_LOG_CHANNEL_WEBHOOK=

export DEBUG=True
export FLASK_ENV=development
export FLASK_APP=wsgi.py
export PYTHONPATH="${PYTHONPATH}:${PWD}/smartzap/"

flask run --port=8000
