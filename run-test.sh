#!/bin/bash

### Local ###
# export DATABASE_NAME=smartzap_dev_db
# export DATABASE_USER=postgres
# export DATABASE_PASSWORD=postgres
# export DATABASE_HOST=localhost
# export DATABASE_PORT=5432

# Declare empty Slack channel to avoid reporting errors in dev
export SLACK_LOG_CHANNEL_WEBHOOK=

export DEBUG=True
export ENVIRONMENT=development
export ENVIRONMENT_TEST=True
export SUSPEND_SIGNALS=True
export PYTHONPATH="${PYTHONPATH}:${PWD}/smartzap/"

cd smartzap
pytest $@
cd ..
