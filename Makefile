.SILENT .PHONY: migration migration-head migration-downgrade migration-downgrade-1 run test code-convention docker-run-local

migration:
	export DATABASE_URL="sqlite:///"$(shell dirname $(realpath $(firstword $(MAKEFILE_LIST))))/storage.db;\
	alembic revision -m $(msg) --rev-id=$(rev) --autogenerate

migration-head:
	export DATABASE_URL="sqlite:///"$(shell dirname $(realpath $(firstword $(MAKEFILE_LIST))))/storage.db;\
	alembic upgrade head

migration-downgrade:
	export DATABASE_URL="sqlite:///"$(shell dirname $(realpath $(firstword $(MAKEFILE_LIST))))/storage.db;\
	alembic downgrade base

migration-downgrade-1:
	export DATABASE_URL="sqlite:///"$(shell dirname $(realpath $(firstword $(MAKEFILE_LIST))))/storage.db;\
	alembic downgrade -1

run:
	export DATABASE_URL="sqlite:///"$(shell dirname $(realpath $(firstword $(MAKEFILE_LIST))))/storage.db;\
	python smartzap/app.py

setup:
	pip install -r smartzap/requirements.txt

setup-dev:
	pip install --upgrade pip ;\
	pip install -r smartzap/requirements-dev.txt

test:
	export PYTHONPATH=""$(shell dirname $(realpath $(firstword $(MAKEFILE_LIST))))/smartzap;\
	py.test -v ;\
	cd ..

test-cov:
	mkdir -p .reports
	export PYTHONPATH=""$(shell dirname $(realpath $(firstword $(MAKEFILE_LIST))))/smartzap;\
	coverage run -m pytest smartzap -v --junitxml=.reports/xunit/xunit.xml
	coverage report -m
	coverage xml

docker-run-local:
	docker build -t smartzap -f Dockerfile_web . ;\
	docker run -it --rm --name my-smartzap -p 5000:80 smartzap

run-load-tests:
	echo Please fill the envs ;\
	export MYACC_URL="https://learning-platform-api-stage.keepsdev.com/myaccount/auth" ;\
	export MYACC_USERNAME="<EMAIL>" ;\
	export MYACC_PASSWORD="<ADD-PASSWORD-HERE>" ;\
	locust -f load_tests/locustfile.py

ruff-check:
	mkdir -p reports
	ruff check . --output-format=pylint --output-file=reports/output_flake.txt