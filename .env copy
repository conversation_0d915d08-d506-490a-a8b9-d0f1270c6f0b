APP_NAME=smartzap-stage
AWS_ACCESS_KEY_ID=********************
AWS_BASE_S3_URL=https://s3.amazonaws.com
AWS_BUCKET_NAME=keeps-smartzap-medias-stage
AWS_LAMBDA_ACCESS_KEY_ID=********************
AWS_LAMBDA_REGION_NAME=us-east-1
AWS_LAMBDA_SECRET_ACCESS_KEY=RhLUlAQ+rDbAOkaXIG3iGfiLQ4zh0TIGiY/z402z
AWS_REGION_NAME=us-east-1
AWS_SECRET_ACCESS_KEY=TBUBJeg2AMDGa7abkQBdUCrRGF8Loz5ZlwXKiChf
CELERY_BROKER_URL=amqp://admin:admin@rabbitmq:5672
CELERY_QUEUE=smartzap-stage
CHATTERBOT_INTERVAL_MINUTES=2
COURSE_PROCESSOR_INTERVAL_MINUTES=2
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/smartzap_dev_db
DEBUG=True
DEFAULT_IMAGE_DISCLAIMER=https://s3.amazonaws.com/keeps-smartzap-medias/view-assets/disclaimer_pr_BR.jpg
DEFAULT_IMAGE_END_COURSE=https://s3.amazonaws.com/keeps-smartzap-medias/view-assets/congratulations_pt_BR.jpg
DEFAULT_IMAGE_LESSON=https://s3.amazonaws.com/keeps-smartzap-medias/view-assets/new_lession_pt_BR.jpg
ELASTIC_APM_ENVIRONMENT=stage
ELASTIC_APM_SECRET_TOKEN=UGvFOr8Rghs1uKtXx8
ELASTIC_APM_SERVER_URL=https://keeps.apm.us-east-1.aws.found.io:443
ENROLLMENT_LIMIT_DAYS_WAITING=30
ENVIRONMENT=staging
GCM_FIREBASE_KEY=AIzaSyDTTd5FpDZyHF86-EYgWDyi2POuaEUOVHQ
GCM_FIREBASE_URL=https://firebasedynamiclinks.googleapis.com
KEEPS_SECRET_TOKEN_INTEGRATION=637a2f9e72daba2ebb03a699c7a4c08d
*******************************
KEYCLOAK_REALM=keeps-dev
KEYCLOAK_SERVER_URL=https://iam.keepsdev.com/auth
KEYCLOAK_USER_ADMIN=<EMAIL>
KEYCLOAK_CLIENT_ID=smartzap
KEYCLOAK_CLIENT_SECRET=85c15807-6ff6-450d-9159-4f189c264598
KONTENT_DATABASE_URL=postgresql://postgres:<EMAIL>:5432/kontent_dev_db
KONTENT_URL=https://learning-platform-api-stage.keepsdev.com/kontent
LEARN_ANALYTICS_URL=https://learning-platform-api-stage.keepsdev.com/analytics/api/v1/
LOG_LEVEL=error
MYACC_URL=https://learning-platform-api-stage.keepsdev.com/myaccount
MYACC_V2_URL=https://learning-platform-api-stage.keepsdev.com/myaccount-v2
PYTHONUNBUFFERED=1
QUIZ_REPORT_PAGE_SIZE=100
SCHEDULE_CALLBACK_URL=https://learning-platform-api-stage.keepsdev.com/smartzap
SCHEDULER_INTERVAL_MINUTES=1
SLACK_LOG_CHANNEL_WEBHOOK=*******************************************************************************
SMARTVIEW_URL=http://smartview-stage.keepsdev.com/contents
SMS_FIRE_KEY=a2VlcHNkZXY6S2VlcHMwMTEwMDFA
SMS_FIRE_TOKEN_WHATSAPP=25a2fb618f39ef2f5e2448b7a1fad0cb
SMS_FIRE_URL=https://api.smsfire.com.br/v1/
TWILIO_ACCOUNT_SID=**********************************
TWILIO_AUTH_TOKEN=3ce181845e6faa49685b7cdb81fe18a1
TWILIO_CHANNELS={"pt-BR":"twilio://**********************************:3ce181845e6faa49685b7cdb81fe18a1@+***********", "es":"twilio://**********************************:3ce181845e6faa49685b7cdb81fe18a1@+************"}
USER_TOKEN_EXPIRATION=5
WEB_CONCURRENCY=2
REPORT_GENERATOR_SERVER_URL=https://learning-platform-api-stage.keepsdev.com/report-generator