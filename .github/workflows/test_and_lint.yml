name: Unit tests and lint

on:
  workflow_call:
    inputs:
      run-build:
        default: true
        type: boolean
        required: false

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    steps:
    - run: mkdir -p .reports

    - uses: actions/checkout@v4

    - name: Set up Python 3.9
      uses: actions/setup-python@v4
      with:
        python-version: "3.9"
        cache: "pip"

    - name: Install dependencies
      run: |
        make setup-dev

    - name: Running Code Conventions
      run: |
        make ruff-check

    - name: Run unit test
      run: |
        make test-cov

    - name: Upload artifacts
      uses: actions/upload-artifact@v4
      with:
        name: reports
        path: .reports/
        include-hidden-files: true

