name: Pipeline (CI)
on:
  workflow_dispatch:
  pull_request:
    branches:
      - develop
      - main
    types: [opened, synchronize, reopened]

jobs:
  tests:
    name: Run unit tests
    uses: ./.github/workflows/test_and_lint.yml

  sonar:
    name: Quality Analysis
    if: github.ref_name != 'main'
    uses: ./.github/workflows/sonar.yml
    needs: [tests]
    secrets:
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
