name: Fast Forward
on:
  issue_comment:
    types: [created, edited]

jobs:
  fast-forward:
    # Only run if the comment contains the /fast-forward command.
    if: ${{ contains(github.event.comment.body, '/fast-forward')
      && github.event.issue.pull_request }}
    runs-on: ubuntu-latest

    permissions:
      contents: write
      pull-requests: write
      issues: write

    steps:
      - name: Fast forwarding
        uses: sequoia-pgp/fast-forward@v1
        with:
          github_token: ${{ secrets.KEEPS_ACTIONS_PERSONAL_ACCESS_TOKEN }}
          merge: true
          comment: on-error
