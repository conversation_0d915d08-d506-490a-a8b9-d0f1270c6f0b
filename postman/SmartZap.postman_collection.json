{"info": {"_postman_id": "24ed923e-a3f9-4a0c-a15c-180c46d1de1c", "name": "SmartZap", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Tests", "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "Login - Stage", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "    ", "    var jsonData = pm.response.json();", "    pm.environment.set(\"access_token\", jsonData.access_token);", "    pm.environment.set(\"refresh_token\", jsonData.refresh_token);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "username", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "123456", "type": "text"}]}, "url": {"raw": "https://learning-platform-api-stage.keepsdev.com/myaccount/auth", "protocol": "https", "host": ["myaccount-api-stage", "keepsdev", "com"], "path": ["auth"]}}, "response": []}]}, {"name": "Create", "item": [{"name": "Create Company", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "", "var jsonData = pm.response.json();", "pm.environment.set(\"my_company_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "x-client", "type": "text", "value": "{{company_id}}"}], "body": {"mode": "raw", "raw": "{\n\t\"id\": \"e76b5082-f4fe-4f41-be79-1977840e16a8\",\n    \"name\": \"My Company Uhull\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/company", "host": ["{{url}}"], "path": ["api", "v1", "company"]}}, "response": []}, {"name": "Create Course", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"course_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"category_id\": \"{{course_category_id}}\",\n    \"name\": \"Como nao apanhar no Muay thai\",\n    \"description\": \"<PERSON>uia rapido de nao levar uma surra no Muay thai\",\n    \"holder_image\": \"http://www.imagens.com.br/imagem-legal\",\n    \"thumb_image\": \"http://www.imagens.com.br/imagem-legal\",\n    \"is_active\": false,\n    \"status\": \"CREATING\",\n    \"lang\": \"pt-br\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/course", "host": ["{{url}}"], "path": ["api", "v1", "course"]}, "description": "Status accepted:  \n * CREATING\n * REVIEWING\n * FINISHED"}, "response": []}, {"name": "C<PERSON> <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"lesson_id\", jsonData.id);", "pm.environment.set(\"lesson1_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"Lesson 1\",\n    \"description\": \"<PERSON>ou um chutinho na cabeca eh??\",\n    \"order\": 1,\n    \"course_id\": \"{{course_id}}\"\n}\n"}, "url": {"raw": "{{url}}/api/v1/lesson", "host": ["{{url}}"], "path": ["api", "v1", "lesson"]}}, "response": []}, {"name": "Create Content", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"content_id\", jsonData.id);", "pm.environment.set(\"content1_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"Content 1\",\n    \"description\": \"Description of content 1\",\n    \"order\": 1,\n    \"dispatch_in\": 3,\n    \"learn_content\": \"12312-fwr33f-12f12f13f41-f1\",\n    \"lesson_id\": \"{{lesson_id}}\",\n    \"type_id\": \"{{content_type_id}}\"\n}\n"}, "url": {"raw": "{{url}}/api/v1/content", "host": ["{{url}}"], "path": ["api", "v1", "content"]}}, "response": []}, {"name": "Create User", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"user_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"Pessoa Legal\",\n    \"phone\": \"{{phone_number}}\",\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/user", "host": ["{{url}}"], "path": ["api", "v1", "user"]}}, "response": []}, {"name": "Create Activity", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"activity_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"user_id\": \"{{user_id}}\",\n    \"content_id\": \"{{content_id}}\",\n    \"action\": \"LISTEN\",\n    \"start_at\": \"{{datetime}}\",\n    \"stop_at\": \"{{datetime}}\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/view/activity", "host": ["{{url}}"], "path": ["api", "v1", "view", "activity"]}, "description": "Actions accepted:\n * WATCH\n * LISTEN\n * READ\n * VIEW"}, "response": []}, {"name": "Create <PERSON><PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"feedback_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"company_id\": \"{{company_id}}\",\n    \"tag\": \"Tag Tag Tag\",\n    \"message\": \"<PERSON><PERSON> bom esse curso\",\n    \"scope\": \"Legal\"\n}\n"}, "url": {"raw": "{{url}}/api/v1/feedback", "host": ["{{url}}"], "path": ["api", "v1", "feedback"]}}, "response": []}, {"name": "Create User 1", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"user_id_1\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"User 1\",\n    \"phone\": \"{{phone_number}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/user", "host": ["{{url}}"], "path": ["api", "v1", "user"]}}, "response": []}, {"name": "Create User 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"user_id_2\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"User 2\",\n    \"phone\": \"{{phone_number}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/user", "host": ["{{url}}"], "path": ["api", "v1", "user"]}}, "response": []}, {"name": "Create User 3", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"user_id_3\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"User 3\",\n    \"phone\": \"{{phone_number}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/user", "host": ["{{url}}"], "path": ["api", "v1", "user"]}}, "response": []}]}, {"name": "Update", "item": [{"name": "Update Course", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"category_id\": \"{{course_category_id}}\",\n    \"name\": \"Como nao apanhar no Muay thai\",\n    \"description\": \"<PERSON>uia rapido de nao levar uma surra no <PERSON> thai\",\n    \"holder_image\": \"http://www.imagens.com.br/imagem-legal2\",\n    \"thumb_image\": \"http://www.imagens.com.br/imagem-legal2\",\n    \"is_active\": true,\n    \"status\": \"REVIEWING\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/course/{{course_id}}", "host": ["{{url}}"], "path": ["api", "v1", "course", "{{course_id}}"]}}, "response": []}, {"name": "Update Lesson", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"Lesson 1\",\n    \"description\": \"Levou um chutinho na cabeca e ficou boladao\",\n    \"order\": 1,\n    \"course_id\": \"{{course_id}}\"\n}"}, "url": {"raw": "{{url}}/api/v1/lesson/{{lesson_id}}", "host": ["{{url}}"], "path": ["api", "v1", "lesson", "{{lesson_id}}"]}}, "response": []}, {"name": "Update Content", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"Content 1\",\n    \"description\": \"Description of the super content 1\",\n    \"order\": 1,\n    \"dispatch_in\": 3,\n    \"learn_content\": \"12312-fwr33f-12f12f13f41-f1\",\n    \"lesson_id\": \"{{lesson_id}}\",\n    \"type_id\": \"{{content_type_id}}\"\n}\n"}, "url": {"raw": "{{url}}/api/v1/content/{{content_id}}", "host": ["{{url}}"], "path": ["api", "v1", "content", "{{content_id}}"]}}, "response": []}, {"name": "Update User", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"Pessoa Legal Pra Chuchu\",\n    \"phone\": \"{{phone_number}}\",\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/user/{{user_id}}", "host": ["{{url}}"], "path": ["api", "v1", "user", "{{user_id}}"]}}, "response": []}, {"name": "Update Activity", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"action\": \"READ\",\n    \"start_at\": \"{{datetime}}\",\n    \"stop_at\": \"{{datetime}}\",\n    \"duration\": 22\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/view/activity/{{activity_id}}", "host": ["{{url}}"], "path": ["api", "v1", "view", "activity", "{{activity_id}}"]}}, "response": []}, {"name": "Update Feedback", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"company_id\": \"{{company_id}}\",\n    \"tag\": \"Tag2 Tag2 Tag2\",\n    \"message\": \"Muito2 bom2 esse2 curso2\",\n    \"scope\": \"Legal2\"\n}\n"}, "url": {"raw": "{{url}}/api/v1/feedback/{{feedback_id}}", "host": ["{{url}}"], "path": ["api", "v1", "feedback", "{{feedback_id}}"]}}, "response": []}]}, {"name": "Read", "item": [{"name": "Read Course Category", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/course-category/{{course_category_id}}", "host": ["{{url}}"], "path": ["api", "v1", "course-category", "{{course_category_id}}"]}}, "response": []}, {"name": "Read Course", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-Client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/course/{{course_id}}", "host": ["{{url}}"], "path": ["api", "v1", "course", "{{course_id}}"]}}, "response": []}, {"name": "Read All Course", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-Client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/course", "host": ["{{url}}"], "path": ["api", "v1", "course"]}}, "response": []}, {"name": "Read Course Enrollments", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-Client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/course/{{course_id}}/enrollment", "host": ["{{url}}"], "path": ["api", "v1", "course", "{{course_id}}", "enrollment"], "query": [{"key": "page", "value": "1", "disabled": true}, {"key": "per_page", "value": "1", "disabled": true}]}}, "response": []}, {"name": "Read Course Lessons", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-Client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/course/{{course_id}}/lesson", "host": ["{{url}}"], "path": ["api", "v1", "course", "{{course_id}}", "lesson"], "query": [{"key": "page", "value": "1", "disabled": true}, {"key": "per_page", "value": "1", "disabled": true}]}}, "response": []}, {"name": "<PERSON>on", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/lesson/{{lesson_id}}", "host": ["{{url}}"], "path": ["api", "v1", "lesson", "{{lesson_id}}"]}}, "response": []}, {"name": "Read Lesson Contents", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/lesson/{{lesson_id}}/content", "host": ["{{url}}"], "path": ["api", "v1", "lesson", "{{lesson_id}}", "content"], "query": [{"key": "page", "value": "1", "disabled": true}, {"key": "per_page", "value": "1", "disabled": true}]}}, "response": []}, {"name": "Read Content Type", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/content-type/{{content_type_id}}", "host": ["{{url}}"], "path": ["api", "v1", "content-type", "{{content_type_id}}"]}}, "response": []}, {"name": "Read Content", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/content/{{content_id}}", "host": ["{{url}}"], "path": ["api", "v1", "content", "{{content_id}}"]}}, "response": []}, {"name": "Read Content Activities", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/content/{{content_id}}/activity", "host": ["{{url}}"], "path": ["api", "v1", "content", "{{content_id}}", "activity"], "query": [{"key": "page", "value": "1", "disabled": true}, {"key": "per_page", "value": "1", "disabled": true}]}}, "response": []}, {"name": "Read User", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/user/{{user_id}}", "host": ["{{url}}"], "path": ["api", "v1", "user", "{{user_id}}"]}}, "response": []}, {"name": "Read Users", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/user", "host": ["{{url}}"], "path": ["api", "v1", "user"]}}, "response": []}, {"name": "Read Activity", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/view/activity/{{activity_id}}", "host": ["{{url}}"], "path": ["api", "v1", "view", "activity", "{{activity_id}}"]}}, "response": []}, {"name": "<PERSON>back", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/feedback/{{feedback_id}}", "host": ["{{url}}"], "path": ["api", "v1", "feedback", "{{feedback_id}}"]}}, "response": []}]}, {"name": "Delete", "item": [{"name": "Delete Activity", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/activity/{{activity_id}}", "host": ["{{url}}"], "path": ["api", "v1", "activity", "{{activity_id}}"]}}, "response": []}, {"name": "Delete Content", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/content/{{content_id}}", "host": ["{{url}}"], "path": ["api", "v1", "content", "{{content_id}}"]}}, "response": []}, {"name": "Delete Lesson", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/lesson/{{lesson_id}}", "host": ["{{url}}"], "path": ["api", "v1", "lesson", "{{lesson_id}}"]}}, "response": []}, {"name": "Delete Course", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/course/{{course_id}}", "host": ["{{url}}"], "path": ["api", "v1", "course", "{{course_id}}"]}}, "response": []}, {"name": "Delete User", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "x-client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/user/{{user_id}}", "host": ["{{url}}"], "path": ["api", "v1", "user", "{{user_id}}"]}}, "response": []}, {"name": "Delete User 1", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/user/{{user_id_1}}", "host": ["{{url}}"], "path": ["api", "v1", "user", "{{user_id_1}}"]}}, "response": []}, {"name": "Delete User 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "x-client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/user/{{user_id_2}}", "host": ["{{url}}"], "path": ["api", "v1", "user", "{{user_id_2}}"]}}, "response": []}, {"name": "Delete User 3", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "x-client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/user/{{user_id_3}}", "host": ["{{url}}"], "path": ["api", "v1", "user", "{{user_id_3}}"]}}, "response": []}, {"name": "Delete Feedback", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/feedback/{{feedback_id}}", "host": ["{{url}}"], "path": ["api", "v1", "feedback", "{{feedback_id}}"]}}, "response": []}, {"name": "Delete Company", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "x-client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/company/{{company_id}}", "host": ["{{url}}"], "path": ["api", "v1", "company", "{{company_id}}"]}}, "response": []}]}]}, {"name": "Schedule Simulation", "item": [{"name": "Create Company", "event": [{"listen": "test", "script": {"exec": ["pm.test('Check status code', () => {", "    pm.expect(pm.response.code).to.be.oneOf([201, 403])", "})"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"id\": \"{{company_id}}\",\n    \"name\": \"My Company Uhull\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/company", "host": ["{{url}}"], "path": ["api", "v1", "company"]}}, "response": []}, {"name": "Create User <PERSON><PERSON>ho", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"user_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "x-client", "type": "text", "value": "{{company_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"id\": \"bbf47825-8dfb-49bc-8ad8-f8adc775f95f\",\n\t\"name\": \"Keeps\",\n    \"phone\": \"+55 (48) 9011-0011\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/user", "host": ["{{url}}"], "path": ["api", "v1", "user"]}}, "response": []}, {"name": "Create Course", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"course_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"category_id\": \"{{course_category_id}}\",\n    \"name\": \"Como nao apanhar da muie\",\n    \"description\": \"<PERSON>uia rapido de nao levar uma surra no <PERSON> thai\",\n    \"holder_image\": \"https://tailandiasinplaya.files.wordpress.com/2013/11/peliculas-3774-imagen4.jpg\",\n    \"thumb_image\": \"https://tailandiasinplaya.files.wordpress.com/2013/11/peliculas-3774-imagen4.jpg\",\n    \"is_active\": false,\n    \"status\": \"CREATING\",\n    \"lang\": \"pt-br\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/course", "host": ["{{url}}"], "path": ["api", "v1", "course"]}, "description": "Status accepted:  \n * CREATING\n * REVIEWING\n * FINISHED"}, "response": []}, {"name": "Create Lesson 1", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"lesson_id\", jsonData.id);", "pm.environment.set(\"lesson1_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"Lesson 1\",\n    \"description\": \"My Lesson 1\",\n    \"order\": 1,\n    \"course_id\": \"{{course_id}}\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/lesson", "host": ["{{url}}"], "path": ["api", "v1", "lesson"]}}, "response": []}, {"name": "Create Content 1.1", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"content_id\", jsonData.id);", "pm.environment.set(\"content11_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"Content 1.1\",\n    \"description\": \"Description of content 1\",\n    \"order\": 1,\n    \"dispatch_in\": 1,\n    \"dispatch_period\": \"MORNING\",\n    \"learn_content\": \"{{learn_content}}\",\n    \"lesson_id\": \"{{lesson_id}}\",\n    \"type_id\": \"{{content_type_id}}\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/content", "host": ["{{url}}"], "path": ["api", "v1", "content"]}}, "response": []}, {"name": "Create Content 1.2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"content_id\", jsonData.id);", "pm.environment.set(\"content12_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "x-client", "type": "text", "value": "{{company_id}}"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"Content 1.2\",\n    \"description\": \"Description of content 2\",\n    \"order\": 2,\n    \"dispatch_in\": 1,\n    \"learn_content\": \"{{learn_content}}\",\n    \"lesson_id\": \"{{lesson_id}}\",\n    \"type_id\": \"{{content_type_id2}}\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/content", "host": ["{{url}}"], "path": ["api", "v1", "content"]}}, "response": []}, {"name": "Create Lesson 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"lesson_id\", jsonData.id);", "pm.environment.set(\"lesson1_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"Lesson 2\",\n    \"description\": \"My lesson 2\",\n    \"order\": 2,\n    \"course_id\": \"{{course_id}}\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/lesson", "host": ["{{url}}"], "path": ["api", "v1", "lesson"]}}, "response": []}, {"name": "Create Content 2.1", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"content_id\", jsonData.id);", "pm.environment.set(\"content21_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"Content 2.1\",\n    \"description\": \"Description of content 1\",\n    \"order\": 1,\n    \"dispatch_in\": 2,\n    \"dispatch_period\": \"AFTERNOON\",\n    \"learn_content\": \"{{learn_content}}\",\n    \"lesson_id\": \"{{lesson_id}}\",\n    \"type_id\": \"{{content_type_id3}}\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/content", "host": ["{{url}}"], "path": ["api", "v1", "content"]}}, "response": []}, {"name": "Create Content 2.2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"content_id\", jsonData.id);", "pm.environment.set(\"content22_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"Content 2.2\",\n    \"description\": \"Description of content 1\",\n    \"order\": 2,\n    \"dispatch_in\": 3,\n    \"dispatch_period\": \"AFTERNOON\",\n    \"learn_content\": \"{{learn_content}}\",\n    \"lesson_id\": \"{{lesson_id}}\",\n    \"type_id\": \"{{content_type_id}}\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/content", "host": ["{{url}}"], "path": ["api", "v1", "content"]}}, "response": []}, {"name": "Create Content 2.3", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"content_id\", jsonData.id);", "pm.environment.set(\"content23_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"Content 2.3\",\n    \"description\": \"Description of content 1\",\n    \"order\": 3,\n    \"dispatch_in\": 4,\n    \"dispatch_period\": \"AFTERNOON\",\n    \"learn_content\": \"{{learn_content}}\",\n    \"lesson_id\": \"{{lesson_id}}\",\n    \"type_id\": \"{{content_type_id}}\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/content", "host": ["{{url}}"], "path": ["api", "v1", "content"]}}, "response": []}, {"name": "Create User", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"user_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"<PERSON>\",\n    \"phone\": \"+55 (48) 9169-8418\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/user", "host": ["{{url}}"], "path": ["api", "v1", "user"]}}, "response": []}, {"name": "Create Enrollment", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"enrollment_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"user_id\": \"{{user_id}}\",\n    \"course_id\": \"{{course_id}}\",\n    \"start_date\": \"{{date}}\",\n    \"end_date\": \"{{date}}\",\n    \"timezone\": \"America/Sao_Paulo\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/enrollment", "host": ["{{url}}"], "path": ["api", "v1", "enrollment"]}}, "response": []}, {"name": "Create User Enrollment", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"enrollment_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "x-client", "type": "text", "value": "{{company_id}}"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"<PERSON>essoa\",\n    \"phone\": \"+55 (48) 90169-0000\",\n    \"course_id\": \"{{course_id}}\",\n    \"timezone\": \"America/Sao_Paulo\",\n    \"start_date\": \"2020-04-16\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/enrollment/user", "host": ["{{url}}"], "path": ["api", "v1", "enrollment", "user"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["const moment = require('moment');", "pm.environment.set(\"date\", moment().format(\"YYYY-MM-DD\"));", "pm.environment.set(\"datetime\", (new Date()).toISOString());", "pm.environment.set(\"learn_content\", \"ced6c9f5-2418-41fa-8ab1-671abc14e1db\");", "pm.environment.set(\"course_category_id\", \"7d46e9d8-30cf-42be-99d0-19dd724d2be9\");", "pm.environment.set(\"content_type_id\", \"b7094e27-b263-4fed-a928-6f0a78439cbe\");"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "Activity Simulation", "item": [{"name": "Create Activity", "event": [{"listen": "test", "script": {"exec": ["pm.test('Check status code', () => {", "    pm.expect(pm.response.code).to.be.oneOf([201])", "})"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["const moment = require('moment');", "var seconds = (Math.floor(Math.random()*99)+10)", "var start_datetime = new Date();", "var stop_datetime = moment(start_datetime).add(seconds, 's').toDate();", "pm.environment.set(\"start_datetime\", start_datetime.toISOString());", "pm.environment.set(\"stop_datetime\", stop_datetime.toISOString());", "", "var contents = [\"content11_id\", \"content12_id\", \"content21_id\", \"content22_id\", \"content23_id\"];", "var content_index = Math.floor(Math.random() * contents.length);", "var content = contents[content_index];", "pm.environment.set(\"random_content_id\", pm.environment.get(content));", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "x-client", "type": "text", "value": "{{company_id}}"}], "body": {"mode": "raw", "raw": "{\n\t\"enrollment_id\": \"{{enrollment_id}}\",\n    \"content_id\": \"{{random_content_id}}\",\n    \"action\": \"READ\",\n    \"start_at\": \"{{start_datetime}}\",\n    \"stop_at\": \"{{stop_datetime}}\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/view/activity", "host": ["{{url}}"], "path": ["api", "v1", "view", "activity"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["const moment = require('moment');", "pm.environment.set(\"date\", moment().format(\"YYYY-MM-DD\"));", "pm.environment.set(\"datetime\", (new Date()).toISOString());", "pm.environment.set(\"learn_content\", \"ced6c9f5-2418-41fa-8ab1-671abc14e1db\");", "pm.environment.set(\"course_category_id\", \"7d46e9d8-30cf-42be-99d0-19dd724d2be9\");", "pm.environment.set(\"content_type_id\", \"b7094e27-b263-4fed-a928-6f0a78439cbe\");"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "<PERSON><PERSON>", "item": [{"name": "Login - PROD", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "    ", "    var jsonData = pm.response.json();", "    pm.environment.set(\"access_token\", jsonData.access_token);", "    pm.environment.set(\"refresh_token\", jsonData.refresh_token);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "username", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "123456", "type": "text"}]}, "url": {"raw": "https://myaccount-api.keepsdev.com/auth", "protocol": "https", "host": ["myaccount-api", "keepsdev", "com"], "path": ["auth"]}}, "response": []}, {"name": "Login - Stage", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "    ", "    var jsonData = pm.response.json();", "    pm.environment.set(\"access_token\", jsonData.access_token);", "    pm.environment.set(\"refresh_token\", jsonData.refresh_token);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "username", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "123456", "type": "text"}]}, "url": {"raw": "https://learning-platform-api-stage.keepsdev.com/myaccount/auth", "protocol": "https", "host": ["myaccount-api-stage", "keepsdev", "com"], "path": ["auth"]}}, "response": []}, {"name": "Create MyAcc User", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"user_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "x-client", "type": "text", "value": "{{company_id}}"}], "body": {"mode": "raw", "raw": "{\n\t\"id\": \"bbf47825-8dfb-49bc-8ad8-f8adc775f95f\",\n\t\"name\": \"Keeps Demonstração\",\n    \"phone\": \"*************\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/user", "host": ["{{url}}"], "path": ["api", "v1", "user"]}}, "response": []}, {"name": "Health Check", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "x-client", "type": "text", "value": "{{company_id}}"}], "body": {"mode": "raw", "raw": "{\n\t\"id\": \"bbf47825-8dfb-49bc-8ad8-f8adc775f95f\",\n\t\"name\": \"Keeps Demonstração\",\n    \"phone\": \"*************\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/healthcheck", "host": ["{{url}}"], "path": ["healthcheck"]}}, "response": []}, {"name": "User Info", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "{{access_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "https://learning-platform-api-stage.keepsdev.com/myaccount/users/info", "protocol": "https", "host": ["myaccount-api-stage", "keepsdev", "com"], "path": ["", "users", "info"]}}, "response": []}, {"name": "Company", "event": [{"listen": "test", "script": {"exec": ["", ""], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "{{access_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "https://learning-platform-api-stage.keepsdev.com/myaccount/companies", "protocol": "https", "host": ["myaccount-api-stage", "keepsdev", "com"], "path": ["companies"]}}, "response": []}, {"name": "KeyCloak Cert", "request": {"method": "GET", "header": [], "url": {"raw": "https://iam.keepsdev.com/auth/realms/keeps-dev/", "protocol": "https", "host": ["iam", "keepsdev", "com"], "path": ["auth", "realms", "keeps-dev", ""]}}, "response": []}, {"name": "KeyCloak Well Known", "request": {"method": "GET", "header": [], "url": {"raw": "https://iam.keepsdev.com/auth/realms/keeps-dev/.well-known/openid-configuration", "protocol": "https", "host": ["iam", "keepsdev", "com"], "path": ["auth", "realms", "keeps-dev", ".well-known", "openid-configuration"]}}, "response": []}, {"name": "Load Learn <PERSON>", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "637a2f9e72daba2ebb03a699c7a4c08d", "type": "text"}], "url": {"raw": "https://learning-platform-api-stage.keepsdev.com/kontent/learn-content/5db61f5e-61cf-43f0-86bf-a17e77fb60ed", "protocol": "https", "host": ["kontent-api-stage", "keepsdev", "com"], "path": ["learn-content", "5db61f5e-61cf-43f0-86bf-a17e77fb60ed"]}}, "response": []}, {"name": "Load Learn Kontent Exam", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "type": "text", "value": "637a2f9e72daba2ebb03a699c7a4c08d"}], "url": {"raw": "https://learning-platform-api-stage.keepsdev.com/kontent/assessments/exams/5db61f5e-61cf-43f0-86bf-a17e77fb60ed", "protocol": "https", "host": ["kontent-api-stage", "keepsdev", "com"], "path": ["assessments", "exams", "5db61f5e-61cf-43f0-86bf-a17e77fb60ed"]}}, "response": []}, {"name": "Load whats responses", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "type": "text", "value": "Basic a2VlcHNkZXY6S2VlcHMwMTEwMDFA"}], "url": {"raw": "https://api.smsfire.com.br/v1/whatsapp/inbox/new", "protocol": "https", "host": ["api", "smsfire", "com", "br"], "path": ["v1", "whatsapp", "inbox", "new"]}}, "response": []}]}, {"name": "Course Category", "item": [{"name": "Read", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/course-category/{{course_category_id}}", "host": ["{{url}}"], "path": ["api", "v1", "course-category", "{{course_category_id}}"]}}, "response": []}, {"name": "Read All", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/course-category?name__ilike=desi", "host": ["{{url}}"], "path": ["api", "v1", "course-category"], "query": [{"key": "name__ilike", "value": "desi"}]}}, "response": []}]}, {"name": "Course", "item": [{"name": "Create", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"course_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"category_id\": \"{{course_category_id}}\",\n    \"name\": \"Como nao apanhar no Muay thai para pessoas bem mais sensiveis que voce. \",\n    \"description\": \"Mussum Ipsum, cacilds vidis litro abertis. Manduma pindureta quium dia nois paga. Praesent malesuada urna nisi, quis volutpat erat hendrerit non. Nam vulputate dapibus.\",\n    \"holder_image\": \"http://www.imagens.com.br/imagem-legal\",\n    \"thumb_image\": \"http://www.imagens.com.br/imagem-legal\",\n    \"is_active\": false,\n    \"status\": \"CREATING\",\n    \"lang\": \"pt-br\",\n    \"content_performance_weight\": 10\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/course", "host": ["{{url}}"], "path": ["api", "v1", "course"]}, "description": "Status accepted:  \n * CREATING\n * REVIEWING\n * FINISHED"}, "response": []}, {"name": "Update", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"quiz_performance_weight\": 6\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/course/{{course_id}}", "host": ["{{url}}"], "path": ["api", "v1", "course", "{{course_id}}"]}}, "response": []}, {"name": "Read", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-Client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/course/{{course_id}}", "host": ["{{url}}"], "path": ["api", "v1", "course", "{{course_id}}"]}}, "response": []}, {"name": "Delete", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/course/{{course_id}}", "host": ["{{url}}"], "path": ["api", "v1", "course", "{{course_id}}"]}}, "response": []}, {"name": "Publish", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "X-Client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/course/{{course_id}}/publish", "host": ["{{url}}"], "path": ["api", "v1", "course", "{{course_id}}", "publish"]}}, "response": []}, {"name": "Read All", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-Client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/course", "host": ["{{url}}"], "path": ["api", "v1", "course"], "query": [{"key": "name__like", "value": "<PERSON><PERSON>", "disabled": true}, {"key": "sort", "value": "updated", "disabled": true}, {"key": "page", "value": "1", "disabled": true}, {"key": "per_page", "value": "9", "disabled": true}, {"key": "points__gte", "value": "50", "disabled": true}, {"key": "category__like", "value": "design", "disabled": true}, {"key": "status", "value": "FINISHED", "disabled": true}, {"key": "is_active", "value": "True", "disabled": true}, {"key": "category__name__like", "value": "Tech", "disabled": true}]}}, "response": []}, {"name": "Read Course Enrollments", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-Client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/course/{{course_id}}/enrollment?id__like=9b9daf38-a0ac-4d17-85be-1e8ec3039da3", "host": ["{{url}}"], "path": ["api", "v1", "course", "{{course_id}}", "enrollment"], "query": [{"key": "id__like", "value": "9b9daf38-a0ac-4d17-85be-1e8ec3039da3"}]}}, "response": []}, {"name": "Read Course Lessons", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-Client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/course/{{course_id}}/lesson?page=1", "host": ["{{url}}"], "path": ["api", "v1", "course", "{{course_id}}", "lesson"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "1", "disabled": true}, {"key": "name__ilike", "value": "lesson 2", "disabled": true}, {"key": "sort", "value": "-order", "disabled": true}]}}, "response": []}, {"name": "Read Course Languages Available", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-Client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/course/language", "host": ["{{url}}"], "path": ["api", "v1", "course", "language"], "query": [{"key": "page", "value": "1", "disabled": true}, {"key": "per_page", "value": "1", "disabled": true}]}}, "response": []}]}, {"name": "Course Company", "item": [{"name": "Create", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"course_company_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"company_id\": \"{{company_id}}\",\n    \"course_id\": \"{{course_id}}\"\n}"}, "url": {"raw": "{{url}}/api/v1/course-company", "host": ["{{url}}"], "path": ["api", "v1", "course-company"]}}, "response": []}, {"name": "Update", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"company_id\": \"{{company_id}}\",\n    \"course_id\": \"{{course_id}}\"\n}"}, "url": {"raw": "{{url}}/api/v1/course-company/{{course_company_id}}", "host": ["{{url}}"], "path": ["api", "v1", "course-company", "{{course_company_id}}"]}}, "response": []}, {"name": "Read", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/course-company/{{course_company_id}}", "host": ["{{url}}"], "path": ["api", "v1", "course-company", "{{course_company_id}}"]}}, "response": []}, {"name": "Delete", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/course-company/{{course_company_id}}", "host": ["{{url}}"], "path": ["api", "v1", "course-company", "{{course_company_id}}"]}}, "response": []}]}, {"name": "Company", "item": [{"name": "Create", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "", "var jsonData = pm.response.json();", "pm.environment.set(\"my_company_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"id\": \"123123456456456\",\n    \"name\": \"My Company Uhull\",\n    \"billing_cycle_day\": 31,\n    \"private_channel\": \"ValorUm:ValorDoji@+55489912398\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/company", "host": ["{{url}}"], "path": ["api", "v1", "company"]}}, "response": []}, {"name": "Update", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"logo_url\": \"https://keeps.com.br/image.jpg\"\n}"}, "url": {"raw": "{{url}}/api/v1/company/{{my_company_id}}", "host": ["{{url}}"], "path": ["api", "v1", "company", "{{my_company_id}}"]}}, "response": []}, {"name": "Read", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/company/{{my_company_id}}", "host": ["{{url}}"], "path": ["api", "v1", "company", "{{my_company_id}}"]}}, "response": []}, {"name": "Delete", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/company/{{my_company_id}}", "host": ["{{url}}"], "path": ["api", "v1", "company", "{{my_company_id}}"]}}, "response": []}, {"name": "Billing", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/company/{{my_company_id}}/billing", "host": ["{{url}}"], "path": ["api", "v1", "company", "{{my_company_id}}", "billing"]}}, "response": []}, {"name": "Billing Charges", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/company/{{my_company_id}}/billing/charges", "host": ["{{url}}"], "path": ["api", "v1", "company", "{{my_company_id}}", "billing", "charges"]}}, "response": []}]}, {"name": "Lesson", "item": [{"name": "Create", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"lesson_id\", jsonData.id);", "pm.environment.set(\"lesson1_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"Lesson 10\",\n    \"description\": \"ABCDEFGHIJKLM\",\n    \"order\": 10,\n    \"course_id\": \"{{course_id}}\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/lesson", "host": ["{{url}}"], "path": ["api", "v1", "lesson"]}}, "response": []}, {"name": "Update", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"Lesson 1\",\n    \"description\": \"Levou um chutinho na cabeca e ficou boladao\",\n    \"order\": 1,\n    \"course_id\": \"{{course_id}}\"\n}"}, "url": {"raw": "{{url}}/api/v1/lesson/{{lesson_id}}", "host": ["{{url}}"], "path": ["api", "v1", "lesson", "{{lesson_id}}"]}}, "response": []}, {"name": "Read", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Load after update\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.id).to.eql(pm.variables.get(\"course_category_id\"));", "    pm.expect(jsonData.name).to.eql(\"Categoria Malzinha\");", "    pm.expect(jsonData.description).to.eql(\"Minha descricao eh muito chata\");", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/lesson/{{lesson_id}}", "host": ["{{url}}"], "path": ["api", "v1", "lesson", "{{lesson_id}}"]}}, "response": []}, {"name": "Delete", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/lesson/{{lesson_id}}", "host": ["{{url}}"], "path": ["api", "v1", "lesson", "{{lesson_id}}"]}}, "response": []}, {"name": "Read Lesson Contents", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/lesson/{{lesson_id}}/content", "host": ["{{url}}"], "path": ["api", "v1", "lesson", "{{lesson_id}}", "content"], "query": [{"key": "page", "value": "2", "disabled": true}, {"key": "per_page", "value": "2", "disabled": true}, {"key": "name__like", "value": "2.2", "disabled": true}]}}, "response": []}]}, {"name": "Content Type", "item": [{"name": "Read", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/content-type/{{content_type_id}}", "host": ["{{url}}"], "path": ["api", "v1", "content-type", "{{content_type_id}}"]}}, "response": []}, {"name": "Read All", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/content-type", "host": ["{{url}}"], "path": ["api", "v1", "content-type"]}}, "response": []}]}, {"name": "Content", "item": [{"name": "Create", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"content_id\", jsonData.id);", "pm.environment.set(\"content1_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"<PERSON><PERSON><PERSON>\",\n    \"description\": \"OI OI OI\",\n    \"order\": 1,\n    \"dispatch_in\": 3,\n    \"learn_content\": \"12312-fwr33f-12f12f13f41-f1\",\n    \"lesson_id\": \"{{lesson_id}}\",\n    \"type_id\": \"{{content_type_id}}\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/content", "host": ["{{url}}"], "path": ["api", "v1", "content"]}}, "response": []}, {"name": "Update", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"Content 1\",\n    \"description\": \"Description of the super content 1\",\n    \"order\": 1,\n    \"dispatch_in\": 3,\n    \"learn_content\": \"12312-fwr33f-12f12f13f41-f1\",\n    \"lesson_id\": \"{{lesson_id}}\",\n    \"type_id\": \"{{content_type_id}}\"\n}\n"}, "url": {"raw": "{{url}}/api/v1/content/{{content_id}}", "host": ["{{url}}"], "path": ["api", "v1", "content", "{{content_id}}"]}}, "response": []}, {"name": "Read", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/content/{{content_id}}", "host": ["{{url}}"], "path": ["api", "v1", "content", "{{content_id}}"]}}, "response": []}, {"name": "Delete", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/content/{{content_id}}", "host": ["{{url}}"], "path": ["api", "v1", "content", "{{content_id}}"]}}, "response": []}, {"name": "Read Content Activities", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/content/{{content_id}}/activity", "host": ["{{url}}"], "path": ["api", "v1", "content", "{{content_id}}", "activity"], "query": [{"key": "page", "value": "1", "disabled": true}, {"key": "per_page", "value": "1", "disabled": true}]}}, "response": []}, {"name": "Image Upload", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "x-client", "type": "text", "value": "{{company_id}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/Users/<USER>/Downloads/IMG_0787.PNG"}]}, "url": {"raw": "{{url}}/api/v1/upload/image", "host": ["{{url}}"], "path": ["api", "v1", "upload", "image"]}}, "response": []}, {"name": "Read URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/content/{{content_id}}/enrollment/{{enrollment_id}}/renew-access", "host": ["{{url}}"], "path": ["api", "v1", "content", "{{content_id}}", "enrollment", "{{enrollment_id}}", "renew-access"]}}, "response": []}, {"name": "Read Re-Schedule", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["// python script", "// @schedule_page.route('/api/v1/enrollment/<enrollment_id>/accept', methods=['POST'])", "// def enrollment_accept(enrollment_id, enrollment_service: service.EnrollmentService):", "//     enrollment = enrollment_service.load(enrollment_id)", "//     enrollment_service.accept_disclaimer(enrollment)", "//     return 'Done', 204", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "x-client", "type": "text", "value": "{{company_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"send_date\": \"2020-07-03\",\n    \"period\": \"NIGHT\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/content/b522ae1f-4abe-4214-90a0-f60b3c89b437/re-schedule", "host": ["{{url}}"], "path": ["api", "v1", "content", "b522ae1f-4abe-4214-90a0-f60b3c89b437", "re-schedule"]}}, "response": []}]}, {"name": "User", "item": [{"name": "Create", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"user_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"Pessoa Legal\",\n    \"phone\": \"{{phone_number}}\",\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/user", "host": ["{{url}}"], "path": ["api", "v1", "user"]}}, "response": []}, {"name": "Import XLSX", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "x-client", "type": "text", "value": "{{company_id}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/Users/<USER>/Projects/keeps/keeps-smartzap-server/tests/assets/users.xlsx"}]}, "url": {"raw": "{{url}}/api/v1/user/import-xlsx", "host": ["{{url}}"], "path": ["api", "v1", "user", "import-xlsx"]}}, "response": []}, {"name": "Update", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"<PERSON>   \"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/user/{{user_id}}", "host": ["{{url}}"], "path": ["api", "v1", "user", "{{user_id}}"]}}, "response": []}, {"name": "Read", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/user/{{user_id}}", "host": ["{{url}}"], "path": ["api", "v1", "user", "{{user_id}}"]}}, "response": []}, {"name": "Read All", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/user?per_page=20&page=1", "host": ["{{url}}"], "path": ["api", "v1", "user"], "query": [{"key": "name__like", "value": "leo", "disabled": true}, {"key": "per_page", "value": "20"}, {"key": "page", "value": "1"}]}}, "response": []}, {"name": "Read User Token", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/enrollment/{{enrollment_id}}/token", "host": ["{{url}}"], "path": ["api", "v1", "enrollment", "{{enrollment_id}}", "token"]}}, "response": []}, {"name": "Read Notification", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/user/notification/{{notification_id}}", "host": ["{{url}}"], "path": ["api", "v1", "user", "notification", "{{notification_id}}"]}}, "response": []}, {"name": "Read All Notifications", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/user/notification?read__eq=false&user_id__eq=coisa&company_id__ne=coco", "host": ["{{url}}"], "path": ["api", "v1", "user", "notification"], "query": [{"key": "read__eq", "value": "false"}, {"key": "user_id__eq", "value": "coisa"}, {"key": "company_id__ne", "value": "coco"}]}}, "response": []}, {"name": "Update Notification", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "x-client", "type": "text", "value": "{{company_id}}"}], "body": {"mode": "raw", "raw": "{\n\t\"read\": \"true\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/user/notification/{{notification_id}}", "host": ["{{url}}"], "path": ["api", "v1", "user", "notification", "{{notification_id}}"]}}, "response": []}, {"name": "Delete", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/user/{{user_id}}", "host": ["{{url}}"], "path": ["api", "v1", "user", "{{user_id}}"]}}, "response": []}]}, {"name": "Enrollment", "item": [{"name": "Create", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"enrollment_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"user_id\": \"{{user_id}}\",\n    \"course_id\": \"{{course_id}}\",\n    \"start_date\": \"{{date}}\",\n    \"end_date\": \"{{date}}\",\n    \"timezone\": \"America/Bogota\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/enrollment", "host": ["{{url}}"], "path": ["api", "v1", "enrollment"]}}, "response": []}, {"name": "Create Batch", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"enrollment_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "x-client", "type": "text", "value": "{{company_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"course_id\": \"123\",\n    \"start_date\": \"{{date}}\",\n    \"end_date\": \"{{date}}\",\n    \"timezone\": \"America/Bogota\",\n    \"users\": [\"1d1a5dc5-c403-47a8-9bdd-ac7c436c4172\", \"7762c56a-84bf-4498-8859-32c32ec1ae9a\", \"aad4c945-cf53-4027-b7da-ad0167ad01f6\"]\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/enrollment/batch", "host": ["{{url}}"], "path": ["api", "v1", "enrollment", "batch"]}}, "response": []}, {"name": "Create File", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "x-client", "type": "text", "value": "{{company_id}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/Users/<USER>/Downloads/File - Smartzap Import.xlsx"}, {"key": "course_id", "value": "{{course_id}}", "type": "text"}, {"key": "start_date", "value": "{{date}}", "type": "text"}, {"key": "end_date", "value": "{{date}}", "type": "text"}, {"key": "timezone", "value": "America/Sao_Paulo", "type": "text"}]}, "url": {"raw": "{{url}}/api/v1/enrollment/file", "host": ["{{url}}"], "path": ["api", "v1", "enrollment", "file"]}}, "response": []}, {"name": "Create User", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"enrollment_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "x-client", "type": "text", "value": "{{company_id}}"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"Pessoa Legal\",\n    \"phone\": \"{{phone_number}}\",\n    \"email\": \"<EMAIL>\",\n    \"course_id\": \"{{course_id}}\",\n    \"timezone\": \"America/Sao_Paulo\",\n    \"start_date\": \"{{date}}\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/enrollment/user", "host": ["{{url}}"], "path": ["api", "v1", "enrollment", "user"]}}, "response": []}, {"name": "Update", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"user_id\": \"123123f-sdf32f-2fwefw\",\n    \"course_id\": \"{{course_id}}\",\n    \"points\": 66,\n    \"performance\": 44\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/enrollment/{{enrollment_id}}", "host": ["{{url}}"], "path": ["api", "v1", "enrollment", "{{enrollment_id}}"]}}, "response": []}, {"name": "Read", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/enrollment/{{enrollment_id}}", "host": ["{{url}}"], "path": ["api", "v1", "enrollment", "{{enrollment_id}}"]}}, "response": []}, {"name": "Read All", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/enrollment", "host": ["{{url}}"], "path": ["api", "v1", "enrollment"], "query": [{"key": "status__eq", "value": "COMPLETED", "disabled": true}, {"key": "per_page", "value": "10", "disabled": true}, {"key": "page", "value": "1", "disabled": true}, {"key": "sort", "value": "-user__name", "disabled": true}, {"key": "current_content_id", "value": "8f050fc6-8382-4fa2-a069-af37668dbf0a", "disabled": true}, {"key": "current_content__name__eq", "value": "Quiz 03", "disabled": true}, {"key": "user__tags__like", "value": "abacate", "disabled": true}]}}, "response": []}, {"name": "Delete", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/enrollment/{{enrollment_id}}", "host": ["{{url}}"], "path": ["api", "v1", "enrollment", "{{enrollment_id}}"]}}, "response": []}, {"name": "Delete in Batch", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"ids\": [\n        \"a61ea9c6-05b2-4585-958a-62723f644bad\", \"2713f787-f637-4391-9736-d0c494c2c199\"\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/enrollment/batch", "host": ["{{url}}"], "path": ["api", "v1", "enrollment", "batch"]}}, "response": []}, {"name": "Accept Disclaimer", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "x-client", "type": "text", "value": "{{company_id}}"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/enrollment/{{enrollment_id}}/accept", "host": ["{{url}}"], "path": ["api", "v1", "enrollment", "{{enrollment_id}}", "accept"]}}, "response": []}, {"name": "Read Tracking", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/enrollment/{{enrollment_id}}/tracking", "host": ["{{url}}"], "path": ["api", "v1", "enrollment", "{{enrollment_id}}", "tracking"]}}, "response": []}]}, {"name": "Activity", "item": [{"name": "Create", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"activity_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJzbWFydHphcCIsInN1YiI6IjY3ZDgwMWFjLWZjYTMtNGQzOS04ZGE1LTM4YzlkOTNhNGMzMiIsImVucm9sbG1lbnRfaWQiOiJkZmZkOWY1Zi03NzFlLTQ1YjItYjdkNC03Yjk3MDkxNjE3ZWMiLCJleHAiOjE1OTc1Nzk4MDcsImxhbmciOiJwdC1iciIsIngtY2xpZW50IjoiZTc2YjUwODItZjRmZS00ZjQxLWJlNzktMTk3Nzg0MGUxNmE4In0.FKUrJHFZ3T4MZ5z3cwfkNyZs07XWIruItAcp-UhBNS8", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"content_id\": \"686549a1-1d9c-4ccc-9353-44cbafad622f\",\n    \"action\": \"WATCH\",\n    \"start_at\": \"{{datetime}}\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/v1/view/activity", "host": ["{{url}}"], "path": ["api", "v1", "view", "activity"]}, "description": "Actions accepted:\n * WATCH\n * LISTEN\n * READ\n * VIEW"}, "response": []}, {"name": "Update", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"stop_at\": \"2020-02-15T13:35:22\"\n}\n"}, "url": {"raw": "{{url}}/api/v1/view/activity/{{activity_id}}", "host": ["{{url}}"], "path": ["api", "v1", "view", "activity", "{{activity_id}}"]}}, "response": []}, {"name": "Read", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/view/activity/{{activity_id}}", "host": ["{{url}}"], "path": ["api", "v1", "view", "activity", "{{activity_id}}"]}}, "response": []}, {"name": "Delete", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/activity/{{activity_id}}", "host": ["{{url}}"], "path": ["api", "v1", "activity", "{{activity_id}}"]}}, "response": []}]}, {"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "Create", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "var jsonData = pm.response.json();", "pm.environment.set(\"feedback_id\", jsonData.id);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"company_id\": \"{{company_id}}\",\n    \"tag\": \"Tag Tag Tag\",\n    \"message\": \"<PERSON><PERSON> bom esse curso\",\n    \"scope\": \"Legal\"\n}\n"}, "url": {"raw": "{{url}}/api/v1/feedback", "host": ["{{url}}"], "path": ["api", "v1", "feedback"]}}, "response": []}, {"name": "Update", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"company_id\": \"{{company_id}}\",\n    \"tag\": \"Tag2 Tag2 Tag2\",\n    \"message\": \"Muito2 bom2 esse2 curso2\",\n    \"scope\": \"Legal2\"\n}\n"}, "url": {"raw": "{{url}}/api/v1/feedback/{{feedback_id}}", "host": ["{{url}}"], "path": ["api", "v1", "feedback", "{{feedback_id}}"]}}, "response": []}, {"name": "Read", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/feedback/{{feedback_id}}", "host": ["{{url}}"], "path": ["api", "v1", "feedback", "{{feedback_id}}"]}}, "response": []}, {"name": "Delete", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "x-client", "value": "{{company_id}}", "type": "text"}], "url": {"raw": "{{url}}/api/v1/feedback/{{feedback_id}}", "host": ["{{url}}"], "path": ["api", "v1", "feedback", "{{feedback_id}}"]}}, "response": []}]}, {"name": "View", "item": [{"name": "Read View Content", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Load after update\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.id).to.eql(pm.variables.get(\"course_category_id\"));", "    pm.expect(jsonData.name).to.eql(\"Categoria Malzinha\");", "    pm.expect(jsonData.description).to.eql(\"Minha descricao eh muito chata\");", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJzbWFydHphcCIsInN1YiI6ImFiMWU2ZTQ5LWE1NzYtNDU4Yi05NDZhLWJjNWJhZTM3NDg0MCIsImVucm9sbG1lbnRfaWQiOiI5Zjc0ZWE1MS1kYzY4LTQzZWItOWQ4Yi05OTc1ZGQyY2FmZmYiLCJleHAiOjE1OTc1MTI1ODYsImxhbmciOiIiLCJ4LWNsaWVudCI6ImU3NmI1MDgyLWY0ZmUtNGY0MS1iZTc5LTE5Nzc4NDBlMTZhOCJ9.2Y5-VknmqQGCS1-X5wP47PlKp2BvJEhgD49HrxiHXFk", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/view/content/{{content_id}}", "host": ["{{url}}"], "path": ["api", "v1", "view", "content", "{{content_id}}"]}}, "response": []}]}, {"name": "Schedule", "item": [{"name": "Schedule Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "SmsStatus", "value": "sent", "type": "text"}]}, "url": {"raw": "{{url}}/api/v1/schedule/20b6ab56-9155-4750-94faecc451dddd4a/callback/twilio?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJzbWFydHphcCIsInN1YiI6IjIxMzNiMWYwLWRhMzAtNDdkYy1hMzdmLWI0ZjRkZDkxOWI4ZCIsImVucm9sbG1lbnRfaWQiOiJlMDhjMWZjYS1iNGZjLTRlMGYtYTBiMi02NTAxMWNlMjNiZGYiLCJleHAiOjE2MDE4MzU1ODYsImxhbmciOiIiLCJ4LWNsaWVudCI6ImU3NmI1MDgyLWY0ZmUtNGY0MS1iZTc5LTE5Nzc4NDBlMTZhOCJ9.zDiDupm0NSZUg9csgoSBiTF7wlLOL-o3vxKk9-k8iW4", "host": ["{{url}}"], "path": ["api", "v1", "schedule", "20b6ab56-9155-4750-94faecc451dddd4a", "callback", "twi<PERSON>"], "query": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJzbWFydHphcCIsInN1YiI6IjIxMzNiMWYwLWRhMzAtNDdkYy1hMzdmLWI0ZjRkZDkxOWI4ZCIsImVucm9sbG1lbnRfaWQiOiJlMDhjMWZjYS1iNGZjLTRlMGYtYTBiMi02NTAxMWNlMjNiZGYiLCJleHAiOjE2MDE4MzU1ODYsImxhbmciOiIiLCJ4LWNsaWVudCI6ImU3NmI1MDgyLWY0ZmUtNGY0MS1iZTc5LTE5Nzc4NDBlMTZhOCJ9.zDiDupm0NSZUg9csgoSBiTF7wlLOL-o3vxKk9-k8iW4"}]}}, "response": []}, {"name": "Schedule", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "x-client", "value": "{{company_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"id\": 68671,\n  \"customId\": 11,\n  \"to\": ************,\n  \"date\": \"2020-02-17 09:47:34\",\n  \"pushname\": \"<PERSON>\",\n  \"photo\": null,\n  \"status\": {\n    \"code\": 2,\n    \"name\": \"DELIVERED\"\n  },\n  \"sub_status\": {\n    \"code\": 1,\n    \"name\": \"OK\"\n  },\n  \"description\": \"MESSAGE DELIVERED TO WHATSAPP\"\n}"}, "url": {"raw": "{{url}}/api/v1/schedule/744541cb-68b4-477b-acc4-a2fd58f57cdf", "host": ["{{url}}"], "path": ["api", "v1", "schedule", "744541cb-68b4-477b-acc4-a2fd58f57cdf"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "SmsMessageSid", "value": "SMf5457dc6cc804a1b9e14f6f17bc49481", "type": "text", "disabled": true}, {"key": "SmsStatus", "value": "received", "type": "text"}, {"key": "SmsSid", "value": "SMf5457dc6cc804a1b9e14f6f17bc49481", "type": "text", "disabled": true}, {"key": "To", "value": "whatsapp:+***********", "type": "text", "disabled": true}, {"key": "MessageSid", "value": "Mf5457dc6cc804a1b9e14f6f17bc49481", "type": "text", "disabled": true}, {"key": "AccountSid", "value": "ACb530962b6a3fdea6990e63792eae0a80", "type": "text", "disabled": true}, {"key": "From", "value": "whatsapp:+************", "type": "text"}, {"key": "ApiVersion", "value": "2010-04-01", "type": "text", "disabled": true}, {"key": "Body", "value": "finalizar", "type": "text"}]}, "url": {"raw": "{{url}}/api/v1/schedule/twilio/responses", "host": ["{{url}}"], "path": ["api", "v1", "schedule", "twi<PERSON>", "responses"], "query": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************.JYUNfO13VDvlkxeZFqX_ref6F5BN91i5F7bJ7N7mA-s", "disabled": true}]}}, "response": []}]}, {"name": "CSV", "item": [{"name": "Read Users", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/stats/user/csv", "host": ["{{url}}"], "path": ["api", "v1", "stats", "user", "csv"], "query": [{"key": "name__ilike", "value": "leo", "disabled": true}]}}, "response": []}, {"name": "Read Course Enrollments In Progress", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-Client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/stats/course/{{course_id}}/enrollment/in-progress/csv", "host": ["{{url}}"], "path": ["api", "v1", "stats", "course", "{{course_id}}", "enrollment", "in-progress", "csv"], "query": [{"key": "user__name__like", "value": "<PERSON>", "disabled": true}, {"key": "status", "value": "STARTED", "disabled": true}]}}, "response": []}, {"name": "Read Course Enrollments Completed", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-Client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/stats/course/{{course_id}}/enrollment/completed/csv", "host": ["{{url}}"], "path": ["api", "v1", "stats", "course", "{{course_id}}", "enrollment", "completed", "csv"]}}, "response": []}, {"name": "Read Course Activities", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-Client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/stats/course/{{course_id}}/activity/csv", "host": ["{{url}}"], "path": ["api", "v1", "stats", "course", "{{course_id}}", "activity", "csv"], "query": [{"key": "action", "value": "WATCH", "disabled": true}]}}, "response": []}, {"name": "Read Course Quizzes", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-Client", "type": "text", "value": "{{company_id}}"}], "url": {"raw": "{{url}}/api/v1/stats/course/{{course_id}}/quizzes/csv", "host": ["{{url}}"], "path": ["api", "v1", "stats", "course", "{{course_id}}", "quizzes", "csv"], "query": [{"key": "status__ne", "value": "COMPLETED", "disabled": true}]}}, "response": []}]}, {"name": "Site Error", "item": [{"name": "Login - PROD", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "    ", "    var jsonData = pm.response.json();", "    pm.environment.set(\"access_token_prod\", jsonData.access_token);", "    pm.environment.set(\"refresh_token_prod\", jsonData.refresh_token);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "username", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "123456", "type": "text"}]}, "url": {"raw": "https://myaccount-api.keepsdev.com/auth", "protocol": "https", "host": ["myaccount-api", "keepsdev", "com"], "path": ["auth"]}}, "response": []}, {"name": "Courses - Keeps Casa L", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token_prod}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "1747ab76-2232-4fdf-948c-340d94172329", "type": "text"}], "url": {"raw": "https://smartzap-api.keepsdev.com/api/v1/course?page=1&per_page=9&sort=-created", "protocol": "https", "host": ["smartzap-api", "keepsdev", "com"], "path": ["api", "v1", "course"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "9"}, {"key": "sort", "value": "-created"}]}}, "response": []}, {"name": "Courses - Keeps Ford", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token_prod}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "type": "text", "value": "e919f759-c99d-4c37-9c17-33df3d3fae4f"}], "url": {"raw": "https://smartzap-api.keepsdev.com/api/v1/course?page=1&per_page=9&sort=-created", "protocol": "https", "host": ["smartzap-api", "keepsdev", "com"], "path": ["api", "v1", "course"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "9"}, {"key": "sort", "value": "-created"}]}}, "response": []}, {"name": "Courses - Keeps Keeps", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Origin", "value": "https://smartzap.keepsdev.com", "type": "text"}, {"key": "Authorization", "value": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJQVjBNNkU5NDIyX2g1bm9XV3NvUWotaUk1R19vOTRBdUgwSjVIaXF5R28wIn0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0ZUZGoLZQpaQ-QQiCqisrne50Kv5tGoTXUozId6Q4jPl1mtDXNZ79N5QZ6Xwi654aSB-y2WJnR2JmLq5bZcLwwh_4nC4lbuGGvULoXnE2W-UgIpR2O3kYtV0s95KaT0P2lc1KGNKEea-YOQPgN9y5Q1p-HE7_Iy_ienGG6SefRL7DCrbrat40aW9qW--8Q0rI0tLTlfplc5nkBfKdxVF9X7PLTeiqwOdyKFbVYkJdyJ78aLvcLWbhj6pB4ZF2SNHv-f5K0CIYpFbZE2iWRaTiLz23IvnjspFfbIb74boiCEE5OxLvJyLUgE5CgZs3QB2Atjc5Uz0_BxWQf3ywsseGQ", "type": "text"}, {"key": "<PERSON><PERSON><PERSON>", "value": "https://smartzap.keepsdev.com/courses", "type": "text"}, {"key": "Host", "value": "smartzap-api.keepsdev.com", "type": "text"}, {"key": "Accept-Language", "value": "en-us", "type": "text"}, {"key": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_5) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.1 Safari/605.1.15:", "type": "text"}, {"key": "Accept-Encoding", "value": "gzip, deflate, br", "type": "text"}, {"key": "Connection", "value": "keep-alive", "type": "text"}, {"key": "x-client", "value": "e76b5082-f4fe-4f41-be79-1977840e16a8", "type": "text"}], "url": {"raw": "https://smartzap-api.keepsdev.com/api/v1/course?page=1&per_page=9&sort=-created", "protocol": "https", "host": ["smartzap-api", "keepsdev", "com"], "path": ["api", "v1", "course"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "9"}, {"key": "sort", "value": "-created"}]}}, "response": []}, {"name": "Courses - Keeps Client Id Local", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token_prod}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "type": "text", "value": "e919f759-c99d-4c37-9c17-33df3d3fae4f"}], "url": {"raw": "http://127.0.0.1:5000/api/v1/course?page=1&per_page=9&sort=-created", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "5000", "path": ["api", "v1", "course"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "9"}, {"key": "sort", "value": "-created"}]}}, "response": []}, {"name": "Course by ID - Keeps Ford", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token_prod}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "type": "text", "value": "e76b5082-f4fe-4f41-be79-1977840e16a8"}], "url": {"raw": "https://smartzap-api.keepsdev.com/api/v1/course/2c0e6f86-c31e-416b-8952-ac113fb6ede4", "protocol": "https", "host": ["smartzap-api", "keepsdev", "com"], "path": ["api", "v1", "course", "2c0e6f86-c31e-416b-8952-ac113fb6ede4"]}}, "response": []}, {"name": "Course by ID - Keeps Ford Local", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token_prod}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "type": "text", "value": "e76b5082-f4fe-4f41-be79-1977840e16a8"}], "url": {"raw": "http://127.0.0.1:5000/api/v1/course/2c0e6f86-c31e-416b-8952-ac113fb6ede4", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "5000", "path": ["api", "v1", "course", "2c0e6f86-c31e-416b-8952-ac113fb6ede4"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["const moment = require('moment');", "pm.environment.set(\"date\", moment().format(\"YYYY-MM-DD\"));", "pm.environment.set(\"datetime\", (new Date()).toISOString());", "pm.environment.set(\"learn_content\", \"ced6c9f5-2418-41fa-8ab1-671abc14e1db\");", "pm.environment.set(\"course_category_id\", \"7d46e9d8-30cf-42be-99d0-19dd724d2be9\");", "pm.environment.set(\"content_type_id\", \"b7094e27-b263-4fed-a928-6f0a78439cbe\");", "pm.environment.set(\"content_type_id2\", \"0faac34b-2393-4352-8a94-a9ee0659f824\");", "pm.environment.set(\"content_type_id3\", \"2284bfce-fdfc-4477-9143-39c380cc653c\");", "", "", "var phone_number = \"+55 48 99\" + (Math<PERSON>floor(Math.random()*8999999)+1000000).toString();", "pm.environment.set(\"phone_number\", phone_number);", "", "pm.environment.set(\"keeps_id\", \"e76b5082-f4fe-4f41-be79-1977840e16a8\");"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}