import os
import random
import uuid

import requests
from locust import HttpUser, task


class SmartzapUser(HttpUser):

    def on_start(self):
        myacc_url = os.environ['MYACC_URL']
        myacc_username = os.environ['MYACC_USERNAME']
        myacc_password = os.environ['MYACC_PASSWORD']
        payload = {
            'username': myacc_username,
            'password': myacc_password
        }
        self.access_token = requests.post(myacc_url, data=payload).json()['access_token']
        self.workspace_id = 'e76b5082-f4fe-4f41-be79-1977840e16a8'
        self.course_id = ''
        self.lesson_order = 0
        self.content_order = 0

    def _headers(self):
        return {
            'Authorization': f'Bearer {self.access_token}',
            'x-client': self.workspace_id
        }

    @task(5)
    def create_workspace(self):
        if self.workspace_id:
            return

        h = self._headers()
        my_workspace_id = str(uuid.uuid4())
        payload = {
            'id': my_workspace_id,
            'name': f'Workspace - {my_workspace_id}'
        }
        self.client.post('/api/v1/workspace', json=payload, headers=h)

    def _load_course_category(self):
        resp = self.client.get('/api/v1/course-category', headers=self._headers())
        categories = resp.json()['result']
        if not categories:
            return None

        index = random.randrange(0, len(categories))
        return categories[index]['id']

    @task
    def create_course(self):
        if not self.workspace_id:
            return

        if self.course_id:
            return

        category_id = self._load_course_category()
        if not category_id:
            return

        payload = {
            'category_id': category_id,
            'name': 'My Course',
            'description': 'My super cool course.',
            'holder_image': 'http://www.imagens.com.br/imagem-legal',
            'thumb_image': 'http://www.imagens.com.br/imagem-legal',
            'is_active': False,
            'status': 'CREATING',
            'lang': 'pt-br'
        }

        resp = self.client.post('/api/v1/course', json=payload, headers=self._headers())
        self.course_id = resp.json()['id']

    @task(5)
    def create_lesson(self):
        if not self.course_id:
            return

        self.lesson_order += 1
        payload = {
            'course_id': self.course_id,
            'name': f'Lesson - {self.lesson_order}',
            'description': f'My super cool lesson - {self.lesson_order}',
            'order': self.lesson_order
        }

        self.client.post('/api/v1/lesson', json=payload, headers=self._headers())

    def _load_lesson_id(self):
        if not self.course_id:
            return None

        resp = self.client.get(f'/api/v1/course/{self.course_id}/lesson', headers=self._headers())
        lessons = resp.json()['result']
        if not lessons:
            return None

        index = random.randrange(0, len(lessons))
        return lessons[index]['id']

    def _load_content_type(self):
        resp = self.client.get('/api/v1/content-type', headers=self._headers())
        content_types = resp.json()['result']
        index = random.randrange(0, len(content_types))
        return content_types[index]['id']

    @task(5)
    def create_content(self):
        lesson_id = self._load_lesson_id()
        if not lesson_id:
            return

        content_type_id = self._load_content_type()
        self.content_order += 1
        payload = {
            'lesson_id': lesson_id,
            'type_id': content_type_id,
            'name': f'Content - {self.content_order}',
            'description': f'My super cool content - {self.content_order}',
            'order': self.content_order,
            'dispatch_in': 3 + self.content_order,
            'learn_content': '12312-fwr33f-12f12f13f41-f1'
        }

        self.client.post('/api/v1/content', json=payload, headers=self._headers())

    @task(5)
    def create_user(self):
        if not self.workspace_id:
            return

        phone_number = [5, 5, 4, 8, 9]
        for _ in range(0, 8):
            phone_number.append(random.randint(0, 9))

        payload = {
            'name': f'User {uuid.uuid4()}',
            'phone': ''.join(map(str, phone_number))
        }

        self.client.post('/api/v1/user', json=payload, headers=self._headers())

    @task(20)
    def read_courses(self):
        if not self.workspace_id:
            return

        self.client.get('/api/v1/course', headers=self._headers())

    @task(20)
    def read_users(self):
        if not self.workspace_id:
            return

        self.client.get('/api/v1/user', headers=self._headers())

    @task(20)
    def read_contents(self):
        lesson_id = self._load_lesson_id()
        if not lesson_id:
            return

        self.client.get(f'/api/v1/lesson/{lesson_id}/content', headers=self._headers())

    @task(20)
    def read_content_types(self):
        if not self.workspace_id:
            return

        self.client.get('/api/v1/content-type', headers=self._headers())

    @task(20)
    def read_course_categories(self):
        if not self.workspace_id:
            return

        self.client.get('/api/v1/course-category', headers=self._headers())
