import datetime

from apscheduler.schedulers.background import BackgroundScheduler
from domain import config
from logger import logger
from tasks import task


def log(message):
    now = datetime.datetime.utcnow()
    logger.info(f'[{now}] {message}')


def message_scheduler_handler():
    log('Scheduler: message_scheduler_handler')
    task.prepare_messages.delay()


def external_message_scheduler_handler():
    log('Scheduler: external_message_scheduler_handler')
    task.prepare_external_messages.delay()


def course_processor_scheduler_handler():
    log('Scheduler: course_processor_scheduler_handler')
    task.load_course_stats.delay()


def billing_scheduler_handler():
    log('Scheduler (Cron): billing_scheduler_handler')
    task.process_billing_charges.delay()


def enrollment_scheduler_handler():
    log('Scheduler (Cron): enrollment_scheduler_handler')
    task.process_waiting_enrollments.delay()


def prepare_courses_recommendations_scheduler_handler():
    log('Scheduler: course_processor_scheduler_handler')
    task.prepare_courses_recommendations.delay()


def prepare_course_reminders_scheduler_handler():
    log(f'Scheduler: {prepare_course_reminders_scheduler_handler.__name__}')
    task.prepare_course_reminders.delay()


def cancel_idle_enrollments_handler():
    log('Scheduler: course_processor_scheduler_handler')
    task.cancel_idle_enrollments.delay()


def start_background_schedulers():
    scheduler = BackgroundScheduler(timezone="America/Sao_Paulo")

    jobs = [
        {'job': message_scheduler_handler, 'minutes': config.d.SCHEDULER_INTERVAL_MINUTES},
        {'job': external_message_scheduler_handler, 'minutes': config.d.SCHEDULER_INTERVAL_MINUTES},
        {'job': course_processor_scheduler_handler, 'minutes': config.d.COURSE_PROCESSOR_INTERVAL_MINUTES},
        {
            'job': prepare_course_reminders_scheduler_handler,
            'minutes': config.d.PREPARE_COURSE_REMINDERS_INTERVAL_MINUTES
        },
    ]
    for j in jobs:
        scheduler.add_job(j['job'], trigger='interval', minutes=j['minutes'])

    scheduler.add_job(billing_scheduler_handler, trigger='cron', hour=config.d.BILLING_HOUR)
    scheduler.add_job(enrollment_scheduler_handler, trigger='cron', hour=config.d.ENROLLMENT_WAITING_HOUR)
    scheduler.add_job(cancel_idle_enrollments_handler, trigger='cron', hour=config.d.CANCEL_IDLE_ENROLLMENTS_HOUR)
    scheduler.add_job(
        prepare_courses_recommendations_scheduler_handler,
        trigger='cron',
        hour=config.d.RECOMMENDATION_HOUR,
        minute=0,
        day_of_week='mon-fri'
    )

    scheduler.start()
