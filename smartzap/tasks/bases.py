import celery
import requests
from domain.config import Config
from sqlalchemy.exc import IntegrityError


class CaixaBaseTask(celery.Task):
    ignore_result = True
    max_retries = 3
    autoretry_for = (IntegrityError,)

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        self.send_error_email(exc, args)

    def send_error_email(self, exc, args):
        headers = {"Content-Type": "application/json"}
        json_data = {
            "receiverMail": Config.KEEPS_SUPPORT_EMAIL,
            "subject": "CAIXA SMARTZAP ERROR",
            "workspaceId": Config.KEEPS_WORKSPACE_ID,
            "language": Config.NOTIFICATION_DEFAULT_LANGUAGE,
            "template": Config.NOTIFICATION_CAIXA_SMARTZAP_BACKGROUND_TASK_ERROR,
            "templateData": {
                "error_detail": str(exc),
                "task_args": str(args),
                "task_name": ".CAIXA_BASE_TASK"
            }
        }
        requests.request(
            "POST",
            f'{Config.NOTIFICATION_API_URL}/emails/messages',
            headers=headers,
            data=json_data
        )
