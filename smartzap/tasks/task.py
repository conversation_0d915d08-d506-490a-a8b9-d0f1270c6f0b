import datetime
import traceback
from contextlib import contextmanager

import pytz
import ucache
from celery.exceptions import Retry
from celery.utils.log import get_task_logger
from domain import config, database, di, model, service
from domain.clients.learn_analytics_client import LearnAnalyticsClient
from domain.config import Config
from domain.constants.task_names import DISPATCH_MESSAGE, DISPATCH_MESSAGE_BY_EXTERNAL
from domain.model import Course, Enrollment, Workspace
from domain.service import EnrollmentService, NotificationService, WorkspaceService
from domain.services.courses_recommendations_service import CoursesRecommendationsService
from domain.services.interfaces.course_reminders_service import ICourseRemindersService
from domain.services.report_service import (
    COMPLETED_ENROLLMENTS,
    IN_PROGRESS_ENROLLMENTS,
    USER_ACTIVITIES,
    ReportService,
)
from domain.signals.events import CREATED_KEY, course_nps_score_event
from logger import logger
from tasks.bases import CaixaBaseTask
from tasks.celery_app import app

logger = get_task_logger(__name__)

DAYS_AFTER_LAST_ENROLLMENT = [0, 3, 5]


@app.on_after_configure.connect
def register_schedule_handler(sender, **kwargs):
    service.schedule_sent_signal.on('sent', schedule_changed_handler)


@app.on_after_configure.connect
def register_nps_score_handler(sender, **kwargs):
    course_nps_score_event.on(CREATED_KEY, course_nps_score_event_handler)


def build_container(client_id='', user_id=''):
    class TasksGlobals(di.Globals):
        def __init__(self, c_id, u_id):
            self.c_id = c_id
            self.u_id = u_id

        def client_id(self):
            return self.c_id

        def user_id(self):
            return self.u_id

    cache = ucache.MemoryCache()
    db, db_session = database.create_engine_and_session(config.d.DATABASE_URL)
    database.shared_session = db_session
    container = di.Container(config.d, cache, g_attr=TasksGlobals(client_id, user_id))
    return container, db


def build_non_db_container():
    cache = ucache.MemoryCache()
    container = di.Container(config.d, cache)
    return container


@contextmanager
def non_db_task(method_name):
    container = build_non_db_container()
    try:
        yield container
    except Exception as error:
        webhook_logger = container.webhook_logger()
        webhook_logger.emit_short_message(f'Smartzap Worker | {method_name}', error)


@contextmanager
def task_transaction(method_name, identifier=None, client_id='', user_id=''):
    container, db = build_container(client_id=client_id, user_id=user_id)
    try:
        yield container
    except Exception as error:
        webhook_logger = container.webhook_logger()
        webhook_logger.emit_short_message(f'Smartzap Worker | {method_name}', error)
        logger.exception(error)
    finally:
        database.shared_session.remove()
        db.dispose()


@contextmanager
def task_reports_transaction(report_error, client_id, user_id, course_id):
    container, db = build_container(client_id, user_id)
    notification_service = container.notification()
    try:
        yield container
    except Retry:
        return
    except Exception as error:
        notification_service.notify_error('Error generating report', report_error)
        webhook_logger = container.webhook_logger()
        webhook_logger.emit_short_message(f'Smartzap Worker | {report_error}', error)
    finally:
        database.shared_session.remove()
        db.dispose()


@app.task(exchange=config.d.CELERY_QUEUE)
def prepare_messages():
    with task_transaction('prepare_messages') as container:
        scheduler = container.schedule()
        page = 1
        processing = True
        while processing:
            pending_schedules = scheduler.load_pending_schedules(page, 10)
            for schedule in pending_schedules:
                logger.info(f'New schedule: {schedule.id}')
                dispatch_message.delay(schedule.id)
            processing = bool(pending_schedules)
            page += 1


@app.task(exchange=config.d.CELERY_QUEUE)
def prepare_external_messages():
    with task_transaction(prepare_external_messages.__name__) as container:
        scheduler = container.schedule()
        page = 1
        processing = True
        while processing:
            pending_schedules = scheduler.load_pending_schedules(page, 10, True)
            for schedule in pending_schedules:
                logger.info(f'New schedule: {schedule.id}')
                dispatch_message_by_external(schedule.id)
            processing = bool(pending_schedules)
            page += 1


@app.task(name=DISPATCH_MESSAGE, rate_limit='3/s', exchange=config.d.CELERY_QUEUE)
def dispatch_message(schedule_id):
    with task_transaction('dispatch_message', identifier=schedule_id) as container:
        logger.info(f'Dispatching schedule: {schedule_id}')
        scheduler_admin = container.schedule_admin()
        scheduler_admin.dispatch(schedule_id)
        logger.info(f'Schedule dispatched: {schedule_id}')


@app.task(name=DISPATCH_MESSAGE_BY_EXTERNAL, rate_limit='3/s', exchange=config.d.CELERY_QUEUE)
def dispatch_message_by_external(schedule_id):
    with task_transaction(dispatch_message_by_external.__name__, identifier=schedule_id) as container:
        logger.info(f'Dispatching schedule: {schedule_id}')
        scheduler_admin = container.schedule_admin_v2()
        scheduler_admin.dispatch(schedule_id)
        logger.info(f'Schedule dispatched: {schedule_id}')


@app.task(exchange=config.d.CELERY_QUEUE)
def send_message(phone_number, message_type, message_data, callback_url, lang, private_channels, media_url):
    with non_db_task('send_message') as container:
        whatsapp = container.whatsapp()
        private_channels = model.LanguageChannels(private_channels) if private_channels else None
        whatsapp.send_message(phone_number, message_type, message_data, callback_url, lang, private_channels, media_url)


@app.task(exchange=config.d.CELERY_QUEUE)
def load_course_stats():
    with task_transaction('load_course_stats') as container:
        notification_service = container.notification()
        course_processor = container.course_processor()
        courses = course_processor.execute()
        for c in courses:
            notification_service.override_user(c.workspace_owner_id, c.user_creator_id)
            notification_service.notify_course('You have a course for review and publish', c.id)


@app.task(exchange=config.d.CELERY_QUEUE)
def process_billing_charges():
    with task_transaction('process_billing_charges') as container:
        workspace_service = container.workspace()
        workspace_service.process_billing_charges(datetime.date.today())


@app.task(exchange=config.d.CELERY_QUEUE)
def process_waiting_enrollments():
    with task_transaction('process_waiting_enrollments') as container:
        enrollment_service = container.enrollment()
        limit_days_waiting = config.d.ENROLLMENT_LIMIT_DAYS_WAITING
        timestamp = datetime.date.today() - datetime.timedelta(days=limit_days_waiting)
        status = model.EnrollmentStatus.waiting
        filters = service.FilterBuilder().eq('status', status).lte('start_date', timestamp).build()
        waiting_enrollments = enrollment_service.apply_filters(filters)
        enrollment_service.cancel_in_batch(waiting_enrollments)


@app.task(exchange=config.d.CELERY_QUEUE)
def users_report_csv(client_id, user_id, filters):
    report_message = 'Users Report CSV'
    with task_reports_transaction(report_message, client_id, user_id, None) as container:
        notification_service = container.notification()
        logger.info(report_message)
        stats_service = container.stats()
        url = stats_service.generate_users_csv(filters)
        notification_service.notify_report(report_message, url)


@app.task(bind=True, exchange=config.d.CELERY_QUEUE, default_retry_delay=30)
def notify_user_when_analytics_report_processed(self, report_id, report_message, client_id, user_id):
    with task_reports_transaction(report_message, client_id, user_id, None) as container:
        learn_analytics_client: LearnAnalyticsClient = container.learn_alytics_client()
        notification_service: NotificationService = container.notification()
        report = learn_analytics_client.get_report(report_id, client_id)
        report_url = report["url"]
        if not report_url:
            self.retry()
        notification_service.notify_report(report_message, report_url)


def completed_enrollments_report_csv(client_id, user_id, course_id, user_token):
    report_message = 'Completed Enrollments Report CSV'
    report_type = COMPLETED_ENROLLMENTS
    with task_reports_transaction(report_message, client_id, user_id, course_id) as container:
        report_service: ReportService = container.report_service()
        report_id = report_service.generate(report_type, course_id, user_token)
        notify_user_when_analytics_report_processed.delay(report_id, report_message, client_id, user_id)


@app.task(exchange=config.d.CELERY_QUEUE)
def in_progress_enrollments_report_csv(client_id, user_id, course_id, user_token):
    report_message = 'In Progress Enrollments Report CSV'
    report_type = IN_PROGRESS_ENROLLMENTS
    with task_reports_transaction(report_message, client_id, user_id, course_id) as container:
        report_service: ReportService = container.report_service()
        report_id = report_service.generate(report_type, course_id, user_token)
        notify_user_when_analytics_report_processed.delay(report_id, report_message, client_id, user_id)


@app.task(exchange=config.d.CELERY_QUEUE)
def activities_report_csv(client_id, user_id, course_id, token):
    report_message = 'Activities Report CSV'
    report_type = USER_ACTIVITIES
    with task_reports_transaction(report_message, client_id, user_id, course_id) as container:
        report_service: ReportService = container.report_service()
        report_id = report_service.generate(report_type, course_id, token)
        notify_user_when_analytics_report_processed.delay(report_id, report_message, client_id, user_id)


@app.task(exchange=config.d.CELERY_QUEUE)
def quizzes_report_csv(client_id, user_id, course_id):
    report_message = 'Quizzes Report CSV'
    logger.info('asaaaasssssssssssssssssssssssssssssssssssssssss')
    with task_reports_transaction(report_message, client_id, user_id, course_id) as container:
        notification_service = container.notification()
        logger.info(report_message)
        stats_service = container.stats()
        url = stats_service.generate_quizzes_csv(course_id)
        notification_service.notify_report(report_message, url)


@app.task(exchange=config.d.CELERY_QUEUE)
def learn_analytics_report_pdf_smartzap_course(client_id, user_id, course_id):
    report_message = 'Smartzap Course Overview PDF'
    with task_reports_transaction(report_message, client_id, user_id, course_id) as container:
        notification_service = container.notification()
        logger.info(f'Learn Analytics | {report_message}')
        la_client = container.learn_analytics()
        result = la_client.get_course_overview_pdf(course_id, client_id)
        if 'url' not in result:
            raise Exception(result)

        notification_service.notify_report(report_message, result['url'])


@app.task(exchange=config.d.CELERY_QUEUE)
def update_enrollment_progress(enrollment_id):
    with task_transaction('update_enrollment_progress') as container:
        enrollment_service = container.enrollment()
        print(f'Update Enrollment Progress | {enrollment_id}')
        enrollment_service.update_progress(enrollment_id)
        logger.info(f'Update Enrollment Progress | {enrollment_id}')


@app.task(exchange=config.d.CELERY_QUEUE)
def calc_course_nps(course_id: str):
    with task_transaction(calc_course_nps.__name__) as container:
        nps_service = container.nps_service()
        logger.info(f'Updating Course NPS Score | {course_id}')
        nps_service.calc_course_score(course_id)


def course_nps_score_event_handler(course: Course):
    calc_course_nps.delay(course.id)


def schedule_changed_handler(schedule):
    logger.info(f'Schedule sent callback | Type: {schedule.type}')
    if schedule.type == model.ScheduleType.lesson_content:
        update_enrollment_progress.delay(schedule.enrollment_id)


@app.task(exchange=config.d.CELERY_QUEUE)
def backup_and_delete_course(client_id, user_id, course_id):
    with task_transaction('backup_and_delete_course', client_id=client_id, user_id=user_id) as container:
        course_backup_service = container.course_backup()
        logger.info(f'Backup and Delete Course | {course_id}')
        course_backup_service.backup_and_delete(course_id)


def course_delete_handler(course, client_id, user_id):
    logger.info(f'Course delete callback | Course Status: {course.status}')
    if course.status == model.CourseStatus.deleting:
        backup_and_delete_course.delay(client_id, user_id, course.id)


def slack_webhook_error_attachment(client_id, user_id, course_id, error):
    return [
        {
            'color': 'danger',
            'fields': [
                {"title": "Workspace", "value": client_id, "short": False},
                {"title": "Course", "value": course_id, "short": False},
                {"title": "User", "value": user_id, "short": False},
                {"title": "Error", "value": error, "short": False},
                {"title": "Traceback", "value": traceback.format_exc(), "short": False}]
        }
    ]


@app.task(exchange=config.d.CELERY_QUEUE, rate_limit='5/s')
def send_courses_recommendations_by_user(user_phone: str, workspace_id: str):
    with task_transaction(send_courses_recommendations_by_workspace.__name__, workspace_id) as container:
        recommendations_service: CoursesRecommendationsService = container.courses_recommendation_service()
        now = datetime.datetime.now(pytz.timezone(Config.DEFAULT_TIMEZONE))
        saturday = 5

        if now.weekday() >= saturday or not (8 <= now.hour < 18):
            logger.info("Task skipped: Outside commercial hours.")
            return
        recommendations_service.schedule_message(user_phone, workspace_id)


@app.task(exchange=config.d.CELERY_QUEUE)
def send_courses_recommendations_by_workspace(workspace_id: str):
    with task_transaction(send_courses_recommendations_by_workspace.__name__, workspace_id) as container:
        recommendations_service: CoursesRecommendationsService = container.courses_recommendation_service()
        user_phones = recommendations_service.list_workspace_users_to_notify(workspace_id)
        for phone in user_phones:
            send_courses_recommendations_by_user(phone, workspace_id)


@app.task(exchange=config.d.CELERY_QUEUE)
def prepare_courses_recommendations():
    with task_transaction(prepare_courses_recommendations.__name__) as container:
        workspace_service: WorkspaceService = container.workspace()
        workspaces_to_notify = workspace_service.db.query(
            Workspace.id
        ).filter(
            Workspace.send_courses_recommendation_message.is_(True)
        ).all()

        for workspace in workspaces_to_notify:
            logger.info('Sending Courses Recommendations For Workspace id: {}'.format(workspace[0]))
            send_courses_recommendations_by_workspace(workspace[0])


@app.task(exchange=config.d.CELERY_QUEUE, base=CaixaBaseTask)
def enroll_user(user_id: str, course_id: str, workspace_id: str, start_date: str):
    """
        WARNING: ONLY USE FOR CAIXA API's
    """
    container, _ = build_container(client_id=workspace_id, user_id=user_id)
    enrollment_service: EnrollmentService = container.enrollment()
    enrollment = Enrollment(
        user_id=user_id, course_id=course_id, workspace_id=workspace_id, start_date=start_date
    )
    enrollment_service.add(enrollment)


@app.task(exchange=config.d.CELERY_QUEUE)
def cancel_idle_enrollments():
    with task_transaction(cancel_idle_enrollments.__name__) as container:
        idle_enrollment_service = container.idle_enrollments_service()
        idle_enrollment_service.cancel_all()


@app.task(exchange=config.d.CELERY_QUEUE)
def schedule_course_reminders_by_workspace(workspace_id: str):
    with task_transaction(schedule_course_reminders_by_workspace.__name__, workspace_id) as container:
        course_reminders_service: ICourseRemindersService = container.course_reminders_service()
        course_reminders_service.schedule_reminders_for_idle_enrollments(workspace_id)


@app.task(exchange=config.d.CELERY_QUEUE)
def prepare_course_reminders():
    with task_transaction(prepare_course_reminders.__name__) as container:
        workspace_service: WorkspaceService = container.workspace()
        workspaces_to_notify = workspace_service.db.query(
            Workspace.id
        ).filter(
            Workspace.send_course_reminder_message.is_(True)
        ).all()

        for workspace in workspaces_to_notify:
            logger.info('Sending Course Reminders For Workspace id: {}'.format(workspace[0]))
            schedule_course_reminders_by_workspace.delay(workspace[0])
