FROM python:3.9-bullseye

WORKDIR /app

EXPOSE 80

ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    HOME=/app \
    PATH=/app/.local/bin/:$PATH

ARG arg_revision=''
ARG arg_build_date=''
ENV REVISION=$arg_revision \
    BUILD_DATE=$arg_build_date

# Install system dependencies and clean up
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    software-properties-common \
    supervisor \
    gcc g++ python3-dev build-essential \
    swig netcat libstdc++6 linux-libc-dev \
    libxslt-dev bash cargo && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

COPY requirements.txt /app/

# Install Python dependencies
RUN pip3 install --upgrade pip setuptools && \
    pip install gunicorn && \
    pip install -r requirements.txt

COPY . /app
COPY supervisord.conf /etc/supervisord.conf

RUN chmod -R ugo+rwx /app && chmod ugo+x entrypoint.sh

ENTRYPOINT ["/app/entrypoint.sh"]
