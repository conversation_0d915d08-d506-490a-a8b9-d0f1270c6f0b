import flask_cors
import healthcheck
import marshmallow
import ucache
import flask
import logging
import traceback

from elasticapm.contrib.flask import ElasticAP<PERSON>
from flask_injector import FlaskInjector
from flask_sqlalchemy_session import flask_scoped_session
from sqlalchemy.exc import Integrity<PERSON>rror, SQLAlchemyError
from sqlalchemy_filters.exceptions import FieldNotFound
from werkzeug.exceptions import InternalServerError, NotFound

import views
from domain.exceptions.service_exceptions import NotFoundException, IntegrityException
from domain.signals.events import course_nps_score_event, CREATED_KEY
from flask_extensions import auth, webfilters, swagger
from domain import service, database, di, config, client
from tasks import task

# Don't perform MyAccount:get_user_info() for these paths
SKIP_ROLES_PATHS = ['/api/v1/view/activity', '/api/v1/view/content']

class FlaskGlobals(di.Globals):
    def client_id(self):
        return flask.g.client_id

    def user_id(self):
        return flask.g.sub

    def enrollment_id(self):
        return flask.g.enrollment_id


def database_available():
    db = database.shared_session
    is_database_working = True
    output = 'Database is ok'
    try:
        # to check database we will execute raw query
        db.execute('SELECT 1')
    except Exception as e:
        output = str(e)
        is_database_working = False
    return is_database_working, output


def _load_roles(myacc, token, workspace_id):
    user_info = myacc.get_user_info(token, workspace_id)

    if not user_info:
        return []

    roles = [role['key'] for role in user_info["roles"] if role['application_id'] == config.d.APP_ID]
    return roles


def _authorize_request(myacc):
    flask.g.sub = ''
    flask.g.email = ''
    flask.g.roles = {}
    flask.g.client_roles = []
    flask.g.enrollment_id = ''
    flask.g.token = flask.request.headers.get('Authorization', '')
    flask.g.client_id = flask.request.headers.get('x-client', '')

    if flask.g.token:
        if flask.g.token.upper().startswith('BEARER'):
            flask.g.token = flask.g.token.split()[-1]
        flask.g.sub, flask.g.email, flask.g.enrollment_id = auth.decode_token(flask.g.token)

        # Don't perform MyAccount:get_user_info()
        # print('[App] Path:', flask.request.path)
        # print('[App] Token:', flask.g.sub, flask.g.email, flask.g.enrollment_id )
        if any(skip_path in flask.request.path for skip_path in SKIP_ROLES_PATHS):
            # print('[App] skipping user roles...')
            return

        flask.g.client_roles = _load_roles(myacc, flask.g.token, flask.g.client_id)


def register_observers():
    service.course_delete_signal.on('delete', task.course_delete_handler)
    course_nps_score_event.on(CREATED_KEY, task.course_nps_score_event_handler)


def create_app(pages=None):
    if not pages:
        pages = views.pages

    app = flask.Flask(config.d.APP_NAME, static_folder=None)

    apm = ElasticAPM(
        app, 
        logging=logging.ERROR,
        service_name=config.d.ELASTIC_APM["SERVICE_NAME"],
        server_url=config.d.ELASTIC_APM["SERVER_URL"],
        secret_token=config.d.ELASTIC_APM["SECRET_TOKEN"],
        environment=config.d.ELASTIC_APM["ENVIRONMENT"],
        debug=config.d.ELASTIC_APM['DEBUG'],

        # Fine-tuning - Ref.: https://www.elastic.co/guide/en/apm/agent/python/current/configuration.html
        transaction_sample_rate=0.1,  # default: 1.0
        span_stack_trace_min_duration=-1,  # default: 5ms
        span_compression_same_kind_max_duration="5ms",  # default: 0ms,
    )

    for page in pages:
        flask_cors.CORS(page)
        app.register_blueprint(page)

    flask_cors.CORS(app)
    database.shared_session = flask_scoped_session(database.session_factory(config.d.DATABASE_URL), app)
    database.session_map[config.d.DATABASE_URL] = database.shared_session

    cache = ucache.MemoryCache()
    flask_g = FlaskGlobals()
    container = di.Container(config.d, cache, flask_g)
    webhook_logger = container.webhook_logger()
    auth.init(config.d.KEYCLOAK_SERVER_URL, config.d.KEYCLOAK_REALM)
    FlaskInjector(app=app, modules=[container.configure])
    swagger.init(app)
    health = healthcheck.HealthCheck(app, '/healthcheck')
    health.add_check(database_available)

    register_observers()

    @app.before_request
    def before_request_func():
        myacc = container.myacc_v2()
        _authorize_request(myacc)

    @app.errorhandler(Exception)
    def handle_exception(e):
        # print('[App] handle_exception', e)
        # if config.d.DEBUG:
        #    traceback.print_exc()

        if isinstance(e, InternalServerError):
            e = e.original_exception
        if isinstance(e, swagger.JsonBodyException):
            return str(e), 400
        if isinstance(e, auth.AuthenticationException):
            return str(e), 401
        if isinstance(e, auth.AuthorizationException):
            return str(e), 403
        if isinstance(e, auth.PermissionException):
            return str(e), 403
        if isinstance(e, IntegrityError):
            orig = getattr(e, 'orig', '')
            if orig:
                return str(orig), 409
            return 'Problem executing database command. Check if the foreign keys exist.', 409
        if isinstance(e, NotFound):
            return str(e), 404
        if isinstance(e, FieldNotFound):
            traceback.print_exc()
            return str(e), 409
        if isinstance(e, SQLAlchemyError):
            traceback.print_exc()
            return 'Database inconsistency.', 409
        if isinstance(e, NotFoundException):
            return e.message, 404
        if isinstance(e, IntegrityException):
            traceback.print_exc()
            return e.message, 409
        if isinstance(e, AssertionError):
            return str(e), 422
        if isinstance(e, marshmallow.ValidationError):
            return str(e), 422
        if isinstance(e, webfilters.WebFilterValidationException):
            return str(e), 422
        if isinstance(e, client.OpenIDException):
            return str(e.message), e.code

        traceback.print_exc()
        app.logger.error("An unexpected error seems to have occurred", exc_info=True)
        apm.capture_exception()
        webhook_logger.emit_request_exception(e)
        return 'An unexpected error seems to have occurred.', 500

    return app
