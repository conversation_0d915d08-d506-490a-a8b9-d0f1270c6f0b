import datetime

from apscheduler.schedulers.blocking import BlockingScheduler
from logger import logger
from tasks import scheduler


def health_check():
    logger.info(f'[{datetime.datetime.now(datetime.UTC)}] Schedulers alive')


if __name__ == '__main__':
    logger.info('Running schedulers...')
    scheduler.start_background_schedulers()
    lock_scheduler = BlockingScheduler(timezone="America/Sao_Paulo")
    lock_scheduler.add_job(health_check, trigger='interval', minutes=60)
    lock_scheduler.start()
