import functools

import flask
from apispec import APISpec, BasePlugin
from apispec.ext.marshmallow import MarshmallowPlugin
from flask_apispec import FlaskApiSpec, annotations
from werkzeug.exceptions import BadRequest


class JsonBodyException(Exception):
    def __init__(self, message):
        self.message = message


class DisableOptionsOperationPlugin(BasePlugin):
    def operation_helper(self, operations, **kwargs):
        operations.pop("options", None)


def init(app):
    api_spec_security = {
        'security': [
            {'ApiKeyAuth': []},
            {'X-Client': []},
        ]
    }
    api_spec = APISpec(title='SmartZap',
                       version='v1',
                       openapi_version='2.0',
                       plugins=[
                           DisableOptionsOperationPlugin(),
                           MarshmallowPlugin()
                       ],
                       **api_spec_security)
    api_spec.components.security_scheme('ApiKeyAuth', {'type': 'apiKey', 'in': 'header', 'name': 'Authorization'})
    api_spec.components.security_scheme('X-Client', {'type': 'apiKey', 'in': 'header', 'name': 'x-client'})
    app.config.update({
        'APISPEC_SPEC': api_spec,
        'APISPEC_SWAGGER_URL': '/swagger-json/',
    })
    docs = FlaskApiSpec(app)
    docs.register_existing_resources()
    return docs


def use_schema(schema_type, parameter_name='object', partial=False, inherit=None, apply=None, **kwargs):
    """
    Transform request data in model or kwargs.
    Args:
        schema_type: Class or instance of Schema.
        parameter_name: name of parameter injected in method.
        partial: Load data partially
        inherit: args from parent classes.
        apply: Parse request with specified args.
        kwargs:

    Returns:

    """
    if hasattr(schema_type, '__call__'):
        schema_type = schema_type()

    def decorator(function):
        options = {
            'args': schema_type,
            'kwargs': kwargs,
        }
        annotations.annotate(function, 'args', [options], inherit=inherit, apply=apply)

        @functools.wraps(function)
        def wrapper(*args, **kwargs):
            if flask.request.is_json:
                try:
                    request_value = flask.request.json
                except BadRequest:
                    raise JsonBodyException('JSON content malformed')
            elif flask.request.args:
                request_value = flask.request.args
            else:
                request_value = {}
            kwargs[parameter_name] = schema_type.load(request_value, partial=partial)
            return function(*args, **kwargs)

        return wrapper
    return decorator


def marshal(schema_type, code=200, description='', inherit=None, apply=None, responses=None):
    """
    serialize result in data.
    Args:
        schema_type: Class or instance of Schema.
        code: code to return in response.
        description: description of apispec.
        inherit: args from parent classes.
        apply: Parse request with specified args.
        responses: List of dicts with code response.

    Returns:

    """
    if hasattr(schema_type, '__call__'):
        schema_type = schema_type()

    def decorator(function):
        options = {
            code: {
                'schema': schema_type,
                'description': description,
            },
        }
        if responses:
            annotations.annotate(function, 'schemas', responses, inherit=inherit, apply=apply)

        annotations.annotate(function, 'schemas', [options], inherit=inherit, apply=apply)

        @functools.wraps(function)
        def wrapper(*args, **kwargs):
            result = function(*args, **kwargs)
            if not isinstance(result, flask.Response):
                return schema_type.dump(result), code
            return result

        return wrapper

    return decorator


def doc_body(schema_type, inherit=None):
    """
    Transform request data in model or kwargs.
    Args:
        schema_type: Class or instance of Schema.
        inherit: args from parent classes.
        apply: Parse request with specified args.
        kwargs:

    Returns:

    """
    if hasattr(schema_type, '__call__'):
        schema_type = schema_type()

    def decorator(function):
        options = {
            'args': schema_type
        }
        annotations.annotate(function, 'args', [options], inherit=inherit)

        @functools.wraps(function)
        def wrapper(*args, **kwargs):
            return function(*args, **kwargs)

        return wrapper
    return decorator
