import inspect
from typing import List

import flask
import marshmallow


class WebFilterValidationException(Exception):
    def __init__(self, message):
        self.message = message


def _parse_key(key):
    operations = {
        'eq': '==',
        'ne': '!=',
        'gt': '>',
        'lt': '<',
        'gte': '>=',
        'lte': '<=',
        'like': 'like',
        'ilike': 'ilike',
        'in': 'in',
        'not_in': 'not_in'
    }
    v = key.split('__')
    op = v[-1]
    if not operations.get(op):
        return key, '=='
    key = '__'.join(v[:-1])
    return key, operations.get(op, '')


def _parse_value(op, value):
    value = value.strip()
    if op in ['like', 'ilike'] and value:
        return f'%{value}%'
    if op in ['in', 'not_in'] and value:
        return [s.strip() for s in value.split(',') if s.strip()]
    return value


def _parse_sort(order):
    fields = order.split(',')
    query_sort = []
    for f in fields:
        f = f.strip()
        if not f:
            continue
        if f.startswith('-'):
            query_sort.append({'field': f[1:], 'direction': 'desc', 'nullslast': True})
        else:
            query_sort.append({'field': f, 'direction': 'asc', 'nullsfirst': True})
    return query_sort


def _parse_pagination(page, per_page):
    page = int(page) if page else 1
    per_page = int(per_page) if per_page else None
    return {'page': page, 'per_page': per_page}


def _validate_sort(schema, sort):
    for f in sort:
        field = f['field']
        if field not in schema.load_fields:
            raise WebFilterValidationException(f'WebFilter: Sort field [{field}] not found in schema')


def _validate(schema, filters, sort):
    if not schema:
        return
    if inspect.isclass(schema):
        schema = schema()
    try:
        data = {}
        for f in filters:
            data[f['field']] = f['value']
        filter_parsed = schema.load(data)
        for f in filters:
            f['value'] = filter_parsed.get(f['field'])

        _validate_sort(schema, sort)
    except marshmallow.ValidationError as err:
        raise WebFilterValidationException(f'WebFilter schema validation error: [{err.messages}]')


def _parse_search(search, search_columns):
    if not search:
        return []
    query_search = []
    for column in search_columns:
       query_search.append({"field": column, "op": "ilike", "value": f'%{search}%'})
    return query_search


def load_filters(schema=None, req=None, search_columns=None, search_field='search', sort_field='sort', page_field='page', per_page_field='per_page', override_filters=None):
    """
    :param per_page_field: The name of the field for page size
    :param page_field: The name of the field for page number
    :param sort_field: The name of the field for sorting
    :param search_field: The name of the field for search
    :param search_columns: Names of the columns for search
    :param req: Flask request or default if empty
    :param schema: Marshmallow schema for fields validation
    :param override_filters: Default filters to implement in request filters or override
    :return: dict: filter, sort and pagination data.
    """
    if search_columns is None:
        search_columns = []
    query_filters = []
    req = req or flask.request
    for key, value in req.args.items():
        key = key.strip()
        if key in [sort_field, page_field, per_page_field, search_field]:
            continue
        field, op = _parse_key(key)
        if op:
            value = _parse_value(op, value)
            query_filters.append({'field': field, 'op': op, 'value': value})

    if override_filters:
        query_filters = _apply_override_filters(override_filters, query_filters)

    search_value = req.args.get(search_field, '')
    query_search = _parse_search(search_value, search_columns)
    query_sort = _parse_sort(req.args.get(sort_field, ''))
    query_pagination = _parse_pagination(req.args.get('page'), req.args.get('per_page', None))
    _validate(schema, query_filters, query_sort)
    return {'filter_spec': query_filters, 'search_spec': query_search, 'sort_spec': query_sort,
            'pag_spec': query_pagination}


def _apply_override_filters(filters: dict, query_filters: List[dict]) -> List[dict]:
    for key, value in filters.items():
        field, op = _parse_key(key)
        _delete_query_filters(query_filters, field)
        value = _parse_value(op, value)
        query_filters.append({'field': field, 'op': op, 'value': value})
    return query_filters


def _delete_query_filters(query_filters, field):
    field_filters = list(filter(lambda spec: spec["field"] == field, query_filters))
    for _filter in field_filters:
        query_filters.remove(_filter)
