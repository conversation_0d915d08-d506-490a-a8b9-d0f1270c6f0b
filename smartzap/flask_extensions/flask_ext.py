import functools

import flask
from domain.config import d
from flask_extensions import auth


def validate_permission(user_roles):
    if not flask.g.client_roles:
        raise auth.AuthorizationException('Permissions not found')

    u_roles = user_roles
    if not isinstance(user_roles, list):
        u_roles = [user_roles]

    for r in u_roles:
        if r not in flask.g.client_roles:
            raise auth.PermissionException('Missing permissions')


def check_caixa_x_client():
    def decorate(f):
        @functools.wraps(f)
        def wrapper(*args, **kargs):
            if not flask.g.client_id:
                raise auth.AuthenticationException('Missing x-client (client_id) header')
            if str(flask.g.client_id) not in d.CAIXA_WORKSPACE_IDS:
                raise auth.AuthorizationException(
                    f"Access denied: The client ID '{flask.g.client_id}' is not authorized to access this resource."
                )
            return f(*args, **kargs)

        return wrapper

    return decorate


def check_permission(user_roles):
    def decorate(f):
        @functools.wraps(f)
        def wrapper(*args, **kargs):
            validate_permission(user_roles)
            return f(*args, **kargs)

        return wrapper

    return decorate


def smart_view():
    def decorate(f):
        @functools.wraps(f)
        def wrapper(*args, **kargs):
            if not flask.g.sub:
                raise auth.AuthorizationException('User not found')
            return f(*args, **kargs)

        return wrapper

    return decorate


def twilio_interaction_auth():
    def decorate(f):
        @functools.wraps(f)
        def wrapper(*args, **kwargs):
            api_key = flask.request.args.get('api-key', '').strip()
            if not auth.validate_twilio_api_key(api_key):
                raise auth.AuthenticationException(
                    "Invalid or missing API key. Please provide a valid 'api-key' as a query parameter."
                )
            return f(*args, **kwargs)

        return wrapper

    return decorate
