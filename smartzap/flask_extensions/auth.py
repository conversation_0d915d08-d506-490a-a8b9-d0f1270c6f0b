import jwt
import requests
import urljoin
from domain import config
from jwt import InvalidAlgorithmError

public_key = None


class AuthenticationException(Exception):
    def __init__(self, message):
        self.message = message


class AuthorizationException(Exception):
    def __init__(self, message):
        self.message = message


class PermissionException(Exception):
    def __init__(self, message):
        self.message = message


def init(url, realm):
    url = urljoin.url_path_join(url, f'realms/{realm}')
    r = requests.get(url)
    data = r.json()
    x5c = data['public_key']
    pem_start = '-----BEGIN PUBLIC KEY-----\n'
    pem_end = '\n-----END PUBLIC KEY-----\n'
    p_key = f'{pem_start}{x5c}{pem_end}'
    global public_key
    public_key = p_key


def pre_decode_token(token):
    decode_options = {'verify_signature': False, 'verify_aud': False, 'exp': False}
    token_decoded = jwt.decode(token, '', algorithms=['RS256', 'HS256'], options=decode_options)
    return token_decoded


def decode_smartzap_token(token):
    decode_options = {'verify_signature': True, 'verify_aud': False, 'exp': True}
    token_decoded = jwt.decode(token, config.d.SMARTZAP_SECRET, algorithms=['HS256'], options=decode_options)
    return token_decoded['sub'], '', token_decoded.get('enrollment_id')


def decode_token(token):
    try:
        pre_decoded_token = pre_decode_token(token)
        if pre_decoded_token.get('aud') == 'smartzap':
            return decode_smartzap_token(token)

        decode_options = {'verify_signature': True, 'verify_aud': False, 'exp': True}
        token_decoded = jwt.decode(token, public_key, algorithms=['RS256'], options=decode_options)
        return token_decoded['sub'], token_decoded.get('email'), ''
    except jwt.ExpiredSignatureError:
        raise AuthenticationException('Token is expired')
    except (jwt.DecodeError, jwt.InvalidAudienceError) as error:
        raise AuthenticationException(str(error))
    except (KeyError, InvalidAlgorithmError):
        raise AuthenticationException('Token malformed')


def validate_twilio_api_key(api_key):
    return api_key == config.d.TWILIO_API_KEY
