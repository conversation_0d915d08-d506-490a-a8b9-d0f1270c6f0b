import ucache
from domain import config, database, di, model
from logger import logger


def log(msg):
    log_path = './smartzap/scripts/reprocessing_performance/rounding_performance.log'
    with open(log_path, 'a') as log_file:
        log_file.write(f'{msg}\n')
    logger.info(msg)


def build_container():
    cache = ucache.MemoryCache()
    db, db_session = database.create_engine_and_session(config.d.DATABASE_URL)
    database.shared_session = db_session
    container = di.Container(config.d, cache)
    return container, db


def get_enrollments():
    return database.shared_session.query(model.Enrollment).filter(
        model.Enrollment.performance.isnot(None),
        model.Enrollment.workspace_id == 'fa91ff3e-345e-4d39-a1c3-7a6e3bcd7ccb',
        model.Enrollment.course_id == 'c2fa343e-f1a9-439a-bb6c-4821ea72d521'
    ).all()


def update_enrollment_performance(enrollment, container):
    last_performance = enrollment.performance
    new_performance = container.course_report().build_performance(enrollment.course, enrollment)
    msg_log = f"Enrollment id: {enrollment.id} - Performance: {last_performance} - New Performance: {new_performance['percentage']}"
    log(msg_log)

    with database.transaction(database.shared_session):
        enrollment.performance = new_performance['percentage']


def rounding_performance():
    container, db = build_container()
    enrollments = get_enrollments()

    for enrollment in enrollments:
        update_enrollment_performance(enrollment, container)

    database.shared_session.commit()


if __name__ == "__main__":
    rounding_performance()
