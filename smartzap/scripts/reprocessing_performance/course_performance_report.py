import os

import ucache
from domain import config, database, di, model
from domain.config import Config
from logger import logger
from sqlalchemy import extract

WORKSPACE = 'fa91ff3e-345e-4d39-a1c3-7a6e3bcd7ccb'
COURSE = 'c2fa343e-f1a9-439a-bb6c-4821ea72d521'

def log(msg):
    log_path = './smartzap/scripts/reprocessing_performance/report.log'
    with open(log_path, 'a') as log_file:
        log_file.write(f'{msg}\n')
    logger.info(msg)

def build_container():
    cache = ucache.MemoryCache()
    db, db_session = database.create_engine_and_session(config.d.DATABASE_URL)
    database.shared_session = db_session
    container = di.Container(config.d, cache)
    return container, db

def get_enrollments():
    return database.shared_session.query(model.Enrollment).filter(
        model.Enrollment.status.in_(['STARTED', 'COMPLETED']),
        extract('year', model.Enrollment.start_date) == 2024,
        model.Enrollment.workspace_id == WORKSPACE,
        model.Enrollment.course_id == COURSE,
        # model.Enrollment.id.in_(['a0ee2cdc-ed84-4b16-b49f-cf4b8deb24a3'])
    ).all()

def update_enrollment_performance(enrollment, container):
    last_performance = enrollment.performance
    new_performance = container.course_report().build_performance(enrollment.course, enrollment)
    msg_log = f"Enrollment id: {enrollment.id} - Performance: {last_performance} - New Performance: {new_performance['percentage']}"
    log(msg_log)

    with database.transaction(database.shared_session):
        enrollment.performance = new_performance['percentage']

import csv


def generate():
    container,db = build_container()
    enrollments = get_enrollments()

    # Define os nomes dos cabeçalhos com base nos dados que você está gravando
    headers = [
        "Matrícula", "Nome", "Telefone", "Curso", "Status da Matrícula", "Data de Início",
        "Data de Fim", "Pontos Conquistados", "Performance", "Tempo Fazendo o Curso",
        "Total Pontos Curso", "Total Duração Curso",
        "Desafio CE 250ml",
        "Desafio Gel Formiga",
        "Desafio Rodilon Pellets",
        "Desafio Racumin Soft Bait",
        "Desafio Envu",
        "Desafio K- Othrine SC",
        "Desafio Gel Barata",
        "Conheça o K- Othrine SC",
        "Conheça o CE 250ml",
        "Conheça o 0,5P",
        "Desafio 0,5P",
        "Conheça o Gel Barata",
        "Conheça o Gel Formiga",
        "Conheça o Rodilon Pellets",
        "Conheça o Racumin Soft Bait",
        "Sobre as pragas",
        "Envu?",
    ]

    with open(f'{os.path.dirname(Config.BASE_DIR)}/temp/report.csv', 'w', newline='') as file:
        writer = csv.writer(file)
        writer.writerow(headers)
        for enrollment in enrollments:
            logger.info(f'Processando matrículas: {enrollment.id}')
            try:
                new_performance = container.course_report().build_performance(enrollment.course, enrollment)
                end_date = enrollment.end_date.strftime('%d/%m/%Y') if enrollment.end_date else "-"
                start_date = enrollment.start_date.strftime('%d/%m/%Y') if enrollment.start_date else "-"

                enroll_data = [
                    enrollment.id,
                    enrollment.user.name,
                    enrollment.user.phone,
                    new_performance['name'],
                    enrollment.status,
                    start_date,
                    end_date,
                    new_performance['points'],
                    container.course_report().format_percentage(new_performance['percentage']),
                    container.course_report().format_duration(new_performance['duration']),
                    new_performance['learn_points'],
                    container.course_report().format_duration(new_performance['learn_duration'])
                ]

                # Adicionando as porcentagens de lições diretamente à lista de dados
                lessons = [container.course_report().format_percentage(item['percentage']) for item in new_performance.get('lessons', [{'contents': []}])[0]['contents']]
                enroll_data.extend(lessons)

                writer.writerow(enroll_data)
                logger.info(",".join(str(item) for item in enroll_data))
                logger.info("###############################################################################")
            except Exception as e:
                logger.error(f'Erro ao processar matrícula {enrollment.id}: {e}')

    database.shared_session.commit()
    logger.info("Relatório gerado com sucesso!")


if __name__ == "__main__":
    generate()
