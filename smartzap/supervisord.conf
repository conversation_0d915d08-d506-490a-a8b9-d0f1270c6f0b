[supervisord]
nodaemon=true

[program:celery]
directory=/app
command=/bin/bash -c "celery -A tasks.task.app worker -E -l error"
numprocs=1
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
redirect_stderr=true
autostart=true
autorestart=true
startsecs=10
stopwaitsecs = 600
killasgroup=true
priority=998


[program:scheduler]
directory=/app
command=/bin/bash -c "python cron_schedulers.py"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
redirect_stderr=true
autostart=true
autorestart=true
startsecs=10
stopwaitsecs = 600
priority=998
