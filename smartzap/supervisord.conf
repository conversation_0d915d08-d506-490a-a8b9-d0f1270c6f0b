[supervisord]
nodaemon=true
logfile=/dev/stdout
logfile_maxbytes=0
loglevel=error

[program:celery]
directory=/app
command=/bin/bash -c "celery -A tasks.task.app worker -E -l error"
numprocs=1
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
redirect_stderr=false
autostart=true
autorestart=true
startsecs=10
stopwaitsecs = 600
killasgroup=true
priority=998
stdout_events_enabled=true
stderr_events_enabled=true


[program:scheduler]
directory=/app
command=/bin/bash -c "python cron_schedulers.py"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
redirect_stderr=false
autostart=true
autorestart=true
startsecs=10
stopwaitsecs = 600
priority=998
stdout_events_enabled=true
stderr_events_enabled=true
