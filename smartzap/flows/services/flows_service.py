import json
import re
from typing import Dict, List, Optional

from flows.exceptions import FlowNotFoundException, MissingFlowsException
from flows.flow import Flow
from flows.flow_input_data import FlowInputData
from twilio.base.exceptions import TwilioRestException
from twilio.rest import Client
from twilio.rest.studio.v2.flow.execution import ExecutionInstance


class FlowsService:
    REQUIRED_FLOW_TYPES = {"enrollment_intro", "enrollment_content", "enrollment_finish"}

    def __init__(self, config_file_path: str) -> None:
        self.flows: List[Flow] = self.load_flows(config_file_path)

    def trigger_flow(self, flow_type: str, input_params: FlowInputData) -> str:
        client = Client(input_params.account_sid, input_params.auth_token)
        flow_sid = self.get_flow_sid(input_params.account_sid, flow_type)
        try:
            execution = client.studio.v2.flows(flow_sid).executions.create(
                to=f'whatsapp:{input_params.recipient}',
                from_=f'whatsapp:{input_params.sender}',
                parameters=input_params.parameters
            )
            return execution.sid
        except TwilioRestException as error:
            return self.handle_flow_exception(error, flow_type, input_params)

    def handle_flow_exception(self, error: TwilioRestException, flow_type: str, input_params: FlowInputData) -> str:
        """
        Handles specific TwilioRestException during flow execution attempts.

        :param error: The exception encountered.
        :param flow_type: The type of flow that was being triggered.
        :param input_params: The flow input parameters.
        :return: The SID of the newly triggered or restarted flow.
        """
        if 'is already active for this contact' not in str(error):
            raise
        execution_sid = self.extract_execution_sid(str(error))
        if execution_sid:
            self.stop_execution(flow_type, input_params, execution_sid)
        return self.trigger_flow(flow_type, input_params)

    def extract_execution_sid(self, error_message: str) -> Optional[str]:
        """
        Extracts the execution SID from the error message if available.

        :param error_message: The error message received from Twilio.
        :return: The execution SID or None if not found.
        """
        pattern = r"Execution (\w+)"
        search = re.search(pattern, error_message)
        if search:
            return search.group(1)
        return None

    def stop_execution(self, flow_type: str, input_params: FlowInputData, execution_sid: str) -> str:
        client = Client(input_params.account_sid, input_params.auth_token)
        flow_sid = self.get_flow_sid(input_params.account_sid, flow_type)
        flow = client.studio.v2.flows(flow_sid).executions.get(execution_sid).update(ExecutionInstance.Status.ENDED)
        return flow.sid

    def load_flows(self, file_path: str) -> List[Flow]:
        """
        Load and parse Twilio flows from a JSON file, ensuring required flow types are present.

        :param file_path: Path to the JSON file.
        :return: List of Flow instances.
        :raises MissingFlowsException: If any required flow type is missing.
        """
        with open(file_path, 'r') as file:
            data = json.load(file)

        flows = []
        for account_sid, details in data.items():
            flow_dict: Dict[str, str] = {flow["type"]: flow["sid"] for flow in details.get("flows", [])}
            missing_types = self.REQUIRED_FLOW_TYPES - flow_dict.keys()
            if missing_types:
                raise MissingFlowsException(account_sid, list(missing_types))

            for flow_type, sid in flow_dict.items():
                flows.append(Flow(account_sid, flow_type, sid))

        return flows

    def get_flow_sid(self, account_sid: str, flow_type: str) -> str:
        """
        Retrieve the SID of a specific flow type for a given account.

        :param account_sid: The account SID to search for.
        :param flow_type: The type of flow to retrieve (e.g., "enrollment_intro").
        :return: The SID of the requested flow.
        :raises FlowNotFoundException: If the flow type is not found for the account.
        """
        for flow in self.flows:
            if flow.account_sid == account_sid and flow.flow_type == flow_type:
                return flow.sid
        raise FlowNotFoundException(account_sid, flow_type)
