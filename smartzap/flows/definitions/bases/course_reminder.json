{"description": "Course Reminder", "states": [{"name": "Course Reminder", "type": "trigger", "transitions": [{"event": "incomingMessage"}, {"event": "incomingCall"}, {"event": "incomingConversationMessage"}, {"next": "set_variables", "event": "incomingRequest"}, {"event": "incomingParent"}], "properties": {"offset": {"x": 0, "y": 0}}}, {"name": "set_variables", "type": "set-variables", "transitions": [{"next": "split_based_in_course_allow_drop_out", "event": "next"}], "properties": {"variables": [{"type": "string", "value": "https://learning-platform-api-stage.keepsdev.com/smartzap", "key": "smartzap_api"}, {"type": "string", "value": "58da72c239014320afb438d12173bfdf", "key": "smartzap_api_key"}, {"type": "string", "value": "{{flow.data.course_allow_drop_out }}", "key": "course_allow_drop_out"}, {"type": "string", "value": "{{flow.data.user_name}}", "key": "user_name"}, {"type": "string", "value": "{{flow.data.course_name}}", "key": "course_name"}], "offset": {"x": 450, "y": 400}}}, {"name": "split_based_in_course_allow_drop_out", "type": "split-based-on", "transitions": [{"next": "set_reminder_template_sid", "event": "noMatch"}, {"next": "set_reminder_with_give_up_template_sid", "event": "match", "conditions": [{"friendly_name": "If value equal_to true", "arguments": ["{{flow.variables.course_allow_drop_out}}"], "type": "equal_to", "value": "true"}]}], "properties": {"input": "{{flow.variables.course_allow_drop_out}}", "offset": {"x": 450, "y": 800}}}, {"name": "set_reminder_with_give_up_template_sid", "type": "set-variables", "transitions": [{"next": "send_message", "event": "next"}], "properties": {"variables": [{"type": "string", "value": "HXa6036971cf1b126f45419ce86ca626be", "key": "message_template_sid"}], "offset": {"x": 680, "y": 1150}}}, {"name": "set_reminder_template_sid", "type": "set-variables", "transitions": [{"next": "send_message", "event": "next"}], "properties": {"variables": [{"type": "string", "value": "HXa6036971cf1b126f45419ce86ca626be", "key": "message_template_sid"}], "offset": {"x": 290, "y": 1160}}}, {"name": "send_message", "type": "send-message", "transitions": [{"event": "sent"}, {"event": "failed"}], "properties": {"offset": {"x": 530, "y": 1560}, "content_variables": [{"value": "{{flow.variables.user_name}}", "key": "1"}, {"value": "{{flow.variables.course_name}}", "key": "2"}], "service": "{{trigger.message.InstanceSid}}", "channel": "{{trigger.message.ChannelSid}}", "from": "{{flow.channel.address}}", "message_type": "content_template", "to": "{{contact.channel.address}}", "content_sid": "{{flow.variables.message_template_sid}}"}}], "initial_state": "<PERSON><PERSON>", "flags": {"allow_concurrent_calls": true}}