{"description": "Give Up Flow", "states": [{"name": "<PERSON><PERSON>", "type": "trigger", "transitions": [{"event": "incomingMessage"}, {"event": "incomingCall"}, {"event": "incomingConversationMessage"}, {"event": "incomingRequest"}, {"next": "set_variables", "event": "incomingParent"}], "properties": {"offset": {"x": 0, "y": 0}}}, {"name": "set_variables", "type": "set-variables", "transitions": [{"next": "http_give_up", "event": "next"}], "properties": {"variables": [{"type": "string", "value": "https://learning-platform-api-stage.keepsdev.com/smartzap", "key": "smartzap_api"}, {"type": "string", "value": "58da72c239014320afb438d12173bfdf", "key": "smartzap_api_key"}], "offset": {"x": 40, "y": 400}}}, {"name": "http_give_up", "type": "make-http-request", "transitions": [{"next": "split_based_on_http_response", "event": "success"}, {"next": "send_failed_message", "event": "failed"}], "properties": {"offset": {"x": 210, "y": 750}, "method": "POST", "content_type": "application/json;charset=utf-8", "add_twilio_auth": false, "body": "{\"phone\":  \"{{contact.channel.address}} \"\n}", "url": "{{flow.variables.smartzap_api}}/api/v1/twilio/give-up?api-key={{flow.variables.smartzap_api_key}}"}}, {"name": "send_success_message", "type": "send-message", "transitions": [{"event": "sent"}, {"event": "failed"}], "properties": {"offset": {"x": -30, "y": 1580}, "content_variables": [{"value": "{{widgets.http_give_up.parsed.course_name}}", "key": "1"}], "service": "{{trigger.message.InstanceSid}}", "channel": "{{trigger.message.ChannelSid}}", "from": "{{flow.channel.address}}", "message_type": "content_template", "to": "{{contact.channel.address}}", "content_sid": "HX38950cd32f98a2f6b91289342261a1fc"}}, {"name": "send_not_enrollment_found", "type": "send-message", "transitions": [{"event": "sent"}, {"event": "failed"}], "properties": {"offset": {"x": 330, "y": 1570}, "content_variables": [{"value": "{{flow.variables.course_name}}", "key": "1"}], "service": "{{trigger.message.InstanceSid}}", "channel": "{{trigger.message.ChannelSid}}", "from": "{{flow.channel.address}}", "message_type": "custom", "to": "{{contact.channel.address}}", "content_sid": "HX38950cd32f98aa Você não possui nenhuma matrícula em progresso. 2f6b91289342261a1fc", "body": "Você não possui nenhuma matrícula em progresso."}}, {"name": "split_based_on_http_response", "type": "split-based-on", "transitions": [{"next": "send_failed_message", "event": "noMatch"}, {"next": "send_success_message", "event": "match", "conditions": [{"friendly_name": "If value equal_to success", "arguments": ["{{widgets.http_give_up.parsed.bot_status}}"], "type": "equal_to", "value": "success"}]}, {"next": "send_not_enrollment_found", "event": "match", "conditions": [{"friendly_name": "If value equal_to no_enrollment", "arguments": ["{{widgets.http_give_up.parsed.bot_status}}"], "type": "equal_to", "value": "no_enrollment"}]}], "properties": {"input": "{{widgets.http_give_up.parsed.bot_status}}", "offset": {"x": 140, "y": 1090}}}, {"name": "send_failed_message", "type": "send-message", "transitions": [{"event": "sent"}, {"event": "failed"}], "properties": {"offset": {"x": -560, "y": 1170}, "service": "{{trigger.message.InstanceSid}}", "channel": "{{trigger.message.ChannelSid}}", "from": "{{flow.channel.address}}", "message_type": "content_template", "to": "{{contact.channel.address}}", "content_sid": "HXed82260353b1fd8e865c2ffa64177795", "body": "Você não possui nenhuma matrícula em progresso."}}], "initial_state": "<PERSON><PERSON>", "flags": {"allow_concurrent_calls": true}}