{"description": "Enrollment Intro Flow", "flags": {"allow_concurrent_calls": true}, "initial_state": "<PERSON><PERSON>", "states": [{"name": "<PERSON><PERSON>", "properties": {"offset": {"x": -1060, "y": -140}}, "transitions": [{"event": "incomingMessage", "next": "check_message_input"}, {"event": "incomingCall"}, {"event": "incomingConversationMessage"}, {"event": "incomingRequest", "next": "set_variables"}, {"event": "incomingParent"}], "type": "trigger"}, {"name": "send_course_introduction_message", "properties": {"channel": "{{trigger.message.ChannelSid}}", "content_sid": "HX8eff8640770e230ea75efafb697a1d9e", "content_variables": [{"key": "1", "value": "{{flow.variables.user_name}}"}, {"key": "2", "value": "{{flow.variables.course_name}}"}, {"key": "3", "value": "{{flow.variables.workspace_name}}"}], "from": "{{flow.channel.address}}", "message_type": "content_template", "offset": {"x": -600, "y": 420}, "service": "{{trigger.message.InstanceSid}}", "timeout": "86400"}, "transitions": [{"event": "incomingMessage", "next": "check_course_introduction_user_response"}, {"event": "timeout"}, {"event": "deliveryFailure"}], "type": "send-and-wait-for-reply"}, {"name": "check_course_introduction_user_response", "properties": {"input": "{{widgets.send_course_introduction_message.inbound.Body}}", "offset": {"x": 90, "y": 700}}, "transitions": [{"event": "noMatch"}, {"conditions": [{"arguments": ["{{widgets.send_course_introduction_message.inbound.Body}}"], "friendly_name": "If value matches_any_of \"sim, quero continuar\", \"si, quiero continuar\"", "type": "equal_to", "value": "sim, quero continuar"}], "event": "match", "next": "set_enrollment_accept_to_true"}, {"conditions": [{"arguments": ["{{widgets.send_course_introduction_message.inbound.Body}}"], "friendly_name": "If value equal_to não", "type": "equal_to", "value": "não"}], "event": "match", "next": "set_enrollment_accept_to_false"}], "type": "split-based-on"}, {"name": "send_course_welcome_message", "properties": {"channel": "{{trigger.message.ChannelSid}}", "content_sid": "HX5913a36bb45be4b95042a2720679861f", "content_variables": [{"key": "1", "value": "{{widgets.http_enrollment_confirmation.parsed.course.description}}"}], "from": "{{flow.channel.address}}", "message_type": "content_template", "offset": {"x": -190, "y": 1820}, "service": "{{trigger.message.InstanceSid}}", "timeout": "86400"}, "transitions": [{"event": "incomingMessage", "next": "set_user_response_by_reply"}, {"event": "timeout"}, {"event": "deliveryFailure"}], "type": "send-and-wait-for-reply"}, {"name": "check_message_input", "properties": {"input": "{{trigger.message.Body}}", "offset": {"x": -2090, "y": 370}}, "transitions": [{"event": "noMatch"}, {"conditions": [{"arguments": ["{{trigger.message.Body}}"], "friendly_name": "If value equal_to <PERSON><PERSON> o conteúdo já", "type": "equal_to", "value": "Quero o conteúdo já"}], "event": "match", "next": "run_smartzap_content_send_flow"}, {"conditions": [{"arguments": ["{{trigger.message.Body}}"], "friendly_name": "If value equal_to <PERSON>r<PERSON><PERSON><PERSON>", "type": "equal_to", "value": "Próximo con<PERSON>"}], "event": "match", "next": "run_smartzap_content_send_flow"}, {"conditions": [{"arguments": ["{{trigger.message.Body}}"], "friendly_name": "If value equal_to sim, quero continuar", "type": "equal_to", "value": "sim, quero continuar"}], "event": "match", "next": "set_enrollment_accept_to_true"}, {"conditions": [{"arguments": ["{{trigger.message.Body}}"], "friendly_name": "If value equal_to <PERSON><PERSON><PERSON>r<PERSON>", "type": "equal_to", "value": "<PERSON><PERSON><PERSON>"}], "event": "match", "next": "run_smartzap_course_finish_flow"}, {"conditions": [{"arguments": ["{{trigger.message.Body}}"], "friendly_name": "If value contains conteúdo", "type": "contains", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "event": "match", "next": "run_smartzap_content_send_flow"}, {"conditions": [{"arguments": ["{{trigger.message.Body}}"], "friendly_name": "If value equal_to <PERSON><PERSON><PERSON> con<PERSON>do", "type": "equal_to", "value": "<PERSON><PERSON><PERSON> con<PERSON>"}], "event": "match", "next": "run_smartzap_content_send_flow"}], "type": "split-based-on"}, {"name": "run_smartzap_content_send_flow", "properties": {"flow_revision": "LatestPublished", "flow_sid": "FW21f74faefba0c09c65bfb286a7074354", "offset": {"x": -410, "y": 2960}}, "transitions": [{"event": "completed"}, {"event": "failed"}], "type": "run-subflow"}, {"name": "send_message_1", "properties": {"body": "Nao consegui entender sua resposta", "channel": "{{trigger.message.ChannelSid}}", "from": "{{flow.channel.address}}", "message_type": "custom", "offset": {"x": 270, "y": 2970}, "service": "{{trigger.message.InstanceSid}}", "to": "{{contact.channel.address}}"}, "transitions": [{"event": "sent"}, {"event": "failed"}], "type": "send-message"}, {"name": "set_user_response_by_reply", "properties": {"offset": {"x": 230, "y": 2040}, "variables": [{"key": "response_message", "type": "string", "value": "{{widgets.send_course_welcome_message.inbound.Body}}"}]}, "transitions": [{"event": "next", "next": "check_user_course_welcome_response"}], "type": "set-variables"}, {"name": "check_user_course_welcome_response", "properties": {"input": "{{flow.variables.response_message}}", "offset": {"x": 240, "y": 2400}}, "transitions": [{"event": "noMatch", "next": "send_message_1"}, {"conditions": [{"arguments": ["{{flow.variables.response_message}}"], "friendly_name": "If value equal_to <PERSON>r<PERSON><PERSON><PERSON>", "type": "equal_to", "value": "<PERSON><PERSON><PERSON> con<PERSON>"}], "event": "match", "next": "run_smartzap_content_send_flow"}, {"conditions": [{"arguments": ["{{flow.variables.response_message}}"], "friendly_name": "If value equal_to con<PERSON><PERSON><PERSON>", "type": "contains", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "event": "match", "next": "run_smartzap_content_send_flow"}], "type": "split-based-on"}, {"name": "set_variables", "properties": {"offset": {"x": -610, "y": 180}, "variables": [{"key": "user_name", "type": "string", "value": "{{flow.data.user_name}}"}, {"key": "workspace_name", "type": "string", "value": "{{flow.data.company_name}}"}, {"key": "course_name", "type": "string", "value": "{{flow.data.course_name}}"}, {"key": "workspace_user_token_expiration", "type": "string", "value": "{{flow.data. workspace_user_token_expiration}}"}, {"key": "smartzap_api", "type": "string", "value": "https://learning-platform-api-stage.keepsdev.com/smartzap"}]}, "transitions": [{"event": "next", "next": "send_course_introduction_message"}], "type": "set-variables"}, {"name": "http_enrollment_confirmation", "properties": {"add_twilio_auth": false, "body": "{\n\"from\":  \"{{contact.channel.address}}\",\n\"accept\": {{flow.variables.enrollment_accept}} \n}", "content_type": "application/json;charset=utf-8", "method": "POST", "offset": {"x": 280, "y": 1430}, "url": "https://learning-platform-api-stage.keepsdev.com/smartzap/api/v1/twilio/enrollment-confirmation?api-key=58da72c239014320afb438d12173bfdf"}, "transitions": [{"event": "success", "next": "check_enrollment_confirmation_status"}, {"event": "failed"}], "type": "make-http-request"}, {"name": "set_enrollment_accept_to_true", "properties": {"offset": {"x": -10, "y": 1090}, "variables": [{"key": "enrollment_accept", "type": "string", "value": "true"}]}, "transitions": [{"event": "next", "next": "http_enrollment_confirmation"}], "type": "set-variables"}, {"name": "set_enrollment_accept_to_false", "properties": {"offset": {"x": 420, "y": 1090}, "variables": [{"key": "enrollment_accept", "type": "string", "value": "false"}]}, "transitions": [{"event": "next", "next": "http_enrollment_confirmation"}], "type": "set-variables"}, {"name": "check_enrollment_confirmation_status", "properties": {"input": "{{widgets.http_enrollment_confirmation.parsed.bot_status}}", "offset": {"x": 680, "y": 1680}}, "transitions": [{"event": "noMatch"}, {"conditions": [{"arguments": ["{{widgets.http_enrollment_confirmation.parsed.bot_status}}"], "friendly_name": "If value equal_to enrollment_confirmed", "type": "equal_to", "value": "enrollment_confirmed"}], "event": "match", "next": "send_course_welcome_message"}, {"conditions": [{"arguments": ["{{widgets.http_enrollment_confirmation.parsed.bot_status}}"], "friendly_name": "If value equal_to enrollment_rejected", "type": "equal_to", "value": "enrollment_rejected"}], "event": "match", "next": "send_enrolment_already_reject_message"}, {"conditions": [{"arguments": ["{{widgets.http_enrollment_confirmation.parsed.bot_status}}"], "friendly_name": "If value equal_to enrollment_already_started", "type": "equal_to", "value": "enrollment_already_started"}], "event": "match", "next": "send_enrollment_already_started_message"}, {"conditions": [{"arguments": ["{{widgets.http_enrollment_confirmation.parsed.bot_status}}"], "friendly_name": "If value equal_to no_enrollment", "type": "equal_to", "value": "no_enrollment"}], "event": "match", "next": "send_no_enrollment_found_message"}], "type": "split-based-on"}, {"name": "run_smartzap_course_finish_flow", "properties": {"flow_revision": "LatestPublished", "flow_sid": "FW03473bdf9e98b12f0ab40cae2e6a0f62", "offset": {"x": -1410, "y": 980}}, "transitions": [{"event": "completed"}, {"event": "failed"}], "type": "run-subflow"}, {"name": "send_enrollment_already_started_message", "properties": {"body": "HXe2f6875fe1ac1f60f661701da76ec98d", "channel": "{{trigger.message.ChannelSid}}", "content_sid": "HXe2f6875fe1ac1f60f661701da76ec98d", "from": "{{flow.channel.address}}", "message_type": "content_template", "offset": {"x": 1230, "y": 2070}, "service": "{{trigger.message.InstanceSid}}", "to": "{{contact.channel.address}}"}, "transitions": [{"event": "sent"}, {"event": "failed"}], "type": "send-message"}, {"name": "send_no_enrollment_found_message", "properties": {"body": "Você não possui nenhuma matrícula em progresso", "channel": "{{trigger.message.ChannelSid}}", "content_sid": "HXe2f6875fe1ac1f60f661701da76ec98d", "from": "{{flow.channel.address}}", "message_type": "custom", "offset": {"x": 1650, "y": 2060}, "service": "{{trigger.message.InstanceSid}}", "to": "{{contact.channel.address}}"}, "transitions": [{"event": "sent"}, {"event": "failed"}], "type": "send-message"}, {"name": "send_enrolment_already_reject_message", "properties": {"body": "Você já rejeitou essa matrícula, infelizmente não é possível se rematrícular.", "channel": "{{trigger.message.ChannelSid}}", "content_sid": "HXe2f6875fe1ac1f60f661701da76ec98d", "from": "{{flow.channel.address}}", "message_type": "custom", "offset": {"x": 830, "y": 2050}, "service": "{{trigger.message.InstanceSid}}", "to": "{{contact.channel.address}}"}, "transitions": [{"event": "sent"}, {"event": "failed"}], "type": "send-message"}]}