{"description": "A New Flow", "states": [{"name": "<PERSON><PERSON>", "type": "trigger", "transitions": [{"event": "incomingMessage"}, {"event": "incomingCall"}, {"event": "incomingConversationMessage"}, {"next": "set_variables_by_http", "event": "incomingRequest"}, {"next": "set_user_response_by_subflow", "event": "incomingParent"}], "properties": {"offset": {"x": -530, "y": 240}}}, {"name": "set_variables_by_http", "type": "set-variables", "transitions": [{"next": "send_enrollment_course_nps_message", "event": "next"}], "properties": {"variables": [{"type": "string", "value": "{{flow.data.course_name}}", "key": "course_name"}, {"type": "string", "value": "{{flow.data.user_name}}", "key": "user_name"}], "offset": {"x": 510, "y": 390}}}, {"name": "send_enrollment_course_nps_message", "type": "send-and-wait-for-reply", "transitions": [{"next": "set_user_response", "event": "incomingMessage"}, {"event": "timeout"}, {"event": "deliveryFailure"}], "properties": {"offset": {"x": 750, "y": 720}, "content_variables": [{"value": "{{flow.variables.user_name}}", "key": "1"}, {"value": "{{flow.variables.course_name}}", "key": "2"}], "service": "{{trigger.message.InstanceSid}}", "channel": "{{trigger.message.ChannelSid}}", "from": "{{flow.channel.address}}", "message_type": "content_template", "content_sid": "HXfba0f69d0e82b60f005c81104d704481", "timeout": "1"}}, {"name": "split_based_in_user_response", "type": "split-based-on", "transitions": [{"next": "send_invalid_response_message", "event": "noMatch"}, {"next": "http_register_course_nps_response", "event": "match", "conditions": [{"friendly_name": "If value regex ^(10|[1-9])$", "arguments": ["{{flow.variables.user_response}}"], "type": "regex", "value": "^(10|[1-9])$"}]}], "properties": {"input": "{{flow.variables.user_response}}", "offset": {"x": 620, "y": 1690}}}, {"name": "set_user_response", "type": "set-variables", "transitions": [{"next": "set_api_variables", "event": "next"}], "properties": {"variables": [{"type": "string", "value": "{{widgets.send_course_welcome_message.inbound.Body}}", "key": "user_response"}], "offset": {"x": 600, "y": 1050}}}, {"name": "send_invalid_response_message", "type": "send-and-wait-for-reply", "transitions": [{"next": "set_user_response_by_invalid_response_message_reply", "event": "incomingMessage"}, {"event": "timeout"}, {"event": "deliveryFailure"}], "properties": {"offset": {"x": 100, "y": 2040}, "content_variables": [{"value": "{{flow.variables.user_name}}", "key": "0"}, {"value": "{{flow.variables.course_name}}", "key": "1"}], "service": "{{trigger.message.InstanceSid}}", "channel": "{{trigger.message.ChannelSid}}", "from": "{{flow.channel.address}}", "message_type": "custom", "content_sid": "HXfba0f69d0e82b60f005c81104d704481", "body": "Não consegui entender sua resposta, para avaliar o curso você precisar digitar um número de 1 á 10, exemplo:\n*8*\n\nCaso você não deseje avaliar esse curso, é possível apenas ignorar essa mensagem.", "timeout": "1"}}, {"name": "set_user_response_by_invalid_response_message_reply", "type": "set-variables", "transitions": [{"next": "split_based_in_user_response", "event": "next"}], "properties": {"variables": [{"type": "string", "value": "{{widgets.send_invalid_response_message.inbound.Body}}", "key": "user_response"}], "offset": {"x": -180, "y": 1460}}}, {"name": "http_register_course_nps_response", "type": "make-http-request", "transitions": [{"next": "split_based_in_response_status", "event": "success"}, {"next": "send_error_message", "event": "failed"}], "properties": {"offset": {"x": 790, "y": 2080}, "method": "POST", "content_type": "application/json;charset=utf-8", "add_twilio_auth": false, "body": "{\n\"score\":   {{flow.variables.user_response}},\n\"phone\":  \"{{contact.channel.address}} \"\n}", "url": "{{flow.variables.smartzap_api}}/api/v1/twilio/register-enrollment-nps-score?api-key={{flow.variables.smartzap_token}}"}}, {"name": "set_user_response_by_subflow", "type": "set-variables", "transitions": [{"next": "set_api_variables", "event": "next"}], "properties": {"variables": [{"type": "string", "value": "{{trigger.message.Body}}", "key": "user_response"}], "offset": {"x": 220, "y": 1060}}}, {"name": "set_api_variables", "type": "set-variables", "transitions": [{"next": "split_based_in_user_response", "event": "next"}], "properties": {"variables": [{"type": "string", "value": "https://learning-platform-api-stage.keepsdev.com/smartzap", "key": "smartzap_api"}, {"type": "string", "value": "58da72c239014320afb438d12173bfdf", "key": "smartzap_token"}], "offset": {"x": 610, "y": 1380}}}, {"name": "send_error_message", "type": "send-message", "transitions": [{"event": "sent"}, {"event": "failed"}], "properties": {"offset": {"x": 930, "y": 2440}, "service": "{{trigger.message.InstanceSid}}", "channel": "{{trigger.message.ChannelSid}}", "from": "{{flow.channel.address}}", "message_type": "custom", "to": "{{contact.channel.address}}", "body": "Algo não saiu como esperado. 😕 Não se preocupe, estamos cuidando disso. Tente novamente mais tarde ou, se precisar, nosso suporte está aqui para te ajudar! 😊"}}, {"name": "send_success_message", "type": "send-message", "transitions": [{"event": "sent"}, {"event": "failed"}], "properties": {"offset": {"x": -410, "y": 2860}, "service": "{{trigger.message.InstanceSid}}", "channel": "{{trigger.message.ChannelSid}}", "from": "{{flow.channel.address}}", "message_type": "custom", "to": "{{contact.channel.address}}", "body": "Agradecemos sua avaliação! Sua opinião é essencial para melhorarmos continuamente."}}, {"name": "split_based_in_response_status", "type": "split-based-on", "transitions": [{"event": "noMatch"}, {"next": "send_success_message", "event": "match", "conditions": [{"friendly_name": "success", "arguments": ["{{widgets.http_register_course_nps_response.parsed.bot_status}}"], "type": "equal_to", "value": "success"}]}, {"next": "send_no_completed_enrollment_message", "event": "match", "conditions": [{"friendly_name": "no_completed_enrollment", "arguments": ["{{widgets.http_register_course_nps_response.parsed.bot_status}}"], "type": "equal_to", "value": "no_completed_enrollment"}]}, {"next": "send_no_feedback_already_registered_message", "event": "match", "conditions": [{"friendly_name": "feedback_already_registered", "arguments": ["{{widgets.http_register_course_nps_response.parsed.bot_status}}"], "type": "equal_to", "value": "feedback_already_registered"}]}, {"next": "send_course_not_accepting_nps_survey_message", "event": "match", "conditions": [{"friendly_name": "course_not_accepting_nps_survey", "arguments": ["{{widgets.http_register_course_nps_response.parsed.bot_status}}"], "type": "equal_to", "value": "course_not_accepting_nps_survey"}]}], "properties": {"input": "{{widgets.http_register_course_nps_response.parsed.bot_status}}", "offset": {"x": -10, "y": 2450}}}, {"name": "send_no_completed_enrollment_message", "type": "send-message", "transitions": [{"event": "sent"}, {"event": "failed"}], "properties": {"offset": {"x": -90, "y": 2930}, "service": "{{trigger.message.InstanceSid}}", "channel": "{{trigger.message.ChannelSid}}", "from": "{{flow.channel.address}}", "message_type": "custom", "to": "{{contact.channel.address}}", "body": "Você ainda não possui nenhuma matrícula concluída para avaliar. Continue seus estudos e, ao finalizar um curso, teremos o prazer de receber sua avaliação!"}}, {"name": "send_no_feedback_already_registered_message", "type": "send-message", "transitions": [{"event": "sent"}, {"event": "failed"}], "properties": {"offset": {"x": 230, "y": 3030}, "service": "{{trigger.message.InstanceSid}}", "channel": "{{trigger.message.ChannelSid}}", "from": "{{flow.channel.address}}", "message_type": "custom", "to": "{{contact.channel.address}}", "body": "Você já avaliou o curso da sua última matrícula concluída. Agradecemos sua participação! Assim que completar novos cursos, ficaremos felizes em receber sua opinião novamente."}}, {"name": "send_course_not_accepting_nps_survey_message", "type": "send-message", "transitions": [{"event": "sent"}, {"event": "failed"}], "properties": {"offset": {"x": 560, "y": 3080}, "service": "{{trigger.message.InstanceSid}}", "channel": "{{trigger.message.ChannelSid}}", "from": "{{flow.channel.address}}", "message_type": "custom", "to": "{{contact.channel.address}}", "body": "Este curso não está aceitando avaliações no momento. Agradecemos seu interesse e estamos sempre disponíveis para ouvir suas sugestões!"}}], "initial_state": "<PERSON><PERSON>", "flags": {"allow_concurrent_calls": true}}