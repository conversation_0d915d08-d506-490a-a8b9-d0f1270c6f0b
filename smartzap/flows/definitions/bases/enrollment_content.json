{"description": "Enrollment Content Flow", "flags": {"allow_concurrent_calls": true}, "initial_state": "<PERSON><PERSON>", "states": [{"name": "<PERSON><PERSON>", "properties": {"offset": {"x": -490, "y": -200}}, "transitions": [{"event": "incomingMessage"}, {"event": "incomingCall"}, {"event": "incomingConversationMessage"}, {"event": "incomingRequest", "next": "set_user_phone_by_api"}, {"event": "incomingParent", "next": "set_user_phone_by_contact"}], "type": "trigger"}, {"name": "get_next_content_data", "properties": {"add_twilio_auth": false, "body": "{\"From\": \"{{flow.variables.user_phone}}\", \"ExecutionId\": \"{{flow.sid}}\" }", "content_type": "application/json;charset=utf-8", "method": "POST", "offset": {"x": 560, "y": 710}, "url": "{{flow.variables.smartzap_api}}/api/v1/twilio/next-content?api-key=58da72c239014320afb438d12173bfdf"}, "transitions": [{"event": "success", "next": "split_based_in_response_status"}, {"event": "failed", "next": "send_error_message"}], "type": "make-http-request"}, {"name": "set_token", "properties": {"offset": {"x": 140, "y": 460}, "variables": [{"key": "token", "type": "string", "value": "58da72c239014320afb438d12173bfdf"}, {"key": "smartzap_api", "type": "string", "value": "https://learning-platform-api-stage.keepsdev.com/smartzap"}]}, "transitions": [{"event": "next", "next": "get_next_content_data"}], "type": "set-variables"}, {"name": "set_global_variables", "properties": {"offset": {"x": 1010, "y": 1540}, "variables": [{"key": "user_name", "type": "string", "value": "{{widgets.get_next_content_data.parsed.user_name}}"}, {"key": "content_title", "type": "string", "value": "{{widgets.get_next_content_data.parsed.content_title}}"}, {"key": "content_description", "type": "string", "value": "{{widgets.get_next_content_data.parsed.content_description}}"}, {"key": "content_link", "type": "string", "value": "{{widgets.get_next_content_data.parsed.content_link}}"}, {"key": "user_token_expiration", "type": "string", "value": "{{widgets.get_next_content_data.parsed.token_expiration}}"}, {"key": "is_content_embed", "type": "string", "value": "{{widgets.get_next_content_data.parsed.is_content_embed}}"}]}, "transitions": [{"event": "next", "next": "is_content_embed"}], "type": "set-variables"}, {"name": "send_content_message", "properties": {"channel": "{{trigger.message.ChannelSid}}", "content_sid": "HX2cc4ba78b811f00086fa355242cf85f3", "content_variables": [{"key": "1", "value": "{{flow.variables.user_name}}"}, {"key": "2", "value": "{{flow.variables.content_title}}"}, {"key": "3", "value": "{{flow.variables.content_description}}"}, {"key": "4", "value": "{{flow.variables.content_link}}"}, {"key": "5", "value": "{{flow.variables.user_token_expiration}}"}], "from": "{{flow.channel.address}}", "message_type": "content_template", "offset": {"x": 800, "y": 2740}, "service": "{{trigger.message.InstanceSid}}", "to": "{{contact.channel.address}}"}, "transitions": [{"event": "sent"}, {"event": "failed", "next": "send_error_message"}], "type": "send-message"}, {"name": "split_based_in_response_status", "properties": {"input": "{{widgets.get_next_content_data.parsed.status}}", "offset": {"x": 720, "y": 1060}}, "transitions": [{"event": "noMatch"}, {"conditions": [{"arguments": ["{{widgets.get_next_content_data.parsed.status}}"], "friendly_name": "If value equal_to no_enrollment", "type": "equal_to", "value": "no_enrollment"}], "event": "match", "next": "send_no_enrollment_found_message"}, {"conditions": [{"arguments": ["{{widgets.get_next_content_data.parsed.status}}"], "friendly_name": "If value equal_to anticipated", "type": "equal_to", "value": "anticipated"}], "event": "match", "next": "set_global_variables"}, {"conditions": [{"arguments": ["{{widgets.get_next_content_data.parsed.status}}"], "friendly_name": "If value equal_to waiting_end", "type": "equal_to", "value": "waiting_end"}], "event": "match", "next": "send_content_nothing_pending_message"}], "type": "split-based-on"}, {"name": "send_no_enrollment_found_message", "properties": {"body": "Você não possui nenhuma matrícula em progresso.", "channel": "{{trigger.message.ChannelSid}}", "content_sid": "HX2cc4ba78b811f00086fa355242cf85f3", "content_variables": [{"key": "1", "value": "{{flow.variables.user_name}}"}, {"key": "2", "value": "{{flow.variables.content_title}}"}, {"key": "3", "value": "{{flow.variables.content_description}}"}, {"key": "4", "value": "{{flow.variables.content_link}}"}, {"key": "5", "value": "{{flow.variables.user_token_expiration}}"}], "from": "{{flow.channel.address}}", "message_type": "custom", "offset": {"x": 210, "y": 1370}, "service": "{{trigger.message.InstanceSid}}", "to": "{{contact.channel.address}}"}, "transitions": [{"event": "sent"}, {"event": "failed", "next": "send_error_message"}], "type": "send-message"}, {"name": "send_content_nothing_pending_message", "properties": {"channel": "{{trigger.message.ChannelSid}}", "content_sid": "HX3f916018db9ad3341b71cb00240224f8", "from": "{{flow.channel.address}}", "message_type": "content_template", "offset": {"x": 1620, "y": 1390}, "service": "{{trigger.message.InstanceSid}}", "to": "{{contact.channel.address}}"}, "transitions": [{"event": "sent"}, {"event": "failed", "next": "send_error_message"}], "type": "send-message"}, {"name": "set_user_phone_by_api", "properties": {"offset": {"x": -160, "y": 130}, "variables": [{"key": "user_phone", "type": "string", "value": "{{flow.channel.address}}"}]}, "transitions": [{"event": "next", "next": "set_token"}], "type": "set-variables"}, {"name": "set_user_phone_by_contact", "properties": {"offset": {"x": 270, "y": 130}, "variables": [{"key": "user_phone", "type": "string", "value": "{{contact.channel.address}}"}]}, "transitions": [{"event": "next", "next": "set_token"}], "type": "set-variables"}, {"name": "is_content_embed", "properties": {"input": "{{flow.variables.is_content_embed}}", "offset": {"x": 1090, "y": 2180}}, "transitions": [{"event": "noMatch", "next": "send_content_message"}, {"conditions": [{"arguments": ["{{flow.variables.is_content_embed}}"], "friendly_name": "If value equal_to true", "type": "equal_to", "value": "true"}], "event": "match", "next": "content_link_is_external"}], "type": "split-based-on"}, {"name": "send_content_description_message", "properties": {"channel": "{{trigger.message.ChannelSid}}", "content_sid": "HX5874b4b2186b955b61ed9f40ea785419", "content_variables": [{"key": "1", "value": "{{flow.variables.content_title}}"}, {"key": "2", "value": "{{flow.variables.content_description}}"}], "from": "{{flow.channel.address}}", "message_type": "content_template", "offset": {"x": 1660, "y": 3570}, "service": "{{trigger.message.InstanceSid}}", "to": "{{contact.channel.address}}"}, "transitions": [{"event": "sent"}, {"event": "failed", "next": "send_error_message"}], "type": "send-message"}, {"name": "send_error_message", "properties": {"body": "Algo não saiu como esperado. 😕 Não se preocupe, estamos cuidando disso. Tente novamente mais tarde ou, se precisar, nosso suporte está aqui para te ajudar! 😊", "channel": "{{trigger.message.ChannelSid}}", "content_sid": "HX2cc4ba78b811f00086fa355242cf85f3", "content_variables": [{"key": "1", "value": "{{flow.variables.user_name}}"}, {"key": "2", "value": "{{flow.variables.content_title}}"}, {"key": "3", "value": "{{flow.variables.content_description}}"}, {"key": "4", "value": "{{flow.variables.content_link}}"}, {"key": "5", "value": "{{flow.variables.user_token_expiration}}"}], "from": "{{flow.channel.address}}", "message_type": "custom", "offset": {"x": -240, "y": 2580}, "service": "{{trigger.message.InstanceSid}}", "to": "{{contact.channel.address}}"}, "transitions": [{"event": "sent"}, {"event": "failed"}], "type": "send-message"}, {"name": "send_media_embed", "properties": {"attributes": "{\n \"interactive\": { \"type\": \"quick_reply\", \"action\": [{ \"type\": \"text\", \"label\": \"Próximo conteúdo\", \"payload\": \"Próximo conteúdo\" }] } ,\n\"media_url\": \"{{flow.variables.content_link}} \"\n}", "body": "{{flow.variables.content_name}}", "channel": "{{trigger.message.ChannelSid}}", "content_sid": "HX2cc4ba78b811f00086fa355242cf85f3", "content_variables": [{"key": "1", "value": "{{flow.variables.user_name}}"}, {"key": "2", "value": "{{flow.variables.content_title}}"}, {"key": "3", "value": "{{flow.variables.content_description}}"}, {"key": "4", "value": "{{flow.variables.content_link}}"}, {"key": "5", "value": "{{flow.variables.user_token_expiration}}"}], "from": "{{flow.channel.address}}", "media_url": "{{flow.variables.content_link}}", "message_type": "custom", "offset": {"x": 1460, "y": 3090}, "service": "{{trigger.message.InstanceSid}}", "to": "{{contact.channel.address}}"}, "transitions": [{"event": "sent", "next": "send_content_description_message"}, {"event": "failed", "next": "send_error_message"}], "type": "send-message"}, {"name": "content_link_is_external", "properties": {"input": "{{flow.variables.content_link}}", "offset": {"x": 1750, "y": 2580}}, "transitions": [{"event": "noMatch", "next": "send_media_link"}, {"conditions": [{"arguments": ["{{flow.variables.content_link}}"], "friendly_name": "If value equal_to true", "type": "regex", "value": "https://s3\\.amazonaws\\.com/[a-zA-Z0-9\\-/._]+"}], "event": "match", "next": "send_media_embed"}, {"conditions": [{"arguments": ["{{flow.variables.content_link}}"], "friendly_name": "If value regex |\\b(?:https?://)?(?:www\\.)?vimeo\\.com/([0-9]+)\\b", "type": "regex", "value": "https://contents\\.keepsdev\\.com/[a-zA-Z0-9\\-/._]+"}], "event": "match", "next": "send_media_embed"}, {"conditions": [{"arguments": ["{{flow.variables.content_link}}"], "friendly_name": "If value regex https://contents\\-stage\\.keepsdev\\.com/[a-zA-Z0-9\\-/._]+", "type": "regex", "value": "https://contents\\-stage\\.keepsdev\\.com/[a-zA-Z0-9\\-/._]+"}], "event": "match", "next": "send_media_embed"}], "type": "split-based-on"}, {"name": "send_media_link", "properties": {"attributes": "{\n \"interactive\": { \"type\": \"quick_reply\", \"action\": [{ \"type\": \"text\", \"label\": \"Próximo conteúdo\", \"payload\": \"Próximo conteúdo\" }] } ,\n\"media_url\": \"{{flow.variables.content_link}} \"\n}", "body": "{{flow.variables.content_link}}\n{{flow.variables.empty}}", "channel": "{{trigger.message.ChannelSid}}", "content_sid": "HX2cc4ba78b811f00086fa355242cf85f3", "content_variables": [{"key": "1", "value": "{{flow.variables.user_name}}"}, {"key": "2", "value": "{{flow.variables.content_title}}"}, {"key": "3", "value": "{{flow.variables.content_description}}"}, {"key": "4", "value": "{{flow.variables.content_link}}"}, {"key": "5", "value": "{{flow.variables.user_token_expiration}}"}], "from": "{{flow.channel.address}}", "message_type": "custom", "offset": {"x": 1900, "y": 3090}, "service": "{{trigger.message.InstanceSid}}", "to": "{{contact.channel.address}}"}, "transitions": [{"event": "sent", "next": "send_content_description_message"}, {"event": "failed"}], "type": "send-message"}]}