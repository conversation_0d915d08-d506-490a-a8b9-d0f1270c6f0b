{"description": "Enrollment Finish Flow", "flags": {"allow_concurrent_calls": true}, "initial_state": "<PERSON><PERSON>", "states": [{"name": "<PERSON><PERSON>", "properties": {"offset": {"x": 0, "y": 0}}, "transitions": [{"event": "incomingMessage"}, {"event": "incomingCall"}, {"event": "incomingConversationMessage"}, {"event": "incomingRequest", "next": "send_end_course"}, {"event": "incomingParent", "next": "http_finish_enrollment"}], "type": "trigger"}, {"name": "send_antecipated_message", "properties": {"body": "✅ Você solicitou a finalização do curso! Aguarde.", "channel": "{{trigger.message.ChannelSid}}", "from": "{{flow.channel.address}}", "message_type": "custom", "offset": {"x": 1080, "y": 1160}, "service": "{{trigger.message.InstanceSid}}", "to": "{{contact.channel.address}}"}, "transitions": [{"event": "sent"}, {"event": "failed"}], "type": "send-message"}, {"name": "http_finish_enrollment", "properties": {"add_twilio_auth": false, "body": "{\"from\": \" {{contact.channel.address}}\"}", "content_type": "application/json;charset=utf-8", "method": "POST", "offset": {"x": 610, "y": 380}, "url": "https://learning-platform-api-stage.keepsdev.com/smartzap/api/v1/twilio/finish-enrollment?api-key=58da72c239014320afb438d12173bfdf"}, "transitions": [{"event": "success", "next": "split_1"}, {"event": "failed"}], "type": "make-http-request"}, {"name": "split_1", "properties": {"input": "{{widgets.http_finish_enrollment.parsed.bot_status}}", "offset": {"x": 840, "y": 690}}, "transitions": [{"event": "noMatch", "next": "send_no_enrollment_found"}, {"conditions": [{"arguments": ["{{widgets.http_finish_enrollment.parsed.bot_status}}"], "friendly_name": "If value equal_to anticipated", "type": "equal_to", "value": "anticipated"}], "event": "match", "next": "send_antecipated_message"}, {"conditions": [{"arguments": ["{{widgets.http_finish_enrollment.parsed.bot_status}}"], "friendly_name": "If value equal_to nothing", "type": "equal_to", "value": "nothing"}], "event": "match", "next": "send_content_pending_message"}, {"conditions": [{"arguments": ["{{widgets.http_finish_enrollment.parsed.bot_status}}"], "friendly_name": "If value equal_to content_pending", "type": "equal_to", "value": "content_pending"}], "event": "match", "next": "send_contents_pending_message"}], "type": "split-based-on"}, {"name": "send_content_pending_message", "properties": {"body": "Aparentemente você não possui cursos para antecipar a finalização.", "channel": "{{trigger.message.ChannelSid}}", "from": "{{flow.channel.address}}", "message_type": "custom", "offset": {"x": 1630, "y": 1130}, "service": "{{trigger.message.InstanceSid}}", "to": "{{contact.channel.address}}"}, "transitions": [{"event": "sent"}, {"event": "failed"}], "type": "send-message"}, {"name": "send_contents_pending_message", "properties": {"body": "Você possui conteúdos com envio pendente e por isso não pode antecipar a finalização do curso.", "channel": "{{trigger.message.ChannelSid}}", "from": "{{flow.channel.address}}", "message_type": "custom", "offset": {"x": 1970, "y": 1120}, "service": "{{trigger.message.InstanceSid}}", "to": "{{contact.channel.address}}"}, "transitions": [{"event": "sent"}, {"event": "failed"}], "type": "send-message"}, {"name": "send_no_enrollment_found", "properties": {"body": "Você não possui curso em andamento.", "channel": "{{trigger.message.ChannelSid}}", "from": "{{flow.channel.address}}", "message_type": "custom", "offset": {"x": 510, "y": 1150}, "service": "{{trigger.message.InstanceSid}}", "to": "{{contact.channel.address}}"}, "transitions": [{"event": "sent"}, {"event": "failed"}], "type": "send-message"}, {"name": "send_end_course", "properties": {"body": "🏆 Parabéns {{flow.data.user_name}}, você concluiu o curso!\n\n*Curso*: {{flow.data.course_name}}!\n\n*Duração*: {{flow.data.duration}}\n\n*Pontos*: {{flow.data.learn_points}} / {{flow.data.points}}\n\n*Performance*: {{flow.data.percentage}}", "channel": "{{trigger.message.ChannelSid}}", "from": "{{flow.channel.address}}", "media_url": "{{flow.data.certificate_url}}", "message_type": "custom", "offset": {"x": 190, "y": 400}, "service": "{{trigger.message.InstanceSid}}", "to": "{{contact.channel.address}}"}, "transitions": [{"event": "sent"}, {"event": "failed"}], "type": "send-message"}]}