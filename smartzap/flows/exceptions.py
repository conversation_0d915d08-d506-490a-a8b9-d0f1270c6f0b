from typing import List


class MissingFlowsException(Exception):
    """
    Custom exception raised when required flow types are missing for an account.
    """
    def __init__(self, account_sid: str, missing_types: List[str]):
        self.account_sid = account_sid
        self.missing_types = missing_types
        message = f"Account '{account_sid}' is missing the following flow types: {', '.join(missing_types)}"
        super().__init__(message)


class FlowNotFoundException(Exception):
    """
    Custom exception raised when a specific flow type cannot be found for an account.
    """
    def __init__(self, account_sid: str, flow_type: str):
        self.account_sid = account_sid
        self.flow_type = flow_type
        message = f"Flow of type '{flow_type}' not found for account '{account_sid}'."
        super().__init__(message)
