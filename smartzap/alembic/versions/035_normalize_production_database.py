"""normalize-production-database

Revision ID: 032
Revises: 031
Create Date: 2025-03-14 15:44:47.612338

"""
from alembic import op
import os
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '035'
down_revision = '033'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    """
    This migration should only run once in the production environment (Smartzap DB)
    """
    if os.getenv('ENVIRONMENT') != 'production':
        return
    op.drop_constraint('activity_content_id_fkey', 'activity', type_='foreignkey')
    op.create_foreign_key(None, 'activity', 'content', ['content_id'], ['id'], ondelete='SET NULL')
    op.drop_constraint('content_lesson_id_fkey', 'content', type_='foreignkey')
    op.create_foreign_key(None, 'content', 'lesson', ['lesson_id'], ['id'])
    op.drop_constraint('course_company_company_id_fkey', 'course_workspace', type_='foreignkey')
    op.drop_constraint('course_company_course_id_fkey', 'course_workspace', type_='foreignkey')
    op.create_foreign_key(None, 'course_workspace', 'workspace', ['workspace_id'], ['id'])
    op.create_foreign_key(None, 'course_workspace', 'course', ['course_id'], ['id'])
    op.drop_constraint('enrollment_course_id_fkey', 'enrollment', type_='foreignkey')
    op.drop_constraint('enrollment_current_content_id_fkey', 'enrollment', type_='foreignkey')
    op.drop_constraint('enrollment_user_id_fkey', 'enrollment', type_='foreignkey')
    op.drop_constraint('enrollment_company_id_fkey', 'enrollment', type_='foreignkey')
    op.create_foreign_key(None, 'enrollment', 'user', ['user_id'], ['id'])
    op.create_foreign_key(None, 'enrollment', 'content', ['current_content_id'], ['id'], ondelete='SET NULL')
    op.create_foreign_key(None, 'enrollment', 'course', ['course_id'], ['id'])
    op.create_foreign_key(None, 'enrollment', 'workspace', ['workspace_id'], ['id'])
    op.drop_constraint('feedback_company_id_fkey', 'feedback', type_='foreignkey')
    op.create_foreign_key(None, 'feedback', 'workspace', ['workspace_id'], ['id'])
    op.drop_constraint('lesson_course_id_fkey', 'lesson', type_='foreignkey')
    op.create_foreign_key(None, 'lesson', 'course', ['course_id'], ['id'])
    op.drop_constraint('user_company_user_id_fkey', 'user_workspace', type_='foreignkey')
    op.drop_constraint('user_company_company_id_fkey', 'user_workspace', type_='foreignkey')
    op.create_foreign_key(None, 'user_workspace', 'workspace', ['workspace_id'], ['id'])
    op.create_foreign_key(None, 'user_workspace', 'user', ['user_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    if os.getenv('ENVIRONMENT') != 'production':
        return
    op.drop_constraint(None, 'user_workspace', type_='foreignkey')
    op.drop_constraint(None, 'user_workspace', type_='foreignkey')
    op.create_foreign_key('user_company_company_id_fkey', 'user_workspace', 'workspace', ['workspace_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('user_company_user_id_fkey', 'user_workspace', 'user', ['user_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint(None, 'lesson', type_='foreignkey')
    op.create_foreign_key('lesson_course_id_fkey', 'lesson', 'course', ['course_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint(None, 'feedback', type_='foreignkey')
    op.create_foreign_key('feedback_company_id_fkey', 'feedback', 'workspace', ['workspace_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint(None, 'enrollment', type_='foreignkey')
    op.drop_constraint(None, 'enrollment', type_='foreignkey')
    op.drop_constraint(None, 'enrollment', type_='foreignkey')
    op.drop_constraint(None, 'enrollment', type_='foreignkey')
    op.create_foreign_key('enrollment_company_id_fkey', 'enrollment', 'workspace', ['workspace_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('enrollment_user_id_fkey', 'enrollment', 'user', ['user_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('enrollment_current_content_id_fkey', 'enrollment', 'content', ['current_content_id'], ['id'])
    op.create_foreign_key('enrollment_course_id_fkey', 'enrollment', 'course', ['course_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint(None, 'course_workspace', type_='foreignkey')
    op.drop_constraint(None, 'course_workspace', type_='foreignkey')
    op.create_foreign_key('course_company_course_id_fkey', 'course_workspace', 'course', ['course_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('course_company_company_id_fkey', 'course_workspace', 'workspace', ['workspace_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint(None, 'content', type_='foreignkey')
    op.create_foreign_key('content_lesson_id_fkey', 'content', 'lesson', ['lesson_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint(None, 'activity', type_='foreignkey')
    op.create_foreign_key('activity_content_id_fkey', 'activity', 'content', ['content_id'], ['id'])
    # ### end Alembic commands ###
