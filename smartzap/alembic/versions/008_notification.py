"""notification

Revision ID: 008
Revises: 007
Create Date: 2020-08-04 14:44:00.161210

"""
import datetime

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '008'
down_revision = '007'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table('notification_type',
        sa.Column('id', sa.String(length=36), nullable=False),
        sa.Column('type', sa.String(), nullable=False),
        sa.Column('action', sa.String(), nullable=True),
        sa.Column('image_url', sa.String(), nullable=True),
        sa.Column('created', sa.DateTime(), nullable=True),
        sa.Column('updated', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_table('notification',
        sa.Column('id', sa.String(length=36), nullable=False),
        sa.Column('user_id', sa.String(length=36), nullable=False),
        sa.Column('company_id', sa.String(length=36), nullable=False),
        sa.Column('type_id', sa.String(length=36), nullable=True),
        sa.Column('message', sa.String(), nullable=False),
        sa.Column('content', sa.String(), nullable=True),
        sa.Column('read', sa.Boolean(), nullable=True),
        sa.Column('created', sa.DateTime(), nullable=True),
        sa.Column('updated', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['type_id'], ['notification_type.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['user.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['company_id'], ['company.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    _add_notification_type_data()


def downgrade():
    op.drop_table('notification')
    op.drop_table('notification_type')


def _add_notification_type_data():
    types = [
        {
            'id': 'fa5a0b6e-1725-4a0f-9620-a86c335a6a1a',
            'type': 'Report',
        }
    ]

    now = datetime.datetime.utcnow()
    with op.batch_alter_table('notification_type') as batch_op:
        connection = batch_op.get_bind()
        content_helper = sa.Table('notification_type', sa.MetaData(),
                                  sa.Column('id', sa.String(length=36), nullable=False),
                                  sa.Column('type', sa.String(), nullable=False),
                                  sa.Column('created', sa.DateTime(), nullable=False),
                                  sa.Column('updated', sa.DateTime(), nullable=False),)
        for t in types:
            connection.execute(content_helper.insert().values(id=t['id'], type=t['type'], created=now, updated=now))
