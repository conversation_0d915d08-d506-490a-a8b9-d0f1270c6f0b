"""performance_weights

Revision ID: 010
Revises: 009
Create Date: 2020-09-09 15:42:18.755747

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '010'
down_revision = '009'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('course', sa.Column('content_performance_weight', sa.Integer(), nullable=True))
    op.add_column('course', sa.Column('quiz_performance_weight', sa.Integer(), nullable=True))
    op.execute('UPDATE course SET content_performance_weight = 5, quiz_performance_weight = 5')


def downgrade():
    op.drop_column('course', 'quiz_performance_weight')
    op.drop_column('course', 'content_performance_weight')
