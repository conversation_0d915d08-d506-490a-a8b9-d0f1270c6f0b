"""nps_survey_fields

Revision ID: 027
Revises: 026
Create Date: 2024-12-18 15:16:53.598446

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '028'
down_revision = '027'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('course', sa.Column('allow_nps_survey', sa.<PERSON>(), nullable=True))
    op.add_column('course', sa.Column('nps_score', sa.Integer(), nullable=True))
    op.add_column('enrollment', sa.Column('nps_score', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('enrollment', 'nps_score')
    op.drop_column('course', 'nps_score')
    op.drop_column('course', 'allow_nps_survey')
    # ### end Alembic commands ###
