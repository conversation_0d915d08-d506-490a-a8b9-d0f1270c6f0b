"""notification_error

Revision ID: 011
Revises: 010
Create Date: 2020-09-14 15:56:56.950175

"""
from alembic import op
import sqlalchemy as sa
import datetime


# revision identifiers, used by Alembic.
revision = '012'
down_revision = '011'
branch_labels = None
depends_on = None


def upgrade():
    types = [
        {
            'id': '073a942b-1a0c-4586-98d3-8b2aa7527227',
            'type': 'Course',
            'action': 'Redirect',
        }
    ]

    now = datetime.datetime.utcnow()
    with op.batch_alter_table('notification_type') as batch_op:
        connection = batch_op.get_bind()
        content_helper = sa.Table('notification_type', sa.MetaData(),
                                  sa.Column('id', sa.String(length=36), nullable=False),
                                  sa.Column('type', sa.String(), nullable=False),
                                  sa.Column('action', sa.String(), nullable=False),
                                  sa.Column('created', sa.DateTime(), nullable=False),
                                  sa.Column('updated', sa.DateTime(), nullable=False), )
        for t in types:
            connection.execute(content_helper.insert().values(id=t['id'], type=t['type'], action=t['action'],
                                                              created=now, updated=now))


def downgrade():
    op.execute("DELETE FROM notification_type WHERE id='073a942b-1a0c-4586-98d3-8b2aa7527227'")
