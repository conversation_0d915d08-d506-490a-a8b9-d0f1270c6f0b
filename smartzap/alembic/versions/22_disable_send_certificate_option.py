"""disable_send_certificate

Revision ID: 22
Revises: 21
Create Date: 2023-08-03 10:45:00

"""
from alembic import op
import sqlalchemy as sa


revision = '22'
down_revision = '21'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('course', sa.Column('disable_send_certificate', sa.<PERSON>(), default=False))


def downgrade():
    op.drop_column('course', 'disable_send_certificate')