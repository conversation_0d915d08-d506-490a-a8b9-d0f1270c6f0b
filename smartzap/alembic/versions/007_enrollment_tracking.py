"""enrollment_tracking

Revision ID: 007
Revises: 006
Create Date: 2020-08-03 14:38:24.724310

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '007'
down_revision = '006'
branch_labels = None
depends_on = None


def course_id_for_content(c):
    with op.batch_alter_table('lesson') as batch_op:
        connection = batch_op.get_bind()
        lesson_helper = sa.Table('lesson', sa.MetaData(),
                                 sa.Column('id', sa.String(length=36), nullable=False),
                                 sa.Column('course_id', sa.String(length=36), nullable=False), )
        lessons = connection.execute(lesson_helper.select().where(lesson_helper.c.id == c[1]))
        for l in lessons:
            return l[1]
    return ''


def is_enrollment_for_activity(e, a):
    with op.batch_alter_table('content') as batch_op:
        connection = batch_op.get_bind()
        content_helper = sa.Table('content', sa.MetaData(),
                                  sa.Column('id', sa.String(length=36), nullable=False),
                                  sa.Column('lesson_id', sa.String(length=36), nullable=False),)
        contents = connection.execute(content_helper.select().where(content_helper.c.id == a[3]))
        for c in contents:
            course_id = course_id_for_content(c)
            return e[2] == course_id
    return False


def load_enrollment_id(a):
    with op.batch_alter_table('enrollment') as batch_op:
        connection = batch_op.get_bind()
        enrollment_helper = sa.Table('enrollment', sa.MetaData(),
                                     sa.Column('id', sa.String(length=36), nullable=False),
                                     sa.Column('user_id', sa.String(length=36), nullable=False),
                                     sa.Column('course_id', sa.String(length=36), nullable=True),)
        enrollments = connection.execute(enrollment_helper.select().where(enrollment_helper.c.user_id == a[1]))
        if enrollments.rowcount == 1:
            for e in enrollments:
                return e[0]

        for e in enrollments:
            if is_enrollment_for_activity(e, a):
                return e[0]
    print(a)
    return 'd0f52460-ccf5-4b31-908a-763652a50ebc'


def update_enrollment_ids():
    with op.batch_alter_table('activity') as batch_op:
        connection = batch_op.get_bind()
        activity_helper = sa.Table('activity', sa.MetaData(),
                                   sa.Column('id', sa.String(length=36), nullable=False),
                                   sa.Column('user_id', sa.String(length=36), nullable=False),
                                   sa.Column('enrollment_id', sa.String(length=36), nullable=True),
                                   sa.Column('content_id', sa.String(length=36), nullable=False),)
        activities = connection.execute(activity_helper.select())
        for a in activities:
            enrollment_id = load_enrollment_id(a)
            connection.execute(activity_helper.
                               update().
                               where(activity_helper.c.id == a[0]).
                               values(enrollment_id=enrollment_id))


def upgrade():
    op.add_column('activity', sa.Column('enrollment_id', sa.String(length=36), nullable=True))
    update_enrollment_ids()
    op.alter_column('activity', 'enrollment_id', nullable=False)
    op.create_foreign_key(None, 'activity', 'enrollment', ['enrollment_id'], ['id'], ondelete='cascade')


def downgrade():
    op.drop_column('activity', 'enrollment_id')
