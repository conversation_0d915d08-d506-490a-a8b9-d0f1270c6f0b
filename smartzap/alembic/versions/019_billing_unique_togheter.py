"""billing_unique_togheter

Revision ID: 019
Revises: 018
Create Date: 2023-02-07 14:49:08.439762

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '019'
down_revision = '018'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint('_start_at__end_at__workspace_id__unique', 'billing', ['start_at', 'end_at', 'workspace_id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('_start_at__end_at__workspace_id__unique', 'billing', type_='unique')
    # ### end Alembic commands ###
