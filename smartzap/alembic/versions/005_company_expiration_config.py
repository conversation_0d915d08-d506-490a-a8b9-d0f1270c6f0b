"""company_expiration_config

Revision ID: 005
Revises: 004
Create Date: 2020-07-28 12:45:37.938946

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '005'
down_revision = '004'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('company', sa.Column('end_course_schedule', sa.Integer(), nullable=True))
    op.add_column('company', sa.Column('user_token_expiration', sa.Integer(), nullable=True))

    op.execute('UPDATE company SET user_token_expiration = 5, end_course_schedule = 6')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('company', 'user_token_expiration')
    op.drop_column('company', 'end_course_schedule')
    # ### end Alembic commands ###
