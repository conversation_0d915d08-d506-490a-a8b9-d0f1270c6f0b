"""create_workspace_certificate_template

Revision ID: 24
Revises: 23
Create Date: 2024-11-04 00:18:26.492709

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '24'
down_revision = '23'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('workspace_certificate_template',
        sa.Column('id', sa.String(length=36), nullable=False),
        sa.Column('workspace_id', sa.String(length=36), nullable=False),
        sa.Column('language', sa.String(length=20), nullable=False),
        sa.Column('certificate_template_key', sa.Text(), nullable=False),
        sa.ForeignKeyConstraint(['workspace_id'], ['workspace.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('workspace_id', 'language', name='workspace_certificate_template_uc')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('workspace_certificate_template')
    # ### end Alembic commands ###
