"""private_channel

Revision ID: 009
Revises: 008
Create Date: 2020-08-04 20:22:00.509908

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '009'
down_revision = '008'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('company', sa.Column('private_channel', sa.String(), nullable=True))


def downgrade():
    op.drop_column('company', 'private_channel')
