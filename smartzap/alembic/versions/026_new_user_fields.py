"""new_user_fields

Revision ID: 026
Revises: 026
Create Date: 2024-12-11 08:48:49.961345

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '026'
down_revision = '025'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user', sa.Column('cpf', sa.String(), nullable=True))
    op.add_column('user', sa.Column('ein', sa.String(), nullable=True))
    op.add_column('user', sa.Column('birthday', sa.Date(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'birthday')
    op.drop_column('user', 'ein')
    op.drop_column('user', 'cpf')
    # ### end Alembic commands ###
