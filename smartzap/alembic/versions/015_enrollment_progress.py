"""enrollment_progress

Revision ID: 15
Revises: 014
Create Date: 2021-10-22 12:54:51.214766

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '015'
down_revision = '014'
branch_labels = None
depends_on = None


def is_schedule_sent(status):
    return status == 'SENT' or status == 'DELIVERED'


def load_enrollment_progress(enrollment_id):
    with op.batch_alter_table('schedule') as batch_op:
        connection = batch_op.get_bind()
        schedule_helper = sa.Table('schedule', sa.MetaData(),
                                   sa.Column('id', sa.String(length=36), nullable=False),
                                   sa.Column('enrollment_id', sa.String(length=36), nullable=False),
                                   sa.Column('type', sa.String(), nullable=True),
                                   sa.Column('status', sa.String(), nullable=True),)
        schedules = connection.execute(schedule_helper.
                                       select().
                                       where(schedule_helper.c.enrollment_id == enrollment_id))

        content_schedules = [s for s in schedules if s['type'] == 'LESSON_CONTENT']
        if not content_schedules:
            return 100

        total_contents = len(content_schedules)
        total_delivered = sum([1 for s in content_schedules if is_schedule_sent(s['status'])])
        return round((100 * total_delivered) / total_contents)


def update_all_enrollment_progresses():
    with op.batch_alter_table('enrollment') as batch_op:
        connection = batch_op.get_bind()
        enrollment_helper = sa.Table('enrollment', sa.MetaData(),
                                   sa.Column('id', sa.String(length=36), nullable=False),
                                   sa.Column('progress', sa.Integer(), nullable=True),)
        enrollments = connection.execute(enrollment_helper.select())
        for e in enrollments:
            progress = load_enrollment_progress(e[0])
            connection.execute(enrollment_helper.
                               update().
                               where(enrollment_helper.c.id == e[0]).
                               values(progress=progress))


def upgrade():
    op.add_column('enrollment', sa.Column('progress', sa.Integer(), nullable=True))
    # update_all_enrollment_progresses()


def downgrade():
    op.drop_column('enrollment', 'progress')
