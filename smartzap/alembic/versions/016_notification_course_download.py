"""notification_course_download

Revision ID: 016
Revises: 015
Create Date: 2021-10-31 21:01:56.950175

"""
from alembic import op
import sqlalchemy as sa
import datetime


# revision identifiers, used by Alembic.
revision = '016'
down_revision = '015'
branch_labels = None
depends_on = None


def upgrade():
    types = [
        {
            'id': '094df104-8964-4cfd-96da-8dd526ed8010',
            'type': 'Course',
            'action': 'Download',
        }
    ]

    now = datetime.datetime.utcnow()
    with op.batch_alter_table('notification_type') as batch_op:
        connection = batch_op.get_bind()
        content_helper = sa.Table('notification_type', sa.MetaData(),
                                  sa.Column('id', sa.String(length=36), nullable=False),
                                  sa.Column('type', sa.String(), nullable=False),
                                  sa.Column('action', sa.String(), nullable=False),
                                  sa.Column('created', sa.DateTime(), nullable=False),
                                  sa.Column('updated', sa.DateTime(), nullable=False), )
        for t in types:
            connection.execute(content_helper.insert().values(id=t['id'], type=t['type'], action=t['action'],
                                                              created=now, updated=now))


def downgrade():
    op.execute("DELETE FROM notification_type WHERE id='094df104-8964-4cfd-96da-8dd526ed8010'")
