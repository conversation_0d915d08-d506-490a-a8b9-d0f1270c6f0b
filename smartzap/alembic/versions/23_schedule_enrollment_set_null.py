"""create_tables

Revision ID: 023
Revises: 22
Create Date: 2023-11-16 10:42:22.808445

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '23'
down_revision = '22'
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column('schedule', 'enrollment_id',
               existing_type=sa.VARCHAR(length=36),
               nullable=True)
    op.drop_constraint('schedule_enrollment_id_fkey', 'schedule', type_='foreignkey')
    op.create_foreign_key(None, 'schedule', 'enrollment', ['enrollment_id'], ['id'], ondelete='SET NULL')
