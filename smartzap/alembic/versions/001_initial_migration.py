"""empty message

Revision ID: 90a6eb7c44c2
Revises: 
Create Date: 2020-04-27 09:18:52.733342

"""
import datetime

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('company',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('logo_url', sa.String(), nullable=True),
    sa.Column('created', sa.DateTime(), nullable=True),
    sa.Column('updated', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('content_type',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('image_url', sa.String(), nullable=True),
    sa.Column('created', sa.DateTime(), nullable=True),
    sa.Column('updated', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('course_category',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('created', sa.DateTime(), nullable=True),
    sa.Column('updated', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('user',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('phone', sa.String(), nullable=False),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('tags', sa.String(), nullable=True),
    sa.Column('avatar', sa.String(), nullable=True),
    sa.Column('my_account_user', sa.Boolean(), nullable=True),
    sa.Column('created', sa.DateTime(), nullable=True),
    sa.Column('updated', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('phone')
    )
    op.create_table('course',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('category_id', sa.String(length=36), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('holder_image', sa.String(), nullable=True),
    sa.Column('thumb_image', sa.String(), nullable=True),
    sa.Column('duration', sa.Float(), nullable=True),
    sa.Column('points', sa.Float(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('lang', sa.String(), nullable=True),
    sa.Column('company_owner_id', sa.String(length=36), nullable=False),
    sa.Column('user_creator_id', sa.String(length=36), nullable=False),
    sa.Column('created', sa.DateTime(), nullable=True),
    sa.Column('updated', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['category_id'], ['course_category.id'], ),
    sa.ForeignKeyConstraint(['company_owner_id'], ['company.id'], ),
    sa.ForeignKeyConstraint(['user_creator_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('feedback',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('company_id', sa.String(length=36), nullable=False),
    sa.Column('tag', sa.String(), nullable=True),
    sa.Column('message', sa.String(), nullable=True),
    sa.Column('scope', sa.String(), nullable=True),
    sa.Column('created', sa.DateTime(), nullable=True),
    sa.Column('updated', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['company_id'], ['company.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('user_company',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('company_id', sa.String(length=36), nullable=False),
    sa.Column('user_id', sa.String(length=36), nullable=False),
    sa.Column('created', sa.DateTime(), nullable=True),
    sa.Column('updated', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['company_id'], ['company.id'],  ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'],  ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', 'company_id', 'user_id'),
    sa.UniqueConstraint('company_id', 'user_id', name='company_user_uc')
    )
    op.create_table('course_company',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('company_id', sa.String(length=36), nullable=False),
    sa.Column('course_id', sa.String(length=36), nullable=False),
    sa.Column('created', sa.DateTime(), nullable=True),
    sa.Column('updated', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['company_id'], ['company.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['course_id'], ['course.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', 'company_id', 'course_id'),
    sa.UniqueConstraint('company_id', 'course_id', name='company_course_uc')
    )
    op.create_table('lesson',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('course_id', sa.String(length=36), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('order', sa.Integer(), nullable=True),
    sa.Column('created', sa.DateTime(), nullable=True),
    sa.Column('updated', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['course_id'], ['course.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('content',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('lesson_id', sa.String(length=36), nullable=False),
    sa.Column('type_id', sa.String(length=36), nullable=True),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('order', sa.Integer(), nullable=True),
    sa.Column('dispatch_in', sa.Integer(), nullable=True),
    sa.Column('dispatch_period', sa.String(), nullable=True),
    sa.Column('learn_content', sa.String(length=36), nullable=True),
    sa.Column('created', sa.DateTime(), nullable=True),
    sa.Column('updated', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['lesson_id'], ['lesson.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['type_id'], ['content_type.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('activity',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('user_id', sa.String(length=36), nullable=False),
    sa.Column('content_id', sa.String(length=36), nullable=False),
    sa.Column('action', sa.String(), nullable=True),
    sa.Column('start_at', sa.DateTime(), nullable=True),
    sa.Column('stop_at', sa.DateTime(), nullable=True),
    sa.Column('duration', sa.Integer(), nullable=True),
    sa.Column('created', sa.DateTime(), nullable=True),
    sa.Column('updated', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['content_id'], ['content.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('enrollment',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('user_id', sa.String(length=36), nullable=False),
    sa.Column('course_id', sa.String(length=36), nullable=False),
    sa.Column('company_id', sa.String(length=36), nullable=False),
    sa.Column('current_lesson_id', sa.String(length=36), nullable=True),
    sa.Column('current_content_id', sa.String(length=36), nullable=True),
    sa.Column('points', sa.Float(), nullable=True),
    sa.Column('performance', sa.Float(), nullable=True),
    sa.Column('start_date', sa.Date(), nullable=True),
    sa.Column('end_date', sa.Date(), nullable=True),
    sa.Column('timezone', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('created', sa.DateTime(), nullable=True),
    sa.Column('updated', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['company_id'], ['company.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['course_id'], ['course.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['current_content_id'], ['content.id'], ),
    sa.ForeignKeyConstraint(['current_lesson_id'], ['lesson.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('schedule',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('enrollment_id', sa.String(length=36), nullable=False),
    sa.Column('message_id', sa.String(), nullable=True),
    sa.Column('send_date', sa.DateTime(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('reference_id', sa.String(length=36), nullable=True),
    sa.Column('type', sa.String(), nullable=False),
    sa.Column('lang', sa.String(), nullable=False),
    sa.Column('timezone', sa.String(), nullable=False),
    sa.Column('created', sa.DateTime(), nullable=True),
    sa.Column('updated', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['enrollment_id'], ['enrollment.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###

    _add_content_type_data()
    _add_course_category_data()

    now = str(datetime.datetime.utcnow())
    tables = ['content_type', 'course_category']
    for t in tables:
        op.execute(f"UPDATE \"{t}\" SET created = '{now}', updated = '{now}';")


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('schedule')
    op.drop_table('enrollment')
    op.drop_table('activity')
    op.drop_table('content')
    op.drop_table('lesson')
    op.drop_table('course_company')
    op.drop_table('user_company')
    op.drop_table('feedback')
    op.drop_table('course')
    op.drop_table('user')
    op.drop_table('course_category')
    op.drop_table('content_type')
    op.drop_table('company')
    # ### end Alembic commands ###


def _add_content_type_data():
    contents = [
        {
            'id': '0faac34b-2393-4352-8a94-a9ee0659f824',
            'name': 'PDF',
            'description': 'PDF'
        },
        {
            'id': '2284bfce-fdfc-4477-9143-39c380cc653c',
            'name': 'Image',
            'description': 'Image'
        },
        {
            'id': '569cc389-ac1d-4fa0-9692-f715b475b59b',
            'name': 'Video',
            'description': 'Video'
        },
        {
            'id': '673e4c02-ae1c-4e61-830b-706d35bd0b11',
            'name': 'Spreadsheet',
            'description': 'Spreadsheet'
        },
        {
            'id': '799f766c-a956-4c03-b5aa-bde9ba357de8',
            'name': 'Podcast',
            'description': 'Podcast'
        },
        {
            'id': '7a41a8e0-ee37-4d0b-ad4f-35bada67134d',
            'name': 'Question',
            'description': 'Question'
        },
        {
            'id': '7ee375e4-b781-46e6-b0de-0323ebb94b96',
            'name': 'Presentation',
            'description': 'Presentation'
        },
        {
            'id': 'b7094e27-b263-4fed-a928-6f0a78439cbe',
            'name': 'Text',
            'description': 'Text'
        },
        {
            'id': 'e459d983-5c61-4e7d-8310-1eec2dc8f369',
            'name': 'Blog',
            'description': 'Blog'
        }
    ]

    with op.batch_alter_table('content_type') as batch_op:
        connection = batch_op.get_bind()
        content_helper = sa.Table('content_type', sa.MetaData(),
                                  sa.Column('id', sa.String(length=36), nullable=False),
                                  sa.Column('name', sa.String(), nullable=False),
                                  sa.Column('description', sa.String(), nullable=False))
        for c in contents:
            connection.execute(content_helper.insert().
                               values(id=c['id'], name=c['name'], description=c['description']))


def _add_course_category_data():
    categories = [
        {
            'id': '0283295a-09ff-4d8d-a59b-3d0e037dd48c',
            'name': 'Lifestyle',
            'description': 'food-and-beverage, beauty and makeup, ...'
        },
        {
            'id': '0eed5be9-e100-438a-a28d-7140481a25fd',
            'name': 'Leadership',
            'description': 'Leadership'
        },
        {
            'id': '49223560-7afb-46b9-bea9-5b7036ba673f',
            'name': 'Marketing',
            'description': 'branding, product marketing, ...'
        },
        {
            'id': '4a782a1f-d441-4d85-9643-90759dd0bb6b',
            'name': 'Management',
            'description': 'Management'
        },
        {
            'id': '57db27db-fca4-4b5e-b0b2-14f0eca7998c',
            'name': 'Entrepreneurship',
            'description': 'business fundamentals, online business, startup, ...'
        },
        {
            'id': '57e9cd65-ebf3-4df9-b05d-444ecca1459b',
            'name': 'Web Design',
            'description': 'Web Design'
        },
        {
            'id': '73b10d7c-6502-40c6-9c70-f9b3692b8b6e',
            'name': 'Sales',
            'description': 'Sales'
        },
        {
            'id': '7d46e9d8-30cf-42be-99d0-19dd724d2be9',
            'name': 'Design',
            'description': 'Design'
        },
        {
            'id': '85a2d7ea-e9c7-4915-abfb-0d3e8927d9e6',
            'name': 'Office Productivity',
            'description': 'excel, ms project, word, ...'
        },
        {
            'id': '861a5fec-572a-419e-9479-0e1a00773fa2',
            'name': 'Social Media',
            'description': 'instagram-marketing, facebook marketing, adsense, ...'
        },
        {
            'id': '********-f0ef-4d82-b328-435ddcf1962d',
            'name': 'Finance',
            'description': 'financial analysis, financial modeling, accounting, ...'
        },
        {
            'id': '95e9d9a9-5550-419e-944b-8d59b0617eeb',
            'name': 'Financial Education',
            'description': 'financial plan, investments, ...'
        },
        {
            'id': '9b619c81-6278-44fa-addb-665bf728ddfa',
            'name': 'Health and Fitness',
            'description': 'general health, nutrition, mental health, ...'
        },
        {
            'id': '9c671f59-bb3c-4945-832b-e5e26b65d5cd',
            'name': 'Development',
            'description': 'programming languages, web development, mobile apps, game development, ...'
        },
        {
            'id': 'a4ca5173-7b03-4184-a0db-9c1d198ebe89',
            'name': 'Digital Marketing',
            'description': 'search engine optimization, analytics and automation, content, ...'
        },
        {
            'id': 'abde9069-242f-48bb-9b46-c64137fe5004',
            'name': 'Technology',
            'description': 'linux, network, ...'
        },
        {
            'id': 'b4e4715f-ee9c-424a-aade-1da388b190fd',
            'name': 'Language',
            'description': 'english, spanish, ...'
        },
        {
            'id': 'cd00fd0c-192e-444f-954e-6b1a8cdc1f90',
            'name': 'Project Management',
            'description': 'product management, business process management, ...'
        },
        {
            'id': 'd837b4a9-6708-4678-b0af-9d59220232f4',
            'name': 'Strategy',
            'description': 'Strategy'
        },
        {
            'id': 'e5077e79-1ac8-4f84-a54e-c768eaf6a552',
            'name': 'Communications',
            'description': 'writing, public-speaking, presentation-skills, communication-skills, ...'
        },
        {
            'id': 'e831d25b-7857-4406-87ee-069d891a14ee',
            'name': 'Personal Development',
            'description': 'life-coaching, neuro linguistic programming, speed reading, public speaking, ...'
        }
    ]

    with op.batch_alter_table('course_category') as batch_op:
        connection = batch_op.get_bind()
        categories_helper = sa.Table('course_category', sa.MetaData(),
                                     sa.Column('id', sa.String(length=36), nullable=False),
                                     sa.Column('name', sa.String(), nullable=False),
                                     sa.Column('description', sa.String(), nullable=False))
        for c in categories:
            connection.execute(categories_helper.insert().
                               values(id=c['id'], name=c['name'], description=c['description']))
