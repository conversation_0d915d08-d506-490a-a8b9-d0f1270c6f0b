"""upgrade_private_channels

Revision ID: 014
Revises: 013
Create Date: 2021-10-10 13:21:31.387235

"""
import json

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '014'
down_revision = '013'
branch_labels = None
depends_on = None


def convert_to_json_private_channel(old_private_channel):
    if not old_private_channel:
        return None
    return f'{{"pt-br": "{old_private_channel}"}}'


def convert_to_old_private_channel(private_channel):
    if not private_channel:
        return None
    json_data = json.loads(private_channel)
    return json_data.get('pt-br')


def batch_companies(private_channel_func):
    with op.batch_alter_table('company') as batch_op:
        connection = batch_op.get_bind()
        company_helper = sa.Table('company', sa.MetaData(),
                                  sa.Column('id', sa.String(length=36), nullable=False),
                                  sa.Column('private_channel', sa.String, nullable=True), )
        companies = connection.execute(company_helper.select())
        for c in companies:
            private_channel = private_channel_func(c[1])
            connection.execute(company_helper.
                               update().
                               where(company_helper.c.id == c[0]).
                               values(private_channel=private_channel))


def upgrade():
    batch_companies(convert_to_json_private_channel)


def downgrade():
    batch_companies(convert_to_old_private_channel)
