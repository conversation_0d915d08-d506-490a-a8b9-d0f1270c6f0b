"""workspace_enrollment_idle_days_limit

Revision ID: 032
Revises: 031
Create Date: 2025-03-11 14:14:25.070298

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '032'
down_revision = '031'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('workspace', sa.Column('enrollment_idle_days_limit', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('workspace', 'enrollment_idle_days_limit')
    # ### end Alembic commands ###
