"""course_allow_content_antecipation

Revision ID: 20
Revises: 019
Create Date: 2023-07-05 17:00:34.524868

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '20'
down_revision = '019'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('course', sa.<PERSON>umn('allow_content_anticipation', sa.<PERSON>(), nullable=True))
    op.execute("UPDATE course SET allow_content_anticipation = true")
    op.alter_column('course', 'allow_content_anticipation', nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('course', 'allow_content_anticipation')
    # ### end Alembic commands ###
