"""new-fields-to-support-external-sender

Revision ID: 025
Revises: 24
Create Date: 2024-12-03 12:48:27.510485

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '025'
down_revision = '24'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('activity_enrollment_id_fkey', 'activity', type_='foreignkey')
    op.drop_constraint('activity_content_id_fkey', 'activity', type_='foreignkey')
    op.create_foreign_key(None, 'activity', 'content', ['content_id'], ['id'])
    op.create_foreign_key(None, 'activity', 'enrollment', ['enrollment_id'], ['id'])
    op.drop_constraint('billing_company_id_fkey', 'billing', type_='foreignkey')
    op.create_foreign_key(None, 'billing', 'workspace', ['workspace_id'], ['id'])
    op.drop_constraint('chat_enrollment_id_fkey', 'chat', type_='foreignkey')
    op.create_foreign_key(None, 'chat', 'enrollment', ['enrollment_id'], ['id'])
    op.alter_column('course', 'allow_content_anticipation',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.drop_constraint('company_course_uc', 'course_workspace', type_='unique')
    op.create_unique_constraint('workspace_course_uc', 'course_workspace', ['workspace_id', 'course_id'])
    op.drop_constraint('notification_company_id_fkey', 'notification', type_='foreignkey')
    op.create_foreign_key(None, 'notification', 'workspace', ['workspace_id'], ['id'])
    op.add_column('schedule', sa.Column('triggered_flow_id', sa.String(length=34), nullable=True))
    op.add_column('schedule', sa.Column('external_sender', sa.Boolean(), nullable=True))
    op.drop_constraint('schedule_company_id_fkey', 'schedule', type_='foreignkey')
    op.create_foreign_key(None, 'schedule', 'workspace', ['workspace_id'], ['id'])
    op.drop_constraint('company_user_uc', 'user_workspace', type_='unique')
    op.create_unique_constraint('workspace_user_uc', 'user_workspace', ['workspace_id', 'user_id'])
    op.add_column('workspace', sa.Column('messages_by_twilio_studio', sa.Boolean(), nullable=True))
    op.add_column('workspace', sa.Column('messages_content_embed', sa.Boolean(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('workspace', 'messages_by_twilio_studio')
    op.drop_constraint('workspace_user_uc', 'user_workspace', type_='unique')
    op.create_unique_constraint('company_user_uc', 'user_workspace', ['workspace_id', 'user_id'])
    op.drop_constraint(None, 'schedule', type_='foreignkey')
    op.create_foreign_key('schedule_company_id_fkey', 'schedule', 'workspace', ['workspace_id'], ['id'], ondelete='CASCADE')
    op.drop_column('schedule', 'external_sender')
    op.drop_column('schedule', 'triggered_flow_id')
    op.drop_constraint(None, 'notification', type_='foreignkey')
    op.create_foreign_key('notification_company_id_fkey', 'notification', 'workspace', ['workspace_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('workspace_course_uc', 'course_workspace', type_='unique')
    op.create_unique_constraint('company_course_uc', 'course_workspace', ['workspace_id', 'course_id'])
    op.alter_column('course', 'allow_content_anticipation',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.drop_constraint(None, 'chat', type_='foreignkey')
    op.create_foreign_key('chat_enrollment_id_fkey', 'chat', 'enrollment', ['enrollment_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint(None, 'billing', type_='foreignkey')
    op.create_foreign_key('billing_company_id_fkey', 'billing', 'workspace', ['workspace_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint(None, 'activity', type_='foreignkey')
    op.drop_constraint(None, 'activity', type_='foreignkey')
    op.create_foreign_key('activity_content_id_fkey', 'activity', 'content', ['content_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('activity_enrollment_id_fkey', 'activity', 'enrollment', ['enrollment_id'], ['id'], ondelete='CASCADE')
    op.drop_column('workspace', 'messages_content_embed')
    # ### end Alembic commands ###
