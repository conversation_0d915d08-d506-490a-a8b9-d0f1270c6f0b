"""company_to_workspace

Revision ID: 017
Revises: 016
Create Date: 2022-07-25 09:21:42.784048

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '017'
down_revision = '016'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.rename_table('company', 'workspace')
    op.alter_column('course_company', 'company_id', new_column_name='workspace_id')
    op.alter_column('user_company', 'company_id', new_column_name='workspace_id')
    op.alter_column('enrollment', 'company_id', new_column_name='workspace_id')
    op.alter_column('billing', 'company_id', new_column_name='workspace_id')
    op.alter_column('course', 'company_owner_id', new_column_name='workspace_owner_id')
    op.alter_column('feedback', 'company_id', new_column_name='workspace_id')
    op.alter_column('notification', 'company_id', new_column_name='workspace_id')
    op.alter_column('schedule', 'company_id', new_column_name='workspace_id')

    op.rename_table('course_company', 'course_workspace')

    op.rename_table('user_company', 'user_workspace')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('course_workspace', 'workspace_id', new_column_name='company_id')
    op.alter_column('user_workspace', 'workspace_id', new_column_name='company_id')
    op.alter_column('enrollment', 'workspace_id', new_column_name='company_id')
    op.alter_column('billing', 'workspace_id', new_column_name='company_id')
    op.alter_column('course', 'workspace_owner_id', new_column_name='company_owner_id')
    op.alter_column('feedback', 'workspace_id', new_column_name='company_id')
    op.alter_column('notification', 'workspace_id', new_column_name='company_id')
    op.alter_column('schedule', 'workspace_id', new_column_name='company_id')
    op.rename_table('workspace', 'company')
    op.rename_table('course_workspace', 'course_company')
    op.rename_table('user_workspace', 'user_company')
    # ### end Alembic commands ###
