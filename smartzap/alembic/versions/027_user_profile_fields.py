"""user-profile-fields

Revision ID: 027
Revises: 026
Create Date: 2024-12-13 16:56:46.566742

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '027'
down_revision = '026'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user', sa.<PERSON>umn('director', sa.String(), nullable=True))
    op.add_column('user', sa.<PERSON>umn('manager', sa.String(), nullable=True))
    op.add_column('user', sa.Column('area_of_activity', sa.String(), nullable=True))
    op.add_column('user', sa.Column('job', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'job')
    op.drop_column('user', 'area_of_activity')
    op.drop_column('user', 'manager')
    op.drop_column('user', 'director')
    # ### end Alembic commands ###
