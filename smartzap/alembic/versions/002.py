"""empty message

Revision ID: 813e5ddd7730
Revises: 001
Create Date: 2020-05-15 09:58:28.038889

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '002'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('activity_content_id_fkey', 'activity', type_='foreignkey')
    op.create_foreign_key(None, 'activity', 'content', ['content_id'], ['id'], ondelete='cascade')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'activity', type_='foreignkey')
    op.create_foreign_key('activity_content_id_fkey', 'activity', 'content', ['content_id'], ['id'])
    # ### end Alembic commands ###
