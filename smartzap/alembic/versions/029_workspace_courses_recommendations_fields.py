"""workspace-courses-recommendations-fields

Revision ID: 029
Revises: 028
Create Date: 2024-12-22 16:45:28.438540

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '029'
down_revision = '028'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('workspace', sa.<PERSON>umn('send_courses_recommendation_message', sa.<PERSON>(), nullable=True))
    op.add_column('workspace', sa.Column('courses_portal_url', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('workspace', 'courses_portal_url')
    op.drop_column('workspace', 'send_courses_recommendation_message')
    # ### end Alembic commands ###
