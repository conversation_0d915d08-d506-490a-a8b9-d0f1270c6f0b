"""anticipate_schedules

Revision ID: 003
Revises: 002
Create Date: 2020-06-20 20:53:54.095236

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '003'
down_revision = '002'
branch_labels = None
depends_on = None


def update_schedules():
    op.execute('UPDATE schedule SET anticipated = FALSE')
    op.execute('UPDATE schedule SET company_id = '
               '    (SELECT enrollment.company_id FROM enrollment WHERE enrollment.id = schedule.enrollment_id) '
               'WHERE exists (SELECT * FROM enrollment WHERE enrollment.id = schedule.enrollment_id)')


def upgrade():
    op.add_column('schedule', sa.Column('anticipated', sa.<PERSON>an(), nullable=True))
    op.add_column('schedule', sa.Column('company_id', sa.String(length=36), nullable=True))
    op.create_foreign_key(None, 'schedule', 'company', ['company_id'], ['id'], ondelete='cascade')

    update_schedules()
    op.alter_column('schedule', 'company_id', nullable=False)


def downgrade():
    op.drop_column('schedule', 'anticipated')
    op.drop_column('schedule', 'company_id')

