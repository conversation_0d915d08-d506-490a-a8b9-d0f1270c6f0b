"""content-relations

Revision ID: 031
Revises: 030
Create Date: 2025-03-06 08:55:06.843032

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '031'
down_revision = '030'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('activity_content_id_fkey', 'activity', type_='foreignkey')
    op.create_foreign_key(None, 'activity', 'content', ['content_id'], ['id'], ondelete='SET NULL')
    op.drop_constraint('fk_enrollment_content_id', 'enrollment', type_='foreignkey')
    op.create_foreign_key(None, 'enrollment', 'content', ['current_content_id'], ['id'], ondelete='SET NULL')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'enrollment', type_='foreignkey')
    op.create_foreign_key('fk_enrollment_content_id', 'enrollment', 'content', ['current_content_id'], ['id'])
    op.drop_constraint(None, 'activity', type_='foreignkey')
    op.create_foreign_key('activity_content_id_fkey', 'activity', 'content', ['content_id'], ['id'])
    # ### end Alembic commands ###
