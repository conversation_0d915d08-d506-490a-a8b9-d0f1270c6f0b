"""user_flag

Revision ID: 013
Revises: 012
Create Date: 2021-01-14 16:44:02.384912

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '013'
down_revision = '012'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('user', sa.Column('sync_check', sa.String(length=36), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'sync_check')
    # ### end Alembic commands ###
