"""chat-enrollment-relation

Revision ID: 030
Revises: 029
Create Date: 2025-01-03 13:17:33.875715

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '030'
down_revision = '029'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('activity', 'enrollment_id',
               existing_type=sa.VARCHAR(length=36),
               nullable=True)
    op.drop_constraint('activity_enrollment_id_fkey', 'activity', type_='foreignkey')
    op.create_foreign_key(None, 'activity', 'enrollment', ['enrollment_id'], ['id'], ondelete='SET NULL')
    op.drop_constraint('chat_enrollment_id_fkey', 'chat', type_='foreignkey')
    op.create_foreign_key(None, 'chat', 'enrollment', ['enrollment_id'], ['id'], ondelete='cascade')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'chat', type_='foreignkey')
    op.create_foreign_key('chat_enrollment_id_fkey', 'chat', 'enrollment', ['enrollment_id'], ['id'])
    op.drop_constraint(None, 'activity', type_='foreignkey')
    op.create_foreign_key('activity_enrollment_id_fkey', 'activity', 'enrollment', ['enrollment_id'], ['id'])
    op.alter_column('activity', 'enrollment_id',
               existing_type=sa.VARCHAR(length=36),
               nullable=False)
    # ### end Alembic commands ###
