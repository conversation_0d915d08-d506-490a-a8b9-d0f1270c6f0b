"""chat_control

Revision ID: 004
Revises: 003
Create Date: 2020-07-13 16:03:32.792471

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '004'
down_revision = '003'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('chat',
                    sa.Column('id', sa.String(length=36), nullable=False),
                    sa.<PERSON>umn('enrollment_id', sa.String(length=36), nullable=False),
                    sa.Column('content_id', sa.String(length=36), nullable=True),
                    sa.Column('type', sa.String(), nullable=False),
                    sa.Column('replied', sa.<PERSON>(), nullable=True),
                    sa.Column('user_answer', sa.String(), nullable=True),
                    sa.Column('created', sa.DateTime(), nullable=True),
                    sa.Column('updated', sa.DateTime(), nullable=True),
                    sa.ForeignKeyConstraint(['enrollment_id'], ['enrollment.id'], ondelete='CASCADE'),
                    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('chat')
    # ### end Alembic commands ###
