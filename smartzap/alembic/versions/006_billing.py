"""billing

Revision ID: 006
Revises: 005
Create Date: 2020-07-30 15:25:00.672344

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '006'
down_revision = '005'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table('billing',
        sa.Column('id', sa.String(length=36), nullable=False),
        sa.<PERSON>umn('company_id', sa.String(length=36), nullable=False),
        sa.Column('start_at', sa.Date(), nullable=True),
        sa.Column('end_at', sa.Date(), nullable=True),
        sa.Column('monthly_plan', sa.Integer(), nullable=True),
        sa.Column('billing_cycle_day', sa.Integer(), nullable=True),
        sa.Column('used', sa.Integer(), nullable=True),
        sa.Column('balance', sa.Integer(), nullable=True),
        sa.Column('created', sa.DateTime(), nullable=True),
        sa.Column('updated', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['company_id'], ['company.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )

    op.add_column('company', sa.Column('monthly_plan', sa.Integer(), nullable=True))
    op.add_column('company', sa.Column('billing_cycle_day', sa.Integer(), nullable=True))
    op.execute('UPDATE company SET monthly_plan = 0, billing_cycle_day = 1')


def downgrade():
    op.drop_column('company', 'billing_cycle_day')
    op.drop_column('company', 'monthly_plan')

    op.drop_table('billing')
