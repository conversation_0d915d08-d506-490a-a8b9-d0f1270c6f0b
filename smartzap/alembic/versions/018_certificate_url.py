"""certificate_url

Revision ID: 018
Revises: 017
Create Date: 2022-11-18 07:51:24.416328

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '018'
down_revision = '017'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('enrollment', sa.Column('certificate_url', sa.String(), nullable=True))


def downgrade():
    op.drop_column('enrollment', 'certificate_url')
