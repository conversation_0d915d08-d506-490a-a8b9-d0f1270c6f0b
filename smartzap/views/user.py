import flask
from domain import schema, service
from flask_extensions import flask_ext, swagger, webfilters
from views import utils

user_page = flask.Blueprint('user', __name__)


@user_page.route('/api/v1/user/<user_id>', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.UserSchema, code=200)
def read_user(user_id, user_service: service.UserService):
    return user_service.load(user_id)


@user_page.route('/api/v1/user', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.paginate_schema(schema.UserSchema), code=200)
def read_users(user_service: service.UserService):
    filters = webfilters.load_filters()
    users, pagination = user_service.load_filters(filters)
    return schema.build_pagination(users, pagination)


@user_page.route('/api/v1/user', methods=['POST'])
@flask_ext.check_permission('admin')
@swagger.use_schema(schema.UserSchema, parameter_name='user')
@swagger.marshal(schema.UserSchema, code=201)
def create_user(user, user_service: service.UserService):
    return user_service.add(user)


@user_page.route('/api/v1/user/import-xlsx', methods=['POST'])
@flask_ext.check_permission('admin')
def import_xlsx_users(xls_importer: service.UserXlsImporter):
    if 'file' not in flask.request.files:
        return 'File not found', 422

    file = flask.request.files['file']
    return utils.import_xlsx_users(file, xls_importer)


@user_page.route('/api/v1/user/<user_id>', methods=['PUT', 'PATCH'])
@flask_ext.check_permission('admin')
@swagger.use_schema(schema.UserSchema, parameter_name='user', partial=True)
def update_user(user_id, user, user_service: service.UserService):
    user_service.update(user_id, user)
    return '', 204


@user_page.route('/api/v1/user/<user_id>', methods=['DELETE'])
@flask_ext.check_permission('admin')
def delete_user(user_id, user_service: service.UserService):
    user_service.delete(user_id)
    return '', 204


@user_page.route('/api/v1/user/notification', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.paginate_schema(schema.NotificationSchema), code=200)
def read_notifications(notification_service: service.NotificationService,
                       user_service: service.UserService, user_id: service.UserId):
    _ = user_service.load(user_id)
    filters = webfilters.load_filters()
    notifications, pagination = notification_service.load_filters(filters)
    return schema.build_pagination(notifications, pagination)


@user_page.route('/api/v1/user/notification/<notification_id>', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.NotificationSchema, code=200)
def read_notification(notification_id, notification_service: service.NotificationService,
                      user_service: service.UserService, user_id: service.UserId):
    _ = user_service.load(user_id)
    return notification_service.load(notification_id)


@user_page.route('/api/v1/user/notification/<notification_id>', methods=['PUT', 'PATCH'])
@flask_ext.check_permission('admin')
@swagger.use_schema(schema.NotificationSchema, parameter_name='notification', partial=True)
def update_notification(notification_id, notification, notification_service: service.NotificationService,
                        user_service: service.UserService, user_id: service.UserId):
    _ = user_service.load(user_id)
    notification_service.update(notification_id, notification)
    return '', 204


@user_page.route('/api/v1/user/notification/batch-read', methods=['POST'])
@flask_ext.check_permission('admin')
def batch_read_notification(notification_service: service.NotificationService,
                            user_service: service.UserService, user_id: service.UserId):
    notification_service.read_all_notifications()
    return '', 200


@user_page.route('/api/v1/user/count', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.UsersCountSchema, code=200)
def read_users_count(user_service: service.UserService):
    users_count = user_service.count_users()
    return schema.UsersCountSchema().dump({"users_count": users_count})
