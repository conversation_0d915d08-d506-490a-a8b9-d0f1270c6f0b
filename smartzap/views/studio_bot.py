from http import HTTPStatus

import flask
from domain import phone_helper, schema, service
from domain.services.content_delivery_service import ContentMessageDataService
from domain.services.message_chatter_bot_v2 import MessageChatterBotV2
from flask_extensions import auth, flask_ext, swagger
from logger import logger
from views.enrollment import enrollment_page
from views.schemas.course_nps_score_response import CourseNpsScoreResponseSchema
from views.schemas.course_nps_score_schema import CourseNpsScoreSchema
from views.schemas.courses_portal_url_bot_response_schema import CoursesPortalUrlBotResponseSchema
from views.schemas.courses_recommendations_bot_schema import CoursesRecommendationsBotSchema
from views.schemas.courses_recommendations_schema import CoursesRecommendationsBotResponseSchema
from views.schemas.enroll_user_bot_schema import EnrollUserBotSchema
from views.schemas.enrollment_confirmation_response_schema import EnrollmentConfirmationResponseSchema
from views.schemas.give_up_studio_bot_response_schema import GiveUpStudioBotResponseSchema
from views.schemas.studio_bot_response_schema import StudioBotResponseSchema
from views.schemas.studio_bot_schema import StudioBotSchema

studio_bot_page = flask.Blueprint('studio-bot', __name__)

PHONE_FIELD = "phone"
BOT_STATUS = "bot_status"


@studio_bot_page.route('/api/v1/twilio/next-content', methods=['GET', 'POST'])
def get_next_content(content_message_data_service: ContentMessageDataService):
    if not auth.validate_twilio_api_key(flask.request.args.get('api-key', '').strip()):
        return '', 401

    twilio_resp = flask.request.json
    phone = twilio_resp.get('From', '').strip()
    execution_sid = twilio_resp.get('ExecutionId')
    phone = phone_helper.normalize_brazilian_9_digits(phone[10:])

    next_content_data, status = content_message_data_service.get_next_content_data(phone, execution_sid)

    next_content_data["status"] = status
    return next_content_data, 200


@enrollment_page.route('/api/v1/twilio/enrollment/<enrollment_id>', methods=['GET', 'POST'])
@swagger.marshal(schema.EnrollmentSchema, code=200)
def read_enrollment_by_bot(enrollment_id, enrollment_service: service.EnrollmentService):
    if not auth.validate_twilio_api_key(flask.request.args.get('api-key', '').strip()):
        return '', 401
    return enrollment_service.load(enrollment_id)


@studio_bot_page.route('/api/v1/twilio/enrollment-confirmation', methods=['GET', 'POST'])
def register_enrollment_confirmation(chatter_bot: MessageChatterBotV2):
    if not auth.validate_twilio_api_key(flask.request.args.get('api-key', '').strip()):
        return '', 401

    twilio_resp = flask.request.json
    logger.info(f'Schedule Twilio Responses:\n[DATA]: {twilio_resp}')

    phone = twilio_resp.get('from', '').strip()
    accept = twilio_resp['accept']
    phone = phone_helper.normalize_brazilian_9_digits(phone[10:])

    enrollment, status = chatter_bot.enrollment_confirmation(phone, accept)

    enrollment_response = {}
    if enrollment:
        enrollment_response = EnrollmentConfirmationResponseSchema().dump(enrollment, many=False)

    enrollment_response[BOT_STATUS] = status
    return enrollment_response, 200


@studio_bot_page.route('/api/v1/twilio/finish-enrollment', methods=['GET', 'POST'])
def finish_enrollment(chatter_bot: MessageChatterBotV2):
    if not auth.validate_twilio_api_key(flask.request.args.get('api-key', '').strip()):
        return '', 401

    twilio_resp = flask.request.json
    logger.info(f'Schedule Twilio Responses:\n[DATA]: {twilio_resp}')

    phone = twilio_resp.get('from', '').strip()
    phone = phone_helper.normalize_brazilian_9_digits(phone[10:])

    status = chatter_bot.finish_enrollment(phone)

    return {BOT_STATUS: status}, 200


@studio_bot_page.route('/api/v1/twilio/register-enrollment-nps-score', methods=['GET', 'POST'])
@swagger.use_schema(CourseNpsScoreSchema, parameter_name='course_nps_score')
@flask_ext.twilio_interaction_auth()
@swagger.marshal(CourseNpsScoreResponseSchema, code=HTTPStatus.OK)
def register_course_nps_rating(course_nps_score, chatter_bot: MessageChatterBotV2):
    status = chatter_bot.register_nps_score(course_nps_score[PHONE_FIELD], course_nps_score["score"])
    return CourseNpsScoreResponseSchema().dump({BOT_STATUS: status})


@studio_bot_page.route('/api/v1/twilio/enroll-user', methods=['GET', 'POST'])
@swagger.use_schema(EnrollUserBotSchema, parameter_name='payload')
@flask_ext.twilio_interaction_auth()
@swagger.marshal(StudioBotResponseSchema, code=HTTPStatus.OK)
def enroll(payload, chatter_bot: MessageChatterBotV2):
    status = chatter_bot.enroll(payload[PHONE_FIELD], payload["course_id"])
    return StudioBotResponseSchema().dump({BOT_STATUS: status})


@studio_bot_page.route('/api/v1/twilio/courses-portal-url', methods=['POST'])
@swagger.use_schema(CoursesRecommendationsBotSchema, parameter_name='payload')
@flask_ext.twilio_interaction_auth()
@swagger.marshal(CoursesPortalUrlBotResponseSchema, code=HTTPStatus.OK)
def get_course_portal_url(payload, chatter_bot: MessageChatterBotV2):
    portal_url, status = chatter_bot.get_courses_portal_url(payload["workspace_id"])
    return CoursesPortalUrlBotResponseSchema().dump({BOT_STATUS: status, "portal_url": portal_url})


@studio_bot_page.route('/api/v1/twilio/courses-recommendations', methods=['POST'])
@swagger.use_schema(CoursesRecommendationsBotSchema, parameter_name='payload')
@flask_ext.twilio_interaction_auth()
@swagger.marshal(CoursesRecommendationsBotResponseSchema, code=HTTPStatus.OK)
def get_courses_recommendations(payload, chatter_bot: MessageChatterBotV2):
    courses, status = chatter_bot.get_courses_recommendations_data(payload[PHONE_FIELD], payload["workspace_id"])
    return CoursesRecommendationsBotResponseSchema().dump({BOT_STATUS: status, **courses})


@studio_bot_page.route('/api/v1/twilio/give-up', methods=['POST'])
@swagger.use_schema(StudioBotSchema, parameter_name='payload')
@flask_ext.twilio_interaction_auth()
@swagger.marshal(GiveUpStudioBotResponseSchema, code=HTTPStatus.OK)
def give_up(payload, chatter_bot: MessageChatterBotV2):
    course_name, status = chatter_bot.give_up(payload[PHONE_FIELD])
    return GiveUpStudioBotResponseSchema().dump({BOT_STATUS: status, "course_name": course_name})
