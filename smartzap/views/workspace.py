import flask
from domain import schema, service
from domain.services.filter_resolver import create_pagination
from flask_extensions import flask_ext, swagger

workspace_page = flask.Blueprint('workspace', __name__)


@workspace_page.route('/api/v1/workspace/<workspace_id>', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.WorkspaceSchema, code=200)
def read_workspace(workspace_id, workspace_service: service.WorkspaceService):
    return workspace_service.load(workspace_id)


@workspace_page.route('/api/v1/workspace', methods=['POST'])
@flask_ext.check_permission('admin')
@swagger.use_schema(schema.WorkspaceSchema, parameter_name='workspace')
@swagger.marshal(schema.WorkspaceSchema, code=201)
def create_workspace(workspace, workspace_service: service.WorkspaceService):
    return workspace_service.add(workspace)


@workspace_page.route('/api/v1/workspace/<workspace_id>', methods=['PUT', 'PATCH'])
@flask_ext.check_permission('admin')
@swagger.use_schema(schema.WorkspaceSchema, parameter_name='workspace', partial=True)
def update_workspace(workspace_id, workspace, workspace_service: service.WorkspaceService):
    workspace_service.update(workspace_id, workspace)
    return '', 204


@workspace_page.route('/api/v1/workspace/<workspace_id>', methods=['DELETE'])
@flask_ext.check_permission('admin')
def delete_workspace(workspace_id, workspace_service: service.WorkspaceService):
    workspace_service.delete(workspace_id)
    return '', 204


@workspace_page.route('/api/v1/workspace/<workspace_id>/billing', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.BillingSchema, code=200)
def billing(workspace_id, workspace_service: service.WorkspaceService):
    workspace = workspace_service.load(workspace_id)
    return workspace_service.billing_report(workspace)


@workspace_page.route('/api/v1/workspace/<workspace_id>/billing/charges', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.paginate_schema(schema.ChargeSchema), code=200)
def billing_charges(workspace_id, workspace_service: service.WorkspaceService):
    charges = workspace_service.load_billing_charges(workspace_id)
    pagination = create_pagination(1, len(charges), 1, len(charges))
    return schema.build_pagination(charges, pagination)
