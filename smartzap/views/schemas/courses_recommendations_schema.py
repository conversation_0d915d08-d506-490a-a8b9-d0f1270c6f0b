from marshmallow import fields
from views.schemas.studio_bot_response_schema import StudioBotResponseSchema


class CoursesRecommendationsBotResponseSchema(StudioBotResponseSchema):
    course_1_id = fields.String(metadata={"description": "The ID of the first recommended course."})
    course_1_name = fields.String(metadata={"description": "The name of the first recommended course."})
    course_1_description = fields.String(metadata={"description": "The description of the first recommended course."})

    course_2_id = fields.String(metadata={"description": "The ID of the second recommended course."})
    course_2_name = fields.String(metadata={"description": "The name of the second recommended course."})
    course_2_description = fields.String(metadata={"description": "The description of the second recommended course."})

    course_3_id = fields.String(metadata={"description": "The ID of the third recommended course."})
    course_3_name = fields.String(metadata={"description": "The name of the third recommended course."})
    course_3_description = fields.String(metadata={"description": "The description of the third recommended course."})
