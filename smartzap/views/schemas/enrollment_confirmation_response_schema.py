import re

import marshmallow
import pytz
from domain.model import Enrollment
from domain.schema import CourseSchema, UserSchema, WorkspaceSchema
from marshmallow_sqlalchemy import SQLAlchemyAutoSchema, auto_field
from marshmallow_sqlalchemy.fields import Nested


class EnrollmentConfirmationResponseSchema(SQLAlchemyAutoSchema):
    class Meta:
        include_fk = True
        transient = True
        load_instance = True
        model = Enrollment

    workspace_id = auto_field(dump_only=True)
    timezone = auto_field(validate=marshmallow.validate.OneOf(pytz.all_timezones))
    user = Nested(UserSchema, many=False, dump_only=True)
    course = Nested(CourseSchema, many=False, dump_only=True)
    progress = auto_field(dump_only=True)
    workspace = Nested(WorkspaceSchema, many=False, dump_only=True)

    @marshmallow.post_dump
    def sanitize_course_description(self, data, **kwargs):
        """
        Remove all HTML tags from the course description.
        """
        if "course" in data and "description" in data["course"]:
            data["course"]["description"] = re.sub(r"<[^>]*>", "", data["course"]["description"])
        return data
