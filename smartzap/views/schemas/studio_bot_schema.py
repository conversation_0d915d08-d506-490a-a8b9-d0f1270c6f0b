from domain.phone_helper import normalize_brazilian_9_digits
from marshmallow import Schema, fields, pre_load


class StudioBotSchema(Schema):
    phone = fields.String(required=True)

    @pre_load
    def normalize_phone(self, data, **kwargs):
        """Normalize the phone number (whatsapp:+554999999999) before validation."""
        if 'phone' in data:
            data['phone'] = normalize_brazilian_9_digits(data['phone'].strip()[10:])
        return data
