import marshmallow
from domain import model
from domain.schema import (
    CourseCategorySchema,
    PowerTextValidator,
    UserSchema,
    WorkspaceSchema,
    default_max_description_length,
    default_max_name_length,
)
from marshmallow_sqlalchemy import SQLAlchemyAutoSchema, auto_field
from webargs.fields import Nested


class CourseSchema(SQLAlchemyAutoSchema):
    class Meta:
        include_fk = True
        transient = True
        load_instance = True
        model = model.Course
        exclude = ("user_creator_id", 'workspace_owner_id')

    name = auto_field(validate=PowerTextValidator(default_max_name_length, True, True, True))
    description = auto_field(validate=PowerTextValidator(default_max_description_length, True, True, True))
    duration = auto_field(dump_only=True)
    points = auto_field(dump_only=True)
    quiz_performance_weight = auto_field(validate=marshmallow.validate.Range(min=0, max=10))
    content_performance_weight = auto_field(validate=marshmallow.validate.Range(min=0, max=10))
    category = Nested(CourseCategorySchema, many=False, dump_only=True)
    workspace_owner = Nested(WorkspaceSchema, many=False, dump_only=True)
    user_creator = Nested(UserSchema, many=False, dump_only=True)

    @marshmallow.post_dump()
    def _prepare_totals(self, data, many, **kwargs):
        data['total_users_enrolled'] = 0
        data['total_users_completed'] = 0
        data['total_lessons'] = 0
        return data
