import re

from marshmallow import ValidationError, fields, pre_load
from views.schemas.studio_bot_schema import StudioBotSchema


class EnrollUserBotSchema(StudioBotSchema):
    selected_choice = fields.String(required=True)
    course_id = fields.String()

    @pre_load
    def extract_course_id(self, data, **kwargs):
        """
        Extracts the UUID (course_id) from the selected_choice value.
        Expected format: enroll_in_<uuid>
        """
        if 'selected_choice' in data:
            match = re.match(r'enroll_in_([a-f0-9\-]+)', data['selected_choice'])
            if match:
                data['course_id'] = match.group(1)
            else:
                raise ValidationError("Invalid selected_choice format. Expected format: 'enroll_in_<uuid>'.")
        return data
