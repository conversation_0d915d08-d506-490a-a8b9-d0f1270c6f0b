import re

from marshmallow import ValidationError, fields, pre_load
from views.schemas.studio_bot_schema import StudioBotSchema


class CoursesRecommendationsBotSchema(StudioBotSchema):
    selected_workspace = fields.String(required=True)
    workspace_id = fields.String()

    @pre_load
    def extract_workspace_id(self, data, **kwargs):
        """
        Extracts the UUID (workspace_id) from the selected_workspace value.
        Expected format: *_<uuid>
        """
        if 'selected_workspace' in data:
            match = re.match(r'.*?_([a-f0-9\-]{36})', data['selected_workspace'])
            if match:
                data['workspace_id'] = match.group(1)
            else:
                raise ValidationError("Invalid selected_workspace format. Expected format: '*_<uuid>'.")
        return data
