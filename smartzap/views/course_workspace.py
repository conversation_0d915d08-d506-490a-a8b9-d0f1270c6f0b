import flask
from domain import schema, service
from flask_extensions import flask_ext, swagger

course_workspace_page = flask.Blueprint('course-workspace', __name__)


@course_workspace_page.route('/api/v1/course-workspace/<course_workspace_id>', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.CourseWorkspaceSchema, code=200)
def read_course_workspace(course_workspace_id, course_workspace_service: service.CourseWorkspaceService):
    return course_workspace_service.load(course_workspace_id)


@course_workspace_page.route('/api/v1/course-workspace', methods=['POST'])
@flask_ext.check_permission('admin')
@swagger.use_schema(schema.CourseWorkspaceSchema, parameter_name='course_workspace')
@swagger.marshal(schema.CourseWorkspaceSchema, code=201)
def create_course_workspace(course_workspace, course_workspace_service: service.CourseWorkspaceService):
    return course_workspace_service.add(course_workspace)


@course_workspace_page.route('/api/v1/course-workspace/<course_workspace_id>', methods=['PUT', 'PATCH'])
@flask_ext.check_permission('admin')
@swagger.use_schema(schema.CourseWorkspaceSchema, parameter_name='course_workspace', partial=True)
def update_course_workspace(
        course_workspace_id,
        course_workspace,
        course_workspace_service: service.CourseWorkspaceService
):
    course_workspace_service.update(course_workspace_id, course_workspace)
    return '', 204


@course_workspace_page.route('/api/v1/course-workspace/<course_workspace_id>', methods=['DELETE'])
@flask_ext.check_permission('admin')
def delete_course_workspace(course_workspace_id, course_workspace_service: service.CourseWorkspaceService):
    course_workspace_service.delete(course_workspace_id)
    return '', 204
