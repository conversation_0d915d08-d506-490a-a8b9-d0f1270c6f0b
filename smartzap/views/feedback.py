import flask
from domain import schema, service
from flask_extensions import flask_ext, swagger

feedback_page = flask.Blueprint('feedback', __name__)


@feedback_page.route('/api/v1/feedback/<feedback_id>', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.FeedbackSchema, code=200)
def read_feedback(feedback_id, feedback_service: service.FeedbackService):
    return feedback_service.load(feedback_id)


@feedback_page.route('/api/v1/feedback', methods=['POST'])
@flask_ext.check_permission('admin')
@swagger.use_schema(schema.FeedbackSchema, parameter_name='feedback')
@swagger.marshal(schema.FeedbackSchema, code=201)
def create_feedback(feedback, feedback_service: service.FeedbackService):
    return feedback_service.add(feedback)


@feedback_page.route('/api/v1/feedback/<feedback_id>', methods=['PUT', 'PATCH'])
@flask_ext.check_permission('admin')
@swagger.use_schema(schema.FeedbackSchema, parameter_name='feedback', partial=True)
def update_feedback(feedback_id, feedback, feedback_service: service.FeedbackService):
    feedback_service.update(feedback_id, feedback)
    return '', 204


@feedback_page.route('/api/v1/feedback/<feedback_id>', methods=['DELETE'])
@flask_ext.check_permission('admin')
def delete_feedback(feedback_id, feedback_service: service.FeedbackService):
    feedback_service.delete(feedback_id)
    return '', 204
