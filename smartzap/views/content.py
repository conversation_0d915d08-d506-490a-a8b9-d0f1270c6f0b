import flask
from domain import client, schema, service
from flask_extensions import flask_ext, swagger, webfilters

content_page = flask.Blueprint('content', __name__)


@content_page.route('/api/v1/content/<content_id>', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.ContentSchema, code=200)
def read_content(content_id, content_service: service.ContentService):
    return content_service.load(content_id)


@content_page.route('/api/v1/content', methods=['POST'])
@flask_ext.check_permission('admin')
@swagger.use_schema(schema.ContentSchema, parameter_name='content')
@swagger.marshal(schema.ContentSchema, code=201)
def create_content(content, content_service: service.ContentService):
    return content_service.add(content)


@content_page.route('/api/v1/content/<content_id>', methods=['PUT', 'PATCH'])
@flask_ext.check_permission('admin')
@swagger.use_schema(schema.ContentSchema, parameter_name='content', partial=True)
def update_content(content_id, content, content_service: service.ContentService):
    content_service.update(content_id, content)
    return '', 204


@content_page.route('/api/v1/content/<content_id>', methods=['DELETE'])
@flask_ext.check_permission('admin')
def delete_content(content_id, content_service: service.ContentService, kontent: client.KontentClient):
    content_service.delete_with_kontent(content_id, kontent)
    return '', 204


@content_page.route('/api/v1/content/<content_id>/activity', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.paginate_schema(schema.ActivitySchema), code=200)
def read_all_activities_from_content(content_id, content_service: service.ContentService):
    filters = webfilters.load_filters()
    activities, pagination = content_service.load_all_activities(content_id, filters)
    return schema.build_pagination(activities, pagination)


@content_page.route('/api/v1/content/<content_id>/enrollment/<enrollment_id>/renew-access', methods=['POST'])
@flask_ext.check_permission('admin')
def renew_content_access(content_id, enrollment_id, schedule_service: service.ScheduleService):
    schedule_service.resend_content(enrollment_id, content_id)
    return '', 204


@content_page.route('/api/v1/content/<content_id>/re-schedule', methods=['POST'])
@flask_ext.check_permission('admin')
@swagger.use_schema(schema.ReScheduleSchema, parameter_name='re_schedule', partial=False)
def re_schedule_content(content_id, re_schedule, course_service: service.CourseService,
                        schedule_service: service.ScheduleService):
    content = course_service.load_content(content_id)
    schedule_service.re_schedule_content(content, re_schedule['send_date'], re_schedule['period'])
    return '', 204
