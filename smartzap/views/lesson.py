import flask
from domain import schema, service
from flask_extensions import flask_ext, swagger, webfilters

lesson_page = flask.Blueprint('lesson', __name__)


@lesson_page.route('/api/v1/lesson/<lesson_id>', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.LessonSchema, code=200)
def read_lesson(lesson_id, lesson_service: service.LessonService):
    return lesson_service.load(lesson_id)


@lesson_page.route('/api/v1/lesson', methods=['POST'])
@flask_ext.check_permission('admin')
@swagger.use_schema(schema.LessonSchema, parameter_name='lesson')
@swagger.marshal(schema.LessonSchema, code=201)
def create_lesson(lesson, lesson_service: service.LessonService):
    return lesson_service.add(lesson)


@lesson_page.route('/api/v1/lesson/<lesson_id>', methods=['PUT', 'PATCH'])
@flask_ext.check_permission('admin')
@swagger.use_schema(schema.LessonSchema, parameter_name='lesson', partial=True)
def update_lesson(lesson_id, lesson, lesson_service: service.LessonService):
    lesson_service.update(lesson_id, lesson)
    return '', 204


@lesson_page.route('/api/v1/lesson/<lesson_id>', methods=['DELETE'])
@flask_ext.check_permission('admin')
def delete_lesson(lesson_id, lesson_service: service.LessonService):
    lesson_service.delete(lesson_id)
    return '', 204


@lesson_page.route('/api/v1/lesson/<lesson_id>/content', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.paginate_schema(schema.ContentSchema), code=200)
def read_all_content_from_lesson(lesson_id, lesson_service: service.LessonService):
    filters = webfilters.load_filters()
    contents, pagination = lesson_service.load_all_contents(lesson_id, filters)
    return schema.build_pagination(contents, pagination)
