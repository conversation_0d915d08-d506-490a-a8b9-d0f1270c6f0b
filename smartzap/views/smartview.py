from domain import client, schema, service
from flask import Blueprint, json, jsonify, request
from flask_extensions import flask_ext, swagger

smartview_page = Blueprint('smartview', __name__)


@smartview_page.route('/api/v1/view/content/<content_id>', methods=['GET'])
@flask_ext.smart_view()
def read_view_content(content_id, user_id: service.UserId, enrollment_id: service.EnrollmentId,
                      client_id: service.ClientId, content_service: service.ContentService,
                      enrollment_service: service.EnrollmentService, kontent: client.KontentClient):
    subject = enrollment_id or user_id
    if not enrollment_service.user_can_access_contents(subject):
        return '', 403

    content = content_service.load(content_id)

    if content.type.name == 'Question':
        return kontent.load_exam(content.learn_content, enrollment_id, client_id), 200

    data = kontent.load_content(content.learn_content)
    return data, 200


@smartview_page.route('/api/v1/view/answers', methods=['POST'])
@flask_ext.smart_view()
def create_view_answers(kontent: client.KontentClient, enrollment_id: service.EnrollmentId,
                        client_id: service.ClientId):
    response = kontent.save_answers(request.json, client_id, enrollment_id)
    return jsonify(json.loads(response.text)), response.status_code


@smartview_page.route('/api/v1/view/activity', methods=['POST'])
@flask_ext.smart_view()
@swagger.use_schema(schema.ActivitySchema, parameter_name='activity')
@swagger.marshal(schema.ActivitySchema, code=201)
def create_view_activity(activity, enrollment_id: service.EnrollmentId, activity_service: service.ActivityService):
    activity.enrollment_id = enrollment_id
    activity.user_id = None
    return activity_service.add(activity)


@smartview_page.route('/api/v1/view/activity/<activity_id>', methods=['PUT', 'PATCH'])
@flask_ext.smart_view()
@swagger.use_schema(schema.ActivitySchema, parameter_name='activity', partial=True)
def update_activity(activity_id, activity, activity_service: service.ActivityService):
    activity_service.update(activity_id, activity)
    return '', 204


@smartview_page.route('/api/v1/view/activity/<activity_id>', methods=['GET'])
@flask_ext.smart_view()
@swagger.marshal(schema.ActivitySchema, code=200)
def read_activity(activity_id, activity_service: service.ActivityService):
    return activity_service.load(activity_id)
