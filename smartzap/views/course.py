import flask
from domain import model, schema, service
from domain.model import CourseStatus
from flask_extensions import flask_ext, swagger, webfilters

course_page = flask.Blueprint('course', __name__)


@course_page.route('/api/v1/course', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.paginate_schema(schema.CourseSchema), code=200)
def read_all_courses(course_service: service.CourseService):
    filters = webfilters.load_filters(override_filters={"status__ne": CourseStatus.deleting})
    courses, pagination = course_service.load_filters(filters)
    return schema.build_pagination(courses, pagination)


@course_page.route('/api/v1/course/<course_id>', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.CourseSchema, code=200)
def read_course(course_id, course_service: service.CourseService):
    return course_service.load(course_id)


@course_page.route('/api/v1/course', methods=['POST'])
@flask_ext.check_permission('admin')
@swagger.use_schema(schema.CourseSchema, parameter_name='course')
@swagger.marshal(schema.CourseSchema, code=201)
def create_course(course, course_service: service.CourseService):
    return course_service.add(course)


@course_page.route('/api/v1/course/<course_id>', methods=['PUT', 'PATCH'])
@flask_ext.check_permission('admin')
@swagger.use_schema(schema.CourseSchema, parameter_name='course', partial=True)
def update_course(course_id, course, course_service: service.CourseService):
    course_service.update(course_id, course)
    return '', 204


@course_page.route('/api/v1/course/<course_id>/transfer-ownership/<user_id>', methods=['POST'])
@flask_ext.check_permission('admin')
def transfer_course_ownership(course_id, user_id, course_service: service.CourseService,
                              user_service: service.UserService):
    user_service.load(user_id)
    course_service.transfer_ownership(course_id, user_id)
    return '', 204


@course_page.route('/api/v1/course/<course_id>/publish', methods=['POST'])
@flask_ext.check_permission('admin')
def publish_course(course_id, course_service: service.CourseService):
    course_service.publish(course_id)
    return '', 204


@course_page.route('/api/v1/course/<course_id>', methods=['DELETE'])
@flask_ext.check_permission('admin')
def delete_course(course_id, course_service: service.CourseService):
    course_service.schedule_delete(course_id)
    return '', 204


@course_page.route('/api/v1/course/<course_id>/enrollment', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.paginate_schema(schema.EnrollmentSchema, 'Course'), code=200)
def read_all_enrollments_from_course(course_id, course_service: service.CourseService):
    search_columns = ["user__name", "user__phone"]
    filters = webfilters.load_filters(search_columns=search_columns)
    enrollments, pagination = course_service.load_all_enrollments(course_id, filters)
    return schema.build_pagination(enrollments, pagination)


@course_page.route('/api/v1/course/<course_id>/lesson', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.paginate_schema(schema.LessonSchema), code=200)
def read_all_lessons_from_course(course_id, course_service: service.CourseService):
    filters = webfilters.load_filters()
    lessons, pagination = course_service.load_all_lessons(course_id, filters)
    return schema.build_pagination(lessons, pagination)


@course_page.route('/api/v1/course/language', methods=['GET'])
@flask_ext.check_permission('admin')
def read_all_course_languages():
    return {'idioms': model.languages_description}, 200
