import os

import flask
from domain import client, schema, service
from flask_extensions import flask_ext, swagger, webfilters
from views import utils

content_type_page = flask.Blueprint('content-type', __name__)


@content_type_page.route('/api/v1/content-type/<content_type_id>', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.ContentTypeSchema, code=200)
def read_content_type(content_type_id, content_type_service: service.ContentTypeService):
    return content_type_service.load(content_type_id)


@content_type_page.route('/api/v1/content-type', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.paginate_schema(schema.ContentTypeSchema), code=200)
def read_all_content_type(content_type_service: service.ContentTypeService):
    filters = webfilters.load_filters()
    types, pagination = content_type_service.load_filters(filters)
    return schema.build_pagination(types, pagination)


@content_type_page.route('/api/v1/upload/image', methods=['POST'])
@flask_ext.check_permission('admin')
def image_upload(s3_client: client.S3Client):
    if 'file' not in flask.request.files:
        return 'File not found', 422

    file = flask.request.files['file']
    filename = file.filename.rsplit('"')[0]
    if filename == '':
        return 'No selected file', 422

    allowed_images = {
        'image/jpeg': 'jpg',
        'image/png': 'png',
    }
    file_extension = allowed_images.get(file.content_type)

    if not file_extension:
        return 'Invalid image type', 422

    if file_extension in ['jpg', 'jpeg']:
        return_data = s3_client.send_file(file_obj=file, content_type='image/jpeg')
    else:
        file_path = utils.convert_png_to_jpg(file)
        return_data = s3_client.send_file(file_path=file_path, content_type='image/jpeg')
        os.remove(file_path)
    return return_data, 201
