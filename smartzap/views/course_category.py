import flask
from domain import schema, service
from flask_extensions import flask_ext, swagger, webfilters

course_category_page = flask.Blueprint('course-category', __name__)


@course_category_page.route('/api/v1/course-category/<course_category_id>', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.CourseCategorySchema, code=200)
def read_course_category(course_category_id, course_category_service: service.CourseCategoryService):
    return course_category_service.load(course_category_id)


@course_category_page.route('/api/v1/course-category', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.paginate_schema(schema.CourseCategorySchema), code=200)
def read_all_course_category(course_category_service: service.CourseCategoryService):
    filters = webfilters.load_filters()
    categories, pagination = course_category_service.load_filters(filters)
    return schema.build_pagination(categories, pagination)
