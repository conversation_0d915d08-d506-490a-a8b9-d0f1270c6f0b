import tempfile

from domain import schema
from PIL import Image


def import_xlsx_users(file, xls_importer):
    filename = file.filename.rsplit('"')[0]
    if filename == '':
        return 'No selected file', 422

    file_extension = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
    if file_extension in ['xlsx']:
        with tempfile.NamedTemporaryFile(mode='wb+') as fp:
            file.save(fp.name)
            user_ok, user_not_ok = xls_importer.import_file(fp.name)
            data_ok = schema.user_schema.dump(user_ok, many=True)
            return {'users': data_ok, 'errors': user_not_ok}, 201
    return 'Invalid file extension', 422


def convert_png_to_jpg(file, delete_png=True):
    with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as jpg_file, \
            tempfile.NamedTemporaryFile(suffix='.png', delete=delete_png) as png_file:
        file.save(png_file.name)
        img = Image.open(png_file.name)
        rgb_im = img.convert('RGB')
        rgb_im.save(jpg_file.name)
        return jpg_file.name


