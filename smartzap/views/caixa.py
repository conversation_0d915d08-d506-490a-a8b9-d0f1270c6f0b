from http import H<PERSON>PStatus

import flask
from domain import schema, service
from domain.services.user_service import UserServiceV2
from flask_extensions import flask_ext, swagger, webfilters
from psycopg2.errorcodes import FOREIGN_KEY_VIOLATION
from sqlalchemy.exc import IntegrityError
from tasks.task import enroll_user
from views.schemas.caixa.course_schema import CourseSchema
from views.schemas.caixa.give_up_enrollment_schema import GiveUpEnrollmentSchema

caixa_page = flask.Blueprint('user', __name__)


@caixa_page.route('/caixa-api/v1/users', methods=['POST'])
@swagger.use_schema(schema.UserWithEmailRequiredSchema, parameter_name='user')
@flask_ext.check_caixa_x_client()
@swagger.marshal(schema.UserResponseSchema)
def create_user(user, user_service: UserServiceV2):
    return user_service.add(user)


@caixa_page.route('/caixa-api/v1/users', methods=['GET'])
@flask_ext.check_caixa_x_client()
@swagger.marshal(schema.paginate_schema(schema.UserSchema), code=200)
def read_users(user_service: UserServiceV2):
    filters = webfilters.load_filters()
    users, pagination = user_service.load_filters(filters)
    return schema.build_pagination(users, pagination)


@caixa_page.route('/caixa-api/v1/courses', methods=['GET'])
@flask_ext.check_caixa_x_client()
@swagger.marshal(schema.paginate_schema(CourseSchema), code=200)
def read_all_courses(course_service: service.CourseService):
    filters = webfilters.load_filters()
    courses, pagination = course_service.load_filters(filters)
    return schema.build_pagination(courses, pagination)


@caixa_page.route('/caixa-api/v1/enrollments', methods=['POST'])
@flask_ext.check_caixa_x_client()
@swagger.use_schema(schema.EnrollmentSchema, parameter_name='enrollment')
def create_enrollment(enrollment, enrollment_service: service.EnrollmentService):
    enrollment.workspace_id = flask.g.client_id
    try:
        enrollment_service.add(enrollment)
    except IntegrityError as integrity_error:
        user_not_found_error = (
                integrity_error.orig.pgcode == FOREIGN_KEY_VIOLATION and 'table "user"' in str(integrity_error)
        )
        if not user_not_found_error:
            raise integrity_error
        enroll_user.delay(
            enrollment.user_id,
            enrollment.course_id,
            enrollment.workspace_id,
            enrollment.start_date
        )

    return "", HTTPStatus.NO_CONTENT


@caixa_page.route('/caixa-api/v1/enrollments/give-up', methods=['POST'])
@flask_ext.check_caixa_x_client()
@swagger.use_schema(GiveUpEnrollmentSchema, parameter_name='input_schema')
def give_up_enrollment(input_schema, enrollment_service: service.EnrollmentService):
    enrollment = enrollment_service.load_first_active_from_user(str(input_schema["user_id"]))

    if not enrollment:
        return {"error": "Not found any enrollment in progress"}, HTTPStatus.NOT_FOUND

    enrollment_service.cancel(enrollment)

    return "", HTTPStatus.NO_CONTENT
