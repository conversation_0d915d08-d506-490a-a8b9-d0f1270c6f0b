import flask
from domain import service
from flask_extensions import flask_ext, webfilters
from tasks import task

stats_page = flask.Blueprint('stats', __name__)


@stats_page.route('/api/v1/stats/user/csv', methods=['POST', 'GET'])
@flask_ext.check_permission('admin')
def read_users_stats_csv(client_id: service.ClientId, user_id: service.UserId):
    filters = webfilters.load_filters()
    task.users_report_csv.delay(client_id, user_id, filters)
    return '', 204


@stats_page.route('/api/v1/stats/course/<course_id>/enrollment/in-progress/csv', methods=['POST', 'GET'])
@flask_ext.check_permission('admin')
def read_all_enrollments_in_progress_stats_csv(course_id, client_id: service.ClientId, user_id: service.UserId):
    token = flask.request.headers.get('Authorization')
    task.in_progress_enrollments_report_csv(client_id, user_id, course_id, token)
    return '', 204


@stats_page.route('/api/v1/stats/course/<course_id>/enrollment/completed/csv', methods=['POST', 'GET'])
@flask_ext.check_permission('admin')
def read_all_enrollments_completed_stats_csv(course_id, client_id: service.ClientId, user_id: service.UserId):
    token = flask.request.headers.get('Authorization')
    task.completed_enrollments_report_csv(client_id, user_id, course_id, token)
    return '', 204


@stats_page.route('/api/v1/stats/course/<course_id>/activity/csv', methods=['POST', 'GET'])
@flask_ext.check_permission('admin')
def read_all_activities_stats_csv(course_id, client_id: service.ClientId, user_id: service.UserId):
    token = flask.request.headers.get('Authorization')
    task.activities_report_csv(client_id, user_id, course_id, token)
    return '', 204


@stats_page.route('/api/v1/stats/course/<course_id>/quizzes/csv', methods=['POST', 'GET'])
@flask_ext.check_permission('admin')
def read_quizzes_stats_csv(course_id, client_id: service.ClientId, user_id: service.UserId):
    task.quizzes_report_csv.delay(client_id, user_id, course_id)
    return '', 204


@stats_page.route('/api/v1/stats/course/<course_id>/overview/pdf', methods=['POST', 'GET'])
@flask_ext.check_permission('admin')
def read_course_overview_pdf(course_id, client_id: service.ClientId, user_id: service.UserId):
    task.learn_analytics_report_pdf_smartzap_course.delay(client_id, user_id, course_id)
    return '', 204
