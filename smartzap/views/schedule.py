import flask
from domain import phone_helper, schema, service
from flask_extensions import auth, flask_ext, swagger, webfilters
from logger import logger
from twilio.twiml.messaging_response import MessagingResponse

schedule_page = flask.Blueprint('schedule', __name__)


@schedule_page.route('/api/v1/schedule/<schedule_id>', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.ScheduleSchema, code=200)
def read_schedule(schedule_id, schedule_service: service.ScheduleService):
    return schedule_service.load(schedule_id)


@schedule_page.route('/api/v1/schedule/<schedule_id>/callback/twilio', methods=['GET', 'POST'])
def schedule_callback_twilio(schedule_id, schedule_service: service.ScheduleService):
    sub, _, _ = auth.decode_token(flask.request.args.get('token', '').strip())
    if not sub:
        return '', 401

    twilio_resp = flask.request.form
    logger.info(f'Schedule Callback:\n[ID]: {schedule_id}\n[DATA]: {twilio_resp}')

    status = twilio_resp.get('SmsStatus', '').strip()
    if status in ['failed', 'undelivered']:
        schedule_service.set_failed(schedule_id)
    if status == 'sent':
        schedule_service.set_delivered(schedule_id)
    return '', 200


@schedule_page.route('/api/v1/schedule/twilio/responses', methods=['GET', 'POST'])
def schedule_twilio_responses(chatter_bot: service.MessageChatterBot):
    if not auth.validate_twilio_api_key(flask.request.args.get('api-key', '').strip()):
        return '', 401

    twilio_resp = flask.request.form
    logger.info(f'Schedule Twilio Responses:\n[DATA]: {twilio_resp}')

    status = twilio_resp.get('SmsStatus', '').strip()
    if status == 'received':
        message = twilio_resp.get('Body', '').strip()
        phone = twilio_resp.get('From', '').strip()
        phone = phone_helper.normalize_brazilian_9_digits(phone[10:])
        answer_type = chatter_bot.answer_type(message)
        reply_message, image_url = chatter_bot.process_message(phone, answer_type, message)
        if reply_message:
            resp = MessagingResponse()
            msg = resp.message(reply_message)
            if image_url:
                msg.media(image_url)
            return str(resp), 200
    return '', 200


@schedule_page.route('/api/v1/schedule/sent-messages-count', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.MessagesSentCountSchema, code=200)
def read_messages_sent_count(schedule_service: service.ScheduleService):
    filters = webfilters.load_filters()
    messages_count = schedule_service.load_messages_sent_count(filters)
    return schema.MessagesSentCountSchema().dump({"messages_sent_count": messages_count})
