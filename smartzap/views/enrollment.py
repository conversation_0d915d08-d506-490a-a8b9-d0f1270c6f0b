import flask
import marshmallow
from domain import schema, service
from domain.exceptions.service_exceptions import IntegrityException
from flask_extensions import flask_ext, swagger, webargs, webfilters
from sqlalchemy.exc import DatabaseError
from views import utils
from webargs.flaskparser import parser

enrollment_page = flask.Blueprint('enrollment', __name__)


@enrollment_page.route('/api/v1/enrollment/<enrollment_id>', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.EnrollmentSchema, code=200)
def read_enrollment(enrollment_id, enrollment_service: service.EnrollmentService):
    return enrollment_service.load(enrollment_id)


@enrollment_page.route('/api/v1/enrollment', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.paginate_schema(schema.EnrollmentSchema), code=200)
def read_all_enrollments(enrollment_service: service.EnrollmentService):
    search_columns = ["user__name", "user__phone"]
    filters = webfilters.load_filters(
        search_columns=search_columns, override_filters={"workspace_id": flask.g.client_id}
    )
    enrollments, pagination = enrollment_service.load_filters(filters)
    return schema.build_pagination(enrollments, pagination)


@enrollment_page.route('/api/v1/enrollment', methods=['POST'])
@flask_ext.check_permission('admin')
@swagger.use_schema(schema.EnrollmentSchema, parameter_name='enrollment')
@swagger.marshal(schema.EnrollmentSchema, code=201)
def create_enrollment(enrollment, enrollment_service: service.EnrollmentService):
    enrollment.workspace_id = flask.g.client_id
    return enrollment_service.add(enrollment)


@enrollment_page.route('/api/v1/enrollment/user', methods=['POST'])
@flask_ext.check_permission('admin')
@swagger.doc_body(schema.UserEnrollmentSchema)
@swagger.marshal(schema.EnrollmentSchema, code=201)
def create_user_enrollment(enrollment_service: service.EnrollmentService, user_service: service.UserService):
    user_enrollment = flask.request.json
    schema.user_enrollment_schema.validate(user_enrollment)
    user = schema.user_schema.load(user_enrollment, unknown=marshmallow.INCLUDE)
    user = user_service.add(user)
    enrollment_data = {**user_enrollment, 'user_id': user.id, 'workspace_id': flask.g.client_id}
    enrollment = schema.enrollment_schema.load(enrollment_data, unknown=marshmallow.EXCLUDE)
    enrollment.workspace_id = flask.g.client_id
    return enrollment_service.add(enrollment)


@enrollment_page.route('/api/v1/enrollment/batch', methods=['POST'])
@flask_ext.check_permission('admin')
@swagger.doc_body(schema.EnrollmentBatchSchema)
def create_enrollment_batch(enrollment_service: service.EnrollmentService):
    enrollment_batch = flask.request.json
    schema.enrollment_batch_schema.validate(enrollment_batch)
    enrollments = []
    users_error = []
    users = enrollment_batch.get('users', [])
    del enrollment_batch['users']
    for user_id in users:
        try:
            enrollment_data = {**enrollment_batch, 'user_id': user_id}
            enrollment = schema.enrollment_schema.load(enrollment_data)
            enrollment.workspace_id = flask.g.client_id
            enrollment_service.add(enrollment)
            enrollments.append(enrollment)
        except DatabaseError:
            users_error.append(user_id)

    enrollments_data = schema.enrollment_schema.dump(enrollments, many=True)
    return {'enrollments': enrollments_data, 'errors': users_error}, 201


@enrollment_page.route('/api/v1/enrollment/file', methods=['POST'])
@flask_ext.check_permission('admin')
def create_enrollment_file(enrollment_service: service.EnrollmentService,
                           xls_importer: service.UserXlsImporter):
    if 'file' not in flask.request.files:
        return 'File not found', 422

    file = flask.request.files['file']
    data, code = utils.import_xlsx_users(file, xls_importer)
    if code != 201:
        return data, code

    users_from_file = data
    enrollments = []
    enrollment_errors = []
    for user in users_from_file['users']:
        try:
            enrollment = parser.parse(webargs.EnrollmentFileArgsSchema, location='form')
            enrollment.workspace_id = flask.g.client_id
            enrollment.user_id = user['id']
            enrollment_service.add(enrollment)
            enrollments.append(enrollment)
        except DatabaseError:
            enrollment_errors.append(user)
        except IntegrityException as e:
            user['error'] = [e.message, ]
            enrollment_errors.append(user)

    enrollments_data = schema.enrollment_schema.dump(enrollments, many=True)
    return {'enrollments': enrollments_data, 'user_errors': users_from_file['errors'],
            'enrollment_errors': enrollment_errors}, 201


@enrollment_page.route('/api/v1/enrollment/<enrollment_id>', methods=['PUT', 'PATCH'])
@flask_ext.check_permission('admin')
@swagger.use_schema(schema.EnrollmentSchema, parameter_name='enrollment', partial=True)
def update_enrollment(enrollment_id, enrollment, enrollment_service: service.EnrollmentService):
    enrollment = enrollment_service.update(enrollment_id, enrollment)
    return schema.EnrollmentSchema().dump(enrollment), 200


@enrollment_page.route('/api/v1/enrollment/<enrollment_id>', methods=['DELETE'])
@flask_ext.check_permission('admin')
def delete_enrollment(enrollment_id, enrollment_service: service.EnrollmentService):
    enrollment_service.delete(enrollment_id)
    return '', 204


@enrollment_page.route('/api/v1/enrollment/batch', methods=['DELETE'])
@flask_ext.check_permission('admin')
@swagger.use_schema(schema.IdsSchema, parameter_name='ids')
def delete_enrollment_batch(ids, enrollment_service: service.EnrollmentService):
    enrollment_service.delete_in_batch(ids.get('ids', []))
    return '', 204


@enrollment_page.route('/api/v1/enrollment/<enrollment_id>/token', methods=['GET'])
@flask_ext.check_permission('admin')
def read_enrollment_token(enrollment_id, user_service: service.UserService,
                          enrollment_service: service.EnrollmentService,
                          schedule_admin_service: service.ScheduleAdminService):
    enrollment = enrollment_service.load(enrollment_id)
    _ = user_service.load(enrollment.user_id)
    return schedule_admin_service.create_token(enrollment.user_id, '', flask.g.client_id,
                                               enrollment.workspace.user_token_expiration, enrollment.id)


@enrollment_page.route('/api/v1/enrollment/<enrollment_id>/tracking', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.list_schema(schema.EnrollmentTrackingSchema), code=200)
def read_enrollment_tracking(enrollment_id, course_report: service.CourseReportService):
    tracking_data = course_report.enrollment_tracking_data(enrollment_id)
    return schema.build_list(tracking_data)


@enrollment_page.route('/api/v1/enrollment/status-count', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.EnrollmentStatusCountSchema, code=200)
def read_enrollment_status_count(enrollment_service: service.EnrollmentService):
    filters = webfilters.load_filters()
    enrollment_stats = enrollment_service.load_enrollment_stats(filters)
    return schema.EnrollmentStatusCountSchema().dump(enrollment_stats)


@enrollment_page.route('/api/v1/enrollment/messages-pending-count', methods=['GET'])
@flask_ext.check_permission('admin')
@swagger.marshal(schema.EnrollmentMessagesPendingCountSchema, code=200)
def read_enrollment_messages_pending_count(enrollment_service: service.EnrollmentService):
    filters = webfilters.load_filters()
    message_pending_count = enrollment_service.load_messages_pending_count(filters)
    return schema.EnrollmentMessagesPendingCountSchema().dump({'message_pending_count': message_pending_count})
