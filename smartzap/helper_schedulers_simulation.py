import random
import uuid

import freezegun
from domain import database, model, service
from logger import logger
from tests import utils


def db_session():
    database_url = 'postgresql://smartzap:smartzap123@127.0.0.1:5432/smartzap_local_db'
    _, db_session = database.create_engine_and_session(database_url)
    return db_session


def create_workspace(db):
    workspace = model.Workspace()
    workspace.id = str(uuid.uuid4())
    workspace.name = 'Workspace'
    workspace.user_token_expiration = 3
    workspace.end_course_schedule = 2
    workspace.monthly_plan = 50
    workspace.billing_cycle_day = 10
    db.add(workspace)
    db.flush()
    return workspace


def create_phone_number():
    phone_number = [5, 5, 4, 8, 9]
    for _ in range(0, 8):
        phone_number.append(random.randint(0, 9))

    return ''.join(map(str, phone_number))


def create_schedules(schedules_size):
    db = db_session()
    workspace = create_workspace(db)
    schedule_service = service.ScheduleService(db, workspace.id)
    with freezegun.freeze_time('2022-09-29 09:08:07', tz_offset=-3):
        for _ in range(0, schedules_size):
            user = utils.create_user(db, create_phone_number(), workspace.id)
            course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
            enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')

            schedule_service.create_schedules(enrollment)


if __name__ == '__main__':
    create_schedules(2000)
    logger.info('Done')
