import base64
import datetime
import json
import os

import boto3
import botocore
import ucache
from domain import config, di
from logger import logger

AWS_ACCESS_KEY_ID = "<access_key>"
AWS_SECRET_ACCESS_KEY = "<secret>"
AWS_REGION_NAME = "sa-east-1"
AWS_BUCKET_NAME_REPORT = "keeps.reports"
AWS_LAMBDA_ACCESS_KEY_ID = "<access_key>"
AWS_LAMBDA_SECRET_ACCESS_KEY = "<secret>"
AWS_LAMBDA_REGION_NAME = "us-east-1"


s3 = boto3.resource(
    "s3",
    aws_access_key_id=AWS_ACCESS_KEY_ID,
    aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
    region_name=AWS_REGION_NAME
)

lambda_client = boto3.client(
    "lambda",
    aws_access_key_id=AWS_LAMBDA_ACCESS_KEY_ID,
    aws_secret_access_key=AWS_LAMBDA_SECRET_ACCESS_KEY,
    region_name=AWS_LAMBDA_REGION_NAME
)


def my_makedirs(path):
    try:
        os.makedirs(path)
    except Exception as e:
        logger.error(e)


def define_template_filename(filename):
    dir_path = os.path.dirname(os.path.realpath(__file__))
    executions_folder = os.path.join(dir_path, '..', 'certificates_files', 'templates')
    my_makedirs(executions_folder)
    return os.path.join(executions_folder, filename)


def download_template_key(key_name, filename):
    try:
        filename = define_template_filename(filename)
        s3.Bucket(AWS_BUCKET_NAME_REPORT).download_file(key_name, filename)
    except botocore.exceptions.ClientError as e:
        logger.error(e.response['Error'])


def define_report_filename(filename):
    dir_path = os.path.dirname(os.path.realpath(__file__))
    executions_folder = os.path.join(dir_path, '..', 'reports')
    my_makedirs(executions_folder)
    return os.path.join(executions_folder, filename)


def download_konquest_certificates():
    download_template_key("certificate_template/mission.jasper", "mission.jasper")


def generate_certificate():
    """
    course_name,  user_name, performance, duration, date_finished, company_icon, template_color
    """
    # color = "#B171FB"
    color = "#2252D8"
    data = {
        "course_name": "MEU CURSO LEGAL",
        "user_name": "LEONARDO VITOR DA SILVA",
        "performance": "57%",
        "duration": "10h32m",
        "date_finished": datetime.datetime.now().strftime("%d/%m/%Y"),
        "company_icon": "https://s3.amazonaws.com/keeps.reports/assets/icones3.png",
        "template_color": color,
    }

    file_url = json_to_pdf("certificate_template/mission.jasper", data, "My_Smartzap_Certificate2")
    logger.info(file_url)


def json_to_pdf(jasper_template, dataset, report_name):
    """
    :param jasper_template: Page name report
    :param dataset: Dict with key as data_json for build report (check dataset for each report)
    :param report_name: choose a name, if None will generate using uuid4.

    :return: local file path where base64 was write (saved)
    """

    payload = {
        "jasper_file_url": jasper_template,
        "jasper_sub_file_url": None,
        "dataset": dataset,
        "params": {},
    }

    response = lambda_client.invoke(
        FunctionName="json-pdf-report", InvocationType="RequestResponse", Payload=json.dumps(payload)
    )

    data = response["Payload"].read().decode("utf-8")

    filename = ''
    if data:
        filename = define_report_filename(f'{report_name}.pdf')
        with open(filename, "wb") as opened_file:
            opened_file.write(base64.b64decode(data))

    return filename


def generate_certificate_client():
    cache = ucache.MemoryCache()
    container = di.Container(config.d, cache)
    certificate_client = container.certificate_client()
    certificate_client.generate_end_course_report('525b6824-b431-4c44-8039-36a5c94300a5', 'Um curso ótimo', 'Leonardo Vitor da Silva',
                                                  '89%', '2h5min', '14/11/2022', 'https://s3.amazonaws.com/keeps.myaccount.media.hml/company-logo/525b6824-b431-4c44-8039-36a5c94300a5', 'pt-br')


if __name__ == '__main__':
    # download_konquest_certificates()
    # generate_certificate()
    # generate_certificate_client()
    logger.info('Done')
