import time

import ucache
from domain import config, di
from logger import logger


def build_container():
    cache = ucache.MemoryCache()
    container = di.Container(config.d, cache)
    return container


def build_all_message_data():
    data = {
        'user_name': 'User Name',
        'course_name': 'Course Name',
        'company_name': 'Company Name',
        'course_description': 'Course description. Very good course',
        'content_description': 'Content description of the course',
        'lesson_name': 'Lesson Name',
        'content_name': 'Content Name',
        'content_days_expiration': 5,
        'short_link': 'https://www.keeps.com.br',
        'percentage': '78%',
        'duration': '8min',
        'learn_duration': '12min',
        'points': '54',
        'learn_points': '60'
    }
    return data


def all_template_messages():
    return [
        'course_introduction',
        'course_welcome',
        'enrollment_started',
        'lesson_content',
        'lesson_content_anticipated',
        'end_course_0',
        'end_course_50',
        'end_course_70',
        'end_course_100',
        'end_course_certificate_0',
        'end_course_certificate_50',
        'end_course_certificate_70',
        'end_course_certificate_100',
    ]


def dispatch_templates(lang, phone_number):
    container = build_container()
    message_dispatcher = container.dispatcher()
    all_message_data = build_all_message_data()
    callback_url = 'https://webhook.site/01a5a846-a589-4ac1-9a99-0cc207d9c880'
    for template in all_template_messages():
        message = message_dispatcher.build_message(template, lang, all_message_data)
        message_dispatcher.send_message(phone_number, message, callback_url, lang)
        time.sleep(1)


if __name__ == '__main__':
    dispatch_templates('pt-br', '5548<phone>')
    dispatch_templates('es', '5548<phone>')
    logger.info('Done')
