from smartzap.domain import phone_helper


def test_only_normalize_cellphone_numbers():
    numbers = [
        ('554891001010', '5548991001010'),
        ('5548991001010', '5548991001010'),
        ('554832000000', '554832000000'),
        ('554832515900', '554832515900'),
        ('991001010', '991001010'),
        ('91001010', '91001010'),
        ('1191001010', '1191001010'),
        ('11991001010', '11991001010'),
        ('554881001010', '5548981001010'),
        ('5504881001010', '5548981001010')  # Should remove the 0 after +55
    ]
    for phone, expected in numbers:
        phone_normalized = phone_helper.normalize_brazilian_9_digits(phone)
        assert expected == phone_normalized
