import unittest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from datetime import datetime, timedelta

from domain.model import Base, Schedule, Enrollment, Workspace, EnrollmentStatus, ScheduleStatus, User, Course, \
    CourseCategory, ScheduleType
from domain.idle_enrollments.idle_enrollment_repository import IdleEnrollmentRepository


class TestIdleEnrollmentRepositoryIntegration(unittest.TestCase):
    def setUp(self):
        self.engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(self.engine)

        self.Session = sessionmaker(bind=self.engine)
        self.session = self.Session()

        self.repository = IdleEnrollmentRepository(self.session)

        self._populate_test_data()

    def _populate_test_data(self):
        # Create users
        user1 = User(id="1", name="Test User 1", phone="123456789", email="<EMAIL>")
        user2 = User(id="2", name="Test User 2", phone="987654321", email="<EMAIL>")
        self.session.add_all([user1, user2])

        # Create workspaces
        workspace1 = Workspace(id="1", name="Workspace 1", enrollment_idle_days_limit=10)
        workspace2 = Workspace(id="2", name="Workspace 2", enrollment_idle_days_limit=20)
        self.session.add_all([workspace1, workspace2])

        category = CourseCategory(id="500", name="Test Category", description="A category for testing")
        self.session.add(category)

        # Create courses with a valid category_id
        course1 = Course(id="101", name="Course 1", category_id="500", workspace_owner_id="1", user_creator_id="1")
        course2 = Course(id="102", name="Course 2", category_id="500", workspace_owner_id="2", user_creator_id="2")
        self.session.add_all([course1, course2])

        # Assign courses to enrollments
        enrollment1 = Enrollment(id="1", user_id="1", course_id="101", workspace_id="1",
                                 status=EnrollmentStatus.started)
        enrollment2 = Enrollment(id="2", user_id="2", course_id="102", workspace_id="2",
                                 status=EnrollmentStatus.started)
        self.session.add_all([enrollment1, enrollment2])

        # Create schedules
        schedule1 = Schedule(
            enrollment_id="1",
            workspace_id="1",
            send_date=datetime.utcnow() - timedelta(days=5),
            status=ScheduleStatus.sent,
            type=ScheduleType.lesson_content,
            lang='pt-br',
            timezone='utc'
        )

        schedule2 = Schedule(
            enrollment_id="2",
            workspace_id="2",
            send_date=datetime.utcnow() - timedelta(days=25),
            status=ScheduleStatus.sent,
            type=ScheduleType.lesson_content,
            lang='pt-br',
            timezone='utc'
        )
        self.session.add_all([schedule1, schedule2])

        self.session.commit()

    def test_get_workspace_limits(self):
        workspaces = self.repository._get_workspace_limits()
        self.assertEqual(len(workspaces), 2)
        self.assertEqual(workspaces[0].enrollment_idle_days_limit, 10)
        self.assertEqual(workspaces[1].enrollment_idle_days_limit, 20)

    def test_get_enrollments_without_recent_activity(self):
        inactive_enrollments = self.repository.get_enrollments_without_recent_activity()
        self.assertEqual(len(inactive_enrollments), 1)
        self.assertEqual(inactive_enrollments[0].id, "2")

    def tearDown(self):
        self.session.close()
        Base.metadata.drop_all(self.engine)
