import unittest
import uuid
from unittest.mock import Mock
from domain.services.message_chatter_bot_v2 import Message<PERSON>hatterBotV2
from domain.exceptions.nps_exceptions import FeedbackAlreadyRegisteredException, CourseNotAcceptingNPSSurveyException


class TestMessageChatterBotV2(unittest.TestCase):
    def setUp(self):
        self.enrollment_service = Mock()
        self.schedule_service = Mock()
        self.tasks_client = Mock()
        self.content_message_data_service = Mock()
        self.nps_service = Mock()
        self.courses_recommendations_service = Mock()
        self.workspace_service = Mock()
        self.user_initiated_enrollment_service = Mock()
        self.user_phone = "1234567890"

        self.bot = MessageChatterBotV2(
            enrollment_service=self.enrollment_service,
            schedule_service=self.schedule_service,
            tasks_client=self.tasks_client,
            nps_service=self.nps_service,
            user_initiated_enrollment_service=self.user_initiated_enrollment_service,
            courses_recommendations_service=self.courses_recommendations_service,
            workspace_service=self.workspace_service
        )

    def test_accept_message(self):
        enrollment = Mock()
        self.enrollment_service.accept_disclaimer.return_value = enrollment

        result = self.bot._accept_message(enrollment)
        self.enrollment_service.accept_disclaimer.assert_called_once_with(enrollment)
        self.assertEqual(result, enrollment)

    def test_finish_enrollment(self):
        enrollment = Mock()
        self.enrollment_service.load_first_active_from_phone.return_value = enrollment
        self.schedule_service.anticipate_course_end.return_value = (None, None)

        result = self.bot.finish_enrollment(self.user_phone)

        self.assertIsNone(result)

    def test_register_nps_score_success(self):
        enrollment = Mock()
        self.enrollment_service.load_last_completed_from_phone.return_value = enrollment

        result = self.bot.register_nps_score(self.user_phone, 9)

        self.enrollment_service.load_last_completed_from_phone.assert_called_once_with(self.user_phone)
        self.nps_service.register_enrollment_score.assert_called_once_with(enrollment, 9)
        self.assertEqual(result, "success")

    def test_register_nps_score_no_completed_enrollment(self):
        self.enrollment_service.load_last_completed_from_phone.return_value = None

        result = self.bot.register_nps_score(self.user_phone, 9)

        self.enrollment_service.load_last_completed_from_phone.assert_called_once_with(self.user_phone)
        self.assertEqual(result, "no_completed_enrollment")

    def test_register_nps_score_feedback_already_registered(self):
        enrollment = Mock()
        self.enrollment_service.load_last_completed_from_phone.return_value = enrollment
        self.nps_service.register_enrollment_score.side_effect = FeedbackAlreadyRegisteredException()

        result = self.bot.register_nps_score(self.user_phone, 9)

        self.enrollment_service.load_last_completed_from_phone.assert_called_once_with(self.user_phone)
        self.nps_service.register_enrollment_score.assert_called_once_with(enrollment, 9)
        self.assertEqual(result, "feedback_already_registered")

    def test_register_nps_score_course_not_accepting(self):
        enrollment = Mock()
        self.enrollment_service.load_last_completed_from_phone.return_value = enrollment
        self.nps_service.register_enrollment_score.side_effect = CourseNotAcceptingNPSSurveyException()

        result = self.bot.register_nps_score(self.user_phone, 9)

        self.enrollment_service.load_last_completed_from_phone.assert_called_once_with(self.user_phone)
        self.nps_service.register_enrollment_score.assert_called_once_with(enrollment, 9)
        self.assertEqual(result, "course_not_accepting_nps_survey")

    def test_get_courses_recommendations_data_success(self):
        course_1 = Mock()
        course_1.id = "course_1_id"
        course_1.name = "Course 1"
        course_1.description = "Description 1"

        course_2 = Mock()
        course_2.id = "course_2_id"
        course_2.name = "Course 2"
        course_2.description = "Description 2"

        course_3 = Mock()
        course_3.id = "course_3_id"
        course_3.name = "Course 3"
        course_3.description = "Description 3"

        workspace_id = uuid.uuid4()

        courses = [course_1, course_2, course_3]
        self.courses_recommendations_service.get_courses.return_value = courses

        result, status = self.bot.get_courses_recommendations_data(self.user_phone, workspace_id)

        self.courses_recommendations_service.get_courses.assert_called_once_with(self.user_phone, workspace_id, 3)
        self.assertEqual(status, "success")
        self.assertEqual(result, {
            "course_1_id": "course_1_id",
            "course_1_name": "Course 1",
            "course_1_description": "Description 1",
            "course_2_id": "course_2_id",
            "course_2_name": "Course 2",
            "course_2_description": "Description 2",
            "course_3_id": "course_3_id",
            "course_3_name": "Course 3",
            "course_3_description": "Description 3"
        })

    def test_get_courses_recommendations_data_insufficient_courses(self):
        courses = [
            Mock(id="course_1_id", name="Course 1", description="Description 1"),
        ]
        workspace_id = uuid.uuid4()
        self.courses_recommendations_service.get_courses.return_value = courses

        result, status = self.bot.get_courses_recommendations_data(self.user_phone, workspace_id)

        self.courses_recommendations_service.get_courses.assert_called_once_with(self.user_phone, workspace_id, 3)
        self.assertEqual(status, "without_courses_to_recommend")
        self.assertEqual(result, {})

    def test_give_up_success(self):
        enrollment = Mock()
        enrollment.course.name = "Sample Course"
        self.enrollment_service.load_first_active_from_phone.return_value = enrollment

        result, status = self.bot.give_up(self.user_phone)

        self.enrollment_service.load_first_active_from_phone.assert_called_once_with(self.user_phone)
        self.schedule_service.give_up_enrollment.assert_called_once_with(enrollment.id)
        self.assertEqual(status, "success")
        self.assertEqual(result, "Sample Course")

    def test_give_up_no_enrollment(self):
        self.enrollment_service.load_first_active_from_phone.return_value = None

        result, status = self.bot.give_up(self.user_phone)

        self.enrollment_service.load_first_active_from_phone.assert_called_once_with(self.user_phone)
        self.schedule_service.give_up_enrollment.assert_not_called()
        self.assertEqual(status, "no_enrollment")
        self.assertIsNone(result)
