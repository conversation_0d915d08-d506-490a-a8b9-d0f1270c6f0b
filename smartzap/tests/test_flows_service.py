import json
import unittest
from unittest.mock import patch, mock_open

from flows.flow import Flow
from flows.services.flows_service import FlowsService
from flows.exceptions import MissingFlowsException, FlowNotFoundException


class TestFlowsService(unittest.TestCase):
    @patch('flows.services.flows_service.Client')
    @patch('flows.services.flows_service.FlowsService.load_flows')
    def setUp(self, mock_load_flows, mock_client):
        self.mock_client = mock_client.return_value
        self.mock_flows = [
            Flow(account_sid='AC123', flow_type='enrollment_intro', sid='FW123'),
            Flow(account_sid='AC123', flow_type='enrollment_content', sid='FW124'),
            Flow(account_sid='AC123', flow_type='enrollment_finish', sid='FW125')
        ]
        mock_load_flows.return_value = self.mock_flows

        # Initialize FlowsService with a mock config file
        self.service = FlowsService(config_file_path='mock_flows.json')

    def test_load_flows_should_raise_missing_flows_exception(self):
        # Test for MissingFlowsException if required flow types are missing in the JSON
        mock_data = {
            "AC123": {
                "flows": [
                    {"type": "enrollment_intro", "sid": "FW123"},
                    {"type": "enrollment_content", "sid": "FW124"}
                ]
            }
        }
        with patch('builtins.open', mock_open(read_data=json.dumps(mock_data))):
            with self.assertRaises(MissingFlowsException) as context:
                self.service.load_flows('mock_flows.json')
            self.assertEqual(str(context.exception), "Account 'AC123' is missing the following flow types: enrollment_finish")

    def test_get_flow_sid_should_return_correct_sid(self):
        flow_sid = self.service.get_flow_sid(account_sid='AC123', flow_type='enrollment_content')
        self.assertEqual(flow_sid, 'FW124')

    def test_get_flow_sid_should_raise_flow_not_found_exception(self):
        with self.assertRaises(FlowNotFoundException) as context:
            self.service.get_flow_sid(account_sid='AC123', flow_type='non_existent_flow')
        self.assertEqual(str(context.exception), "Flow of type 'non_existent_flow' not found for account 'AC123'.")

