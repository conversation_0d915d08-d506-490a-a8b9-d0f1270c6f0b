import unittest
from unittest.mock import MagicMock

from domain.idle_enrollments.idle_enrollment_service import IdleEnrollmentService
from domain.idle_enrollments.interfaces.idle_enrollments_repository import IIdleEnrollmentsRepository
from domain.service import EnrollmentService


class TestIdleEnrollmentService(unittest.TestCase):
    def setUp(self):
        self.mock_repository = MagicMock(spec=IIdleEnrollmentsRepository)
        self.mock_enrollment_service = MagicMock(spec=EnrollmentService)
        self.service = IdleEnrollmentService(self.mock_repository, self.mock_enrollment_service)

    def test_cancel_all_calls_repository_methods(self):
        enrollments = [MagicMock(id=1)]
        self.mock_repository.get_enrollments_without_recent_activity.return_value = enrollments

        self.service.cancel_all()

        self.mock_repository.get_enrollments_without_recent_activity.assert_called_once()
        self.mock_enrollment_service.cancel_in_batch.assert_called_once_with(enrollments)
