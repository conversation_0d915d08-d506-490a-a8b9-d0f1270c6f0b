import unittest
from unittest.mock import MagicMock
from domain.client import MyA<PERSON>unt<PERSON>lient
from domain.services.user_service import UserServiceV2


class TestUserServiceV2(unittest.TestCase):

    def setUp(self):
        self.mock_db = MagicMock()
        self.mock_my_account_client = MagicMock(spec=MyAccountClient)
        self.client_id = "mock-client-id"
        self.user_service = UserServiceV2(self.mock_db, self.client_id, self.mock_my_account_client)

    def test_add_user_success(self):
        user_data = {
            "name": "Test User",
            "email": "<EMAIL>",
            "director": "Director Name",
            "manager": "Manager Name",
            "area_of_activity": "Development"
        }

        self.mock_my_account_client.create_user.return_value = {
            "id": "mock-user-id",
            "email": "<EMAIL>",
            "name": "Test User"
        }

        result = self.user_service.add(user_data)

        self.assertEqual(result["id"], "mock-user-id")
        self.assertEqual(result["email"], "<EMAIL>")
        self.assertEqual(result["name"], "Test User")
        self.mock_my_account_client.create_user.assert_called_once_with(
            {
                "name": "Test User",
                "email": "<EMAIL>",
                "director": "Director Name",
                "manager": "Manager Name",
                "area_of_activity": "Development",
                "profile": {
                    "director": "Director Name",
                    "manager": "Manager Name",
                    "area_of_activity": "Development"
                }
            },
            self.client_id
        )

    def test_add_user_without_optional_fields(self):
        user_data = {
            "name": "Test User",
            "email": "<EMAIL>"
        }

        self.mock_my_account_client.create_user.return_value = {
            "id": "mock-user-id",
            "email": "<EMAIL>",
            "name": "Test User"
        }

        result = self.user_service.add(user_data)

        self.assertEqual(result["id"], "mock-user-id")
        self.assertEqual(result["email"], "<EMAIL>")
        self.assertEqual(result["name"], "Test User")
        self.mock_my_account_client.create_user.assert_called_once_with(
            {
                "name": "Test User",
                "email": "<EMAIL>",
                "profile": {
                    "director": None,
                    "manager": None,
                    "area_of_activity": None
                }
            },
            self.client_id
        )

    def test_add_user_client_failure(self):
        user_data = {
            "name": "Test User",
            "email": "<EMAIL>"
        }

        self.mock_my_account_client.create_user.side_effect = Exception("Client error")

        with self.assertRaises(Exception) as context:
            self.user_service.add(user_data)

        self.assertEqual(str(context.exception), "Client error")
        self.mock_my_account_client.create_user.assert_called_once_with(
            {
                "name": "Test User",
                "email": "<EMAIL>",
                "profile": {
                    "director": None,
                    "manager": None,
                    "area_of_activity": None
                }
            },
            self.client_id
        )
