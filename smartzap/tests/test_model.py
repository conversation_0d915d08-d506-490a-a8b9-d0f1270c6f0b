import datetime
import uuid
import freezegun

import pytest

from smartzap.domain import model
from . import utils

def build_chat(chat_type):
    chat = model.Chat()
    chat.type = chat_type
    return chat


def test_enrollment_status_changes():
    enrollment = model.Enrollment()

    enrollment.status = model.EnrollmentStatus.waiting
    assert enrollment.can_change_status(model.EnrollmentStatus.started)
    assert enrollment.can_change_status(model.EnrollmentStatus.refused)
    assert enrollment.can_change_status(model.EnrollmentStatus.waiting)
    assert not enrollment.can_change_status(model.EnrollmentStatus.completed)

    enrollment.status = model.EnrollmentStatus.started
    assert enrollment.can_change_status(model.EnrollmentStatus.completed)
    assert enrollment.can_change_status(model.EnrollmentStatus.started)
    assert not enrollment.can_change_status(model.EnrollmentStatus.refused)
    assert not enrollment.can_change_status(model.EnrollmentStatus.waiting)

    enrollment.status = model.EnrollmentStatus.refused
    assert enrollment.can_change_status(model.EnrollmentStatus.refused)
    assert not enrollment.can_change_status(model.EnrollmentStatus.started)
    assert not enrollment.can_change_status(model.EnrollmentStatus.waiting)
    assert not enrollment.can_change_status(model.EnrollmentStatus.completed)

    enrollment.status = model.EnrollmentStatus.completed
    assert enrollment.can_change_status(model.EnrollmentStatus.completed)
    assert not enrollment.can_change_status(model.EnrollmentStatus.started)
    assert not enrollment.can_change_status(model.EnrollmentStatus.refused)
    assert not enrollment.can_change_status(model.EnrollmentStatus.waiting)


def test_chat_do_not_reply_if_already_replied():
    for chat_type in model.ChatType.all:
        chat = model.Chat()
        chat.type = chat_type
        assert not chat.can_reply_message(chat_type)


def test_workspace_private_channel_validation(db):
    for url in ['abc@+55489912398', ':abc@+55489912398', '@+55489912398']:
        with pytest.raises(AssertionError, match='Invalid Private Channel'):
            workspace = model.Workspace()
            workspace.id = str(uuid.uuid4())
            workspace.name = 'Companhia'
            workspace.user_token_expiration = 3
            workspace.end_course_schedule = 2
            workspace.monthly_plan = 0
            workspace.billing_cycle_day = 1
            workspace.private_channel = url
            db.add(workspace)
            db.flush()


def test_workspace_empty_private_channel_validation(db):
    workspace = utils.create_workspace(db)
    assert not workspace.private_channel


def test_course_content_and_quiz_weights(db):
    course = model.Course()
    course.name = 'My Course'
    weights = [(-2, 5), (2, 15), (92, -5), (0, 11), (1, 100)]
    for c_weight, q_weight in weights:
        with pytest.raises(AssertionError, match='Performance Weight. Must be between 0-10'):
            course.content_performance_weight = c_weight
            course.quiz_performance_weight = q_weight
            db.add(course)
            db.commit()


def test_course_is_immutable():
    course = model.Course()
    immutable_statuses = [model.CourseStatus.processing, model.CourseStatus.processing]
    for status in immutable_statuses:
        course.status = status
        assert course.is_immutable()

    mutable_statuses = [model.CourseStatus.creating, model.CourseStatus.reviewing, model.CourseStatus.finished]
    for status in mutable_statuses:
        course.status = status
        assert not course.is_immutable()


def test_user_phone_nine_digits_format():
    user = model.User()
    user.phone = '01 48 91698418'
    assert user.phone == '014891698418'

    user.phone = '+55(48) 99169-8418'
    assert user.phone == '5548991698418'

    user.phone = '+55 (48) 9169-8418'
    assert user.phone == '5548991698418'


def test_languages_channels():
    content = '''{
       "pt-br": "@pt-br",
       "ES-CO": "@es-co"
    }
    '''
    languages_channels = model.LanguageChannels(content)
    assert languages_channels.load('pt-br') == '@pt-br'
    assert languages_channels.load('pt-BR') == '@pt-br'
    assert languages_channels.load('es-co') == '@es-co'
    assert languages_channels.load('en-us') == '@pt-br'
    assert languages_channels.load('') == '@pt-br'

    content = '''{
       "pt-br": "@pt-br",
       "es-co": "@es-co",
       "default": "es-co"
    }
    '''
    languages_channels = model.LanguageChannels(content)
    assert languages_channels.load('en-us') == '@es-co'
    assert languages_channels.load('') == '@es-co'


def test_languages_channels_without_country():
    content = '''{
       "PT": "@pt",
       "es": "@es"
    }
    '''
    languages_channels = model.LanguageChannels(content)
    assert languages_channels.load('pt-br') == '@pt'
    assert languages_channels.load('es-co') == '@es'
    assert languages_channels.load('en-US') == '@pt'
    assert languages_channels.load('') == '@pt'
    assert languages_channels.load(None) == '@pt'


def test_languages_channels_empty_content():
    content = ''
    languages_channels = model.LanguageChannels(content)
    assert languages_channels.load('pt-br') == ''
    assert languages_channels.load('') == ''
    assert languages_channels.load(None) == ''

    languages_channels = model.LanguageChannels(None)
    assert languages_channels.load('pt-br') == ''


def test_chat_in_24_window():
    create_values = [
        ('2020-02-27 09:09:00', True),
        ('2020-02-28 09:08:00', True),
        ('2020-02-27 08:58:00', True),
        ('2020-02-26 09:18:00', True),
        ('2020-02-26 09:17:00', False),
        ('2020-02-26 08:08:00', False),
        ('2020-02-24 09:08:00', False),
    ]
    chat = model.Chat()
    with freezegun.freeze_time('2020-02-27 09:08:00'):
        for c, expectation in create_values:
            chat.created = datetime.datetime.fromisoformat(c)
            assert chat.is_in_24_window() == expectation


def test_delete_enrollment_set_schedule_null(db, schedule_service):
    enrollment = model.Enrollment()
    enrollment.id = str(uuid.uuid4())
    enrollment.workspace_id = str(uuid.uuid4())
    enrollment.user_id = str(uuid.uuid4())
    enrollment.course_id = str(uuid.uuid4())
    enrollment.status = model.EnrollmentStatus.started
    enrollment.created = datetime.datetime.now()
    db.add(enrollment)
    
    schedule = model.Schedule()
    schedule.enrollment_id = enrollment.id
    schedule.reference_id = str(uuid.uuid4())
    schedule.type = model.ScheduleType.lesson_content
    schedule.send_date = datetime.datetime.now()
    schedule.lang = 'pt-br'
    schedule.timezone = 'UTC'
    schedule.workspace_id = enrollment.workspace_id
    schedule.status = model.ScheduleStatus.delivered
    schedule_service.add(schedule)

    db.delete(enrollment)
    db.commit()

    schedule = db.query(model.Schedule).filter(model.Schedule.id == schedule.id).first()
    assert schedule.enrollment_id is None