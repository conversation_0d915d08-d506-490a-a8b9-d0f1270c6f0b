import datetime
import os
import re
import uuid
from unittest import mock
from unittest.mock import Magic<PERSON>ock
import urllib

import freezegun
import pytest
import deepdiff

import domain.exceptions.service_exceptions
from domain import model, service, client
from domain.service import PrivateChannelService, MessageChatterBot, COURSE_WELCOME, \
    COURSE_WELCOME_WITHOUT_CONTENT_ANTICIPATION
from domain.services.certificate_service import CertificateService
from . import utils

UPDATE_ENROLLMENT_PROGRESS_PATH = 'tasks.task.update_enrollment_progress.delay'
SEND_TASK = 'tasks.celery_app.app.send_task'


def test_delete_schedules_from_enrollment_id(mocker, db, workspace, schedule_service):
    mocker.patch(SEND_TASK)
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'es', workspace.id, user.id)
    enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Bogota')
    schedule_service.create_schedules(enrollment)
    my_schedules = schedule_service.load_all()
    assert len(my_schedules) == 11

    schedule_service.delete_schedules_from_enrollment_id(enrollment.id)
    my_schedules = schedule_service.load_all()
    assert len(my_schedules) == 0


def test_load_messages_sent_count(mocker, db, workspace, user, schedule_service):
    mocker.patch(SEND_TASK)
    course = utils.create_course_finished(db, 'pt-BR', workspace.id, user.id)
    enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Bogota')
    schedule_service.create_schedules(enrollment)
    expected_sent_messages = 5

    schedules = db.query(model.Schedule).limit(expected_sent_messages).all()
    for schedule in schedules:
        schedule.status = model.ScheduleStatus.delivered
    db.commit()

    messages_sent_count = schedule_service.load_messages_sent_count({})

    assert messages_sent_count == expected_sent_messages


def test_load_messages_pending_count(mocker, db, workspace, schedule_service, enrollment_service):
    mocker.patch(SEND_TASK)
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'es', workspace.id, user.id)
    enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Bogota')
    schedule_service.create_schedules(enrollment)

    messages_sent_count = enrollment_service.load_messages_pending_count({})

    assert messages_sent_count == 11


def test_cancel_enrollment(mocker, db, workspace, schedule_service, enrollment_service):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'es', workspace.id, user.id)
    enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Bogota')

    updated_enrollment = enrollment_service.cancel(enrollment)

    assert updated_enrollment.status == model.EnrollmentStatus.canceled


def test_import_xlsx(db):
    current_di_path = os.path.dirname(os.path.abspath(__file__))
    filename = os.path.join(current_di_path, 'assets/users.xlsx')

    user_service = service.UserService(db, '123456')
    xls_importer = service.UserXlsImporter(user_service)
    user_ok, user_not_ok = xls_importer.import_file(filename)
    assert len(user_ok) == 3
    assert len(user_not_ok) == 3

    for u in user_ok:
        assert u.phone in ['5548996887365', '59177391027', '48996887311']

    for u in user_not_ok:
        assert u['phone'] in ['5548996888376', '5548996881234', '55489912347365']

    user_ok, user_not_ok = xls_importer.import_file(filename)
    assert len(user_ok) == 3
    assert len(user_not_ok) == 3


def test_delete_course_must_delete_its_lessons_and_contents(db, workspace):
    user = utils.create_user(db, '5548996887365', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    course_service = service.CourseService(db, workspace.id, user.id)
    lessons = db.query(model.Lesson).all()
    assert len(lessons) == 3
    contents = db.query(model.Content).all()
    assert len(contents) == 9

    course_service.delete(course.id)
    lessons = db.query(model.Lesson).all()
    assert len(lessons) == 0
    contents = db.query(model.Content).all()
    assert len(contents) == 0


def test_delete_course_not_delete_schedules_delivered(mocker, db, workspace, schedule_service):
    mocker.patch(SEND_TASK)
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    course_service = service.CourseService(db, workspace.id, user.id)
    enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')
    schedule_service.create_schedules(enrollment)
    my_schedules = schedule_service.load_all()
    for schedule in my_schedules:
        schedule.status = model.ScheduleStatus.delivered
        schedule.enrollment_id = enrollment.id
    db.commit()
    assert len(my_schedules) == 11

    enrollment_service = service.EnrollmentService(db, schedule_service)
    my_enrollments = enrollment_service.load_all()
    assert len(my_enrollments) == 1

    course_service.delete(course.id)

    my_enrollments = enrollment_service.load_all()
    assert len(my_enrollments) == 0
    my_schedules = schedule_service.load_all()
    assert len(my_schedules) == 11


def test_delete_course_must_delete_schedules_pending(mocker, db, workspace, schedule_service):
    mocker.patch(SEND_TASK)
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    course_service = service.CourseService(db, workspace.id, user.id)
    enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')
    schedule_service.create_schedules(enrollment)
    my_schedules = schedule_service.load_all()
    for schedule in my_schedules:
        schedule.status = model.ScheduleStatus.pending
        schedule.enrollment_id = enrollment.id
    db.commit()
    assert len(my_schedules) == 11

    enrollment_service = service.EnrollmentService(db, schedule_service)
    my_enrollments = enrollment_service.load_all()
    assert len(my_enrollments) == 1

    course_service.delete(course.id)

    my_enrollments = enrollment_service.load_all()
    assert len(my_enrollments) == 0
    my_schedules = schedule_service.load_all()
    assert len(my_schedules) == 0


def test_schedule_delete_course_must_notify(db, workspace, course_service):
    notifies = []

    @service.course_delete_signal.on('delete')
    def handler(course, client_id, user_id):
        notifies.append(1 if course.status == model.CourseStatus.deleting else 0)

    user = utils.create_user(db, '5548991234567', workspace.id)
    new_course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    course_service.schedule_delete(new_course.id)
    assert sum(notifies) == 1


def test_new_enrollment_must_schedule_course_intro_message(mocker, db, workspace, schedule_service):
    mocker.patch(SEND_TASK)
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    _ = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')
    my_schedules = schedule_service.load_all()
    assert len(my_schedules) == 1
    assert my_schedules[0].type == model.ScheduleType.course_introduction


def test_update_enrollment_start_date_only_if_status_is_waiting(mocker, db, workspace):
    mocker.patch(SEND_TASK)
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')

    enrollment2 = model.Enrollment()
    enrollment2.start_date = datetime.datetime.utcnow()

    schedule_service = MagicMock()
    enrollment_service = service.EnrollmentService(db, schedule_service)
    enrollment_service.update(enrollment.id, enrollment2)

    enrollment2.status = model.EnrollmentStatus.started
    _ = db.merge(enrollment2)
    with pytest.raises(domain.exceptions.service_exceptions.IntegrityException,
                       match='The Enrollment start_date only can be changed when status is WAITING'):
        enrollment2.start_date = datetime.datetime.utcnow()
        enrollment_service.update(enrollment.id, enrollment2)

    assert schedule_service.create_schedules.call_count == 0


def test_update_enrollment_timezone_only_if_status_is_waiting(mocker, db, workspace):
    mocker.patch(SEND_TASK)
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')

    enrollment2 = model.Enrollment()
    enrollment2.timezone = 'America/Bogota'

    schedule_service = MagicMock()
    enrollment_service = service.EnrollmentService(db, schedule_service)
    enrollment_service.update(enrollment.id, enrollment2)

    enrollment2.status = model.EnrollmentStatus.started
    _ = db.merge(enrollment2)
    with pytest.raises(domain.exceptions.service_exceptions.IntegrityException,
                       match='The Enrollment timezone only can be changed when status is WAITING'):
        enrollment2.timezone = 'America/Sao_Paulo'
        enrollment_service.update(enrollment.id, enrollment2)

    assert schedule_service.create_schedules.call_count == 0


def test_update_enrollment_status_must_follow_model_rules(mocker, db, workspace):
    mocker.patch(SEND_TASK)
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')
    enrollment.status = model.EnrollmentStatus.started
    db.flush()

    schedule_service = MagicMock()
    enrollment_service = service.EnrollmentService(db, schedule_service)
    with pytest.raises(domain.exceptions.service_exceptions.IntegrityException,
                       match='The Enrollment status can not be changed from STARTED to REFUSED'):
        enrollment2 = model.Enrollment()
        enrollment2.status = model.EnrollmentStatus.refused
        enrollment_service.update(enrollment.id, enrollment2)

    assert schedule_service.create_schedules.call_count == 0


def test_update_enrollment_status_to_started_must_schedule_messages(db, workspace):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')

    enrollment2 = model.Enrollment()
    enrollment2.status = model.EnrollmentStatus.started

    schedule_service = MagicMock()
    enrollment_service = service.EnrollmentService(db, schedule_service)
    enrollment_service.update(enrollment.id, enrollment2)

    assert schedule_service.create_schedules.called


def test_update_enrollment_status_to_canceled(db, workspace):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')

    enrollment_2 = model.Enrollment()
    enrollment_2.status = model.EnrollmentStatus.canceled

    schedule_service = MagicMock()
    enrollment_service = service.EnrollmentService(db, schedule_service)
    enrollment_service.update(enrollment.id, enrollment_2)
    db.refresh(enrollment)

    assert enrollment.status == model.EnrollmentStatus.canceled
    assert schedule_service.delete_schedules_from_enrollment_id.called


def test_update_enrollment_cancel_in_batch(db, workspace):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    utils.create_enrollments(db, 2, course.id, workspace.id, 'America/Sao_Paulo')
    schedule_service = MagicMock()
    enrollment_service = service.EnrollmentService(db, schedule_service)
    enrollments = db.query(model.Enrollment).filter()

    enrollment_service.cancel_in_batch(enrollments)

    for enrollment in enrollments:
        assert enrollment.status == model.EnrollmentStatus.canceled
        assert schedule_service.delete_schedules_from_enrollment_id.called


def test_update_enrollment_status_only_schedule_messages_if_changed(db, workspace):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')
    enrollment.status = model.EnrollmentStatus.started
    db.flush()

    enrollment2 = model.Enrollment()
    enrollment2.status = model.EnrollmentStatus.started

    schedule_service = MagicMock()
    enrollment_service = service.EnrollmentService(db, schedule_service)
    enrollment_service.update(enrollment.id, enrollment2)

    assert not schedule_service.create_schedules.called


def test_enrollment_create_add_today_start_date_if_empty(db, workspace):
    schedule_service = MagicMock()
    enrollment_service = service.EnrollmentService(db, schedule_service)
    with freezegun.freeze_time('2020-05-01 09:00:00', tz_offset=-3):
        user = utils.create_user(db, '5548991234567', workspace.id)
        course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
        enrollment = model.Enrollment()
        enrollment.workspace_id = workspace.id
        enrollment.user_id = user.id
        enrollment.course_id = course.id
        enrollment.timezone = 'America/Sao_Paulo'
        enrollment_service.add(enrollment)

    # two days later
    with freezegun.freeze_time('2020-05-03 09:00:00', tz_offset=-3):
        enrollment = enrollment_service.load(enrollment.id)
        assert enrollment.start_date == datetime.date(2020, 5, 1)


def test_delete_enrollment_not_must_delete_schedules(db, workspace, schedule_service):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')

    my_schedules = schedule_service.load_all()
    for schedule in my_schedules:
        schedule.status = model.ScheduleStatus.pending
    db.commit()
    assert len(my_schedules) == 1

    enrollment_service = service.EnrollmentService(db, schedule_service)
    enrollment_service.delete(enrollment.id)

    my_schedules = schedule_service.load_all()
    assert len(my_schedules) == 1

def test_delete_enrollment_not_delete_schedules_delivered(db, workspace, schedule_service):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')

    my_schedules = schedule_service.load_all()
    for schedule in my_schedules:
        schedule.status = model.ScheduleStatus.delivered
    db.commit()
    assert len(my_schedules) == 1

    enrollment_service = service.EnrollmentService(db, schedule_service)
    enrollment_service.delete(enrollment.id)

    my_schedules = schedule_service.load_all()
    assert len(my_schedules) == 1


def test_load_enrollment_stats(db, workspace):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    expected_count_waiting = 2
    expected_count_started = 1
    utils.create_enrollments(db, expected_count_waiting, course.id, workspace.id, 'America/Sao_Paulo')
    utils.create_enrollments(
        db, expected_count_started, course.id, workspace.id, 'America/Sao_Paulo', model.EnrollmentStatus.started
    )

    schedule_service = MagicMock()
    enrollment_service = service.EnrollmentService(db, schedule_service, workspace.id)
    stats = enrollment_service.load_enrollment_stats({})

    assert stats['started_count'] == expected_count_started
    assert stats['waiting_count'] == expected_count_waiting


def test_report_percentage_points(db, workspace, report_service):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    question_content_type = utils.create_content_type(db, 'Question')
    utils.create_content(db, course.lessons[0].id, question_content_type.id, 7)
    enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')

    utils.create_activities(db, service.ActivityService(db), enrollment.id)

    course.quiz_performance_weight = 0
    course.content_performance_weight = 10
    report = report_service.build_performance(course, enrollment)
    user_total_points = 221.71431492605814
    user_total_seconds = 552
    user_percentage = 0.574  # not rounded: 0.5735721508887806
    assert report['percentage'] == user_percentage
    assert abs(report['points'] - user_total_points) < 0.000001
    assert report['duration'] == user_total_seconds


def test_report_weight_performance(db, workspace, report_service):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')

    contents_1 = [
        {'name': 'C1', 'duration': 3.6, 'points': 36, 'learn_duration': 9, 'learn_points': 90, 'type': 'CONTENT'},
        {'name': 'C2', 'duration': 1, 'points': 3, 'learn_duration': 5, 'learn_points': 15, 'type': 'CONTENT'},
        {'name': 'QA', 'duration': 10, 'points': 50, 'learn_duration': 10, 'learn_points': 50, 'type': 'QUIZ'}
    ]
    contents_2 = [
        {'name': 'C3', 'duration': 5, 'points': 50, 'learn_duration': 5, 'learn_points': 50, 'type': 'CONTENT'},
        {'name': 'C4', 'duration': 0.6, 'points': 6, 'learn_duration': 2, 'learn_points': 20, 'type': 'CONTENT'},
        {'name': 'QB', 'duration': 10, 'points': 50, 'learn_duration': 10, 'learn_points': 50, 'type': 'QUIZ'}
    ]
    contents_3 = [
        {'name': 'C5', 'duration': 16, 'points': 160, 'learn_duration': 16, 'learn_points': 160, 'type': 'CONTENT'},
        {'name': 'QC', 'duration': 20, 'points': 100, 'learn_duration': 20, 'learn_points': 100, 'type': 'QUIZ'}
    ]
    user_score = {
        '01': {'name': 'lesson 1', 'contents': contents_1},
        '02': {'name': 'lesson 2', 'contents': contents_2},
        '03': {'name': 'lesson 3', 'contents': contents_3},
    }
    # total content_points: 36+3+50+6+160 = 255
    # total learn_content_points: 90+15+50+20+160 = 335
    # total quiz_points: 50+50+100 = 200
    # total learn_quiz_points: 50+50+100 = 200
    enrollment_activities_mock = 'domain.service.CourseReportService._load_enrollment_activities'
    with mock.patch(enrollment_activities_mock) as mock_enrollment_activities:
        mock_enrollment_activities.return_value = user_score

        expectation = [(4, 6, 222.0, 254.0, 0.904), (6, 4, 233.0, 281.0, 0.857), (0, 10, 200.0, 200.0, 1.0),
                       (10, 0, 255.0, 335.0, 0.761), (9, 1, 249.5, 321.5, 0.785), (1, 9, 205.5, 213.5, 0.976),
                       (5, 5, 227.5, 267.5, 0.881), (2, 8, 211.0, 227.0, 0.952)]
        for c_weight, q_weight, c_points, c_learn_points, performance in expectation:
            course.content_performance_weight = c_weight
            course.quiz_performance_weight = q_weight
            report = report_service.build_performance(course, enrollment)
            assert report['points'] == c_points
            assert report['learn_points'] == c_learn_points
            assert report['percentage'] == performance


def test_ignore_contents_created_after_enrollment(db, workspace, report_service, ):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')
    lesson_id = course.lessons[0].id
    content = model.Content(lesson_id=lesson_id, name="NEW CONTENT", order=4, type=utils.create_content_type(db))
    db.add(content)

    activities = report_service._load_enrollment_activities(course, enrollment)

    content_names = [_content["name"] for _content in activities[lesson_id]["contents"]]
    assert content.name not in content_names


def test_enrollment_tracking(db, workspace, report_service):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')

    utils.create_activities(db, service.ActivityService(db), enrollment.id)

    tracking = report_service.enrollment_tracking_data(enrollment.id)
    expectation = [(72, 171.0, ''), (12, 5.3999999999999995, ''), (100, 182.0, ''), (30, 2.6, ''),
                   (141, 542.5574603174604, ''), (90, 79.39999999999999, ''), (29, 144.0, ''),
                   (13, 38.8, ''), (65, 122.2, '')]
    assert len(tracking) == 9
    for i in range(9):
        assert tracking[i]['duration'] == expectation[i][0]
        assert tracking[i]['learn_duration'] == expectation[i][1]
        assert tracking[i].get('schedule_status', 'Default Value') == expectation[i][2]


def test_load_schedule_by_enrollment_and_content(db, workspace, course_service, schedule_service, schedule_admin, mocker):
    mocker.patch(UPDATE_ENROLLMENT_PROGRESS_PATH)
    enrollment = utils.create_course_and_accept_enrollment(db, workspace, '5548991234567', 'pt-br', 'America/Sao_Paulo',
                                                           datetime.datetime(2020, 2, 29, 9, 8, 7), -3, schedule_admin)
    course = course_service.load(enrollment.course_id)
    all_contents = [content for lesson in course.lessons for content in lesson.contents]
    content = all_contents[3]
    assert schedule_service.load_reference_status(enrollment.id, content.id) == model.ScheduleStatus.pending

    with freezegun.freeze_time('2020-03-08 00:00:01', tz_offset=-3):
        utils.dispatch_schedules(schedule_service, schedule_admin)

    assert schedule_service.load_reference_status(enrollment.id, content.id) == model.ScheduleStatus.sent


def test_course_processor(db, workspace, kontent_client, kontent_map, slack_client):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course_pt_br = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    course_es_co = utils.create_course_finished(db, 'es', workspace.id, user.id)
    course_processor = service.CourseProcessor(db, kontent_client, slack_client)
    assert not course_processor.execute()

    for learn_id, data in kontent_map.items():
        data['analyzed'] = False

    course_es_co.status = model.CourseStatus.processing
    db.flush()
    for learn_id, data in kontent_map.items():
        assert not course_processor.execute()
        data['analyzed'] = True

    assert not course_es_co.points
    assert not course_es_co.duration
    assert not course_pt_br.points
    assert not course_pt_br.duration

    assert len(course_processor.execute()) == 1
    assert len(course_processor.execute()) == 0
    assert course_es_co.points == 386.55
    assert course_es_co.duration == pytest.approx(1287.95746, 0.00001)
    assert not course_pt_br.points
    assert not course_pt_br.duration

    course_pt_br.status = model.CourseStatus.processing
    db.flush()
    assert len(course_processor.execute()) == 1
    assert len(course_processor.execute()) == 0
    assert course_es_co.points == 386.55
    assert course_es_co.duration == pytest.approx(1287.95746, 0.00001)
    assert course_pt_br.points == 386.55
    assert course_pt_br.duration == pytest.approx(1287.95746, 0.00001)


def test_course_processor_must_process_all_waiting(db, workspace, kontent_client, kontent_map, slack_client):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course_pt_br = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    course_es_co = utils.create_course_finished(db, 'es', workspace.id, user.id)
    assert not course_es_co.points
    assert not course_es_co.duration
    assert not course_pt_br.points
    assert not course_pt_br.duration

    course_processor = service.CourseProcessor(db, kontent_client, slack_client)
    assert not course_processor.execute()

    assert not course_es_co.points
    assert not course_es_co.duration
    assert not course_pt_br.points
    assert not course_pt_br.duration

    course_pt_br.status = model.CourseStatus.processing
    course_es_co.status = model.CourseStatus.processing
    db.flush()

    assert len(course_processor.execute()) == 2
    assert course_es_co.points == 386.55
    assert course_es_co.duration == pytest.approx(1287.95746, 0.00001)
    assert course_pt_br.points == 386.55
    assert course_pt_br.duration == pytest.approx(1287.95746, 0.00001)


def test_course_service_throws_when_delete_if_not_the_owner(db, workspace):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    course.workspace_owner_id = str(uuid.uuid4())
    db.commit()

    course_service = service.CourseService(db, workspace.id, user.id)
    with pytest.raises(domain.exceptions.service_exceptions.IntegrityException, match='Unable to delete the course. Only workspace owner can delete.'):
        course_service.delete(course.id)


def test_course_service_throws_updating_and_deleting_if_is_in_immutable_status(db, workspace):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course_service = service.CourseService(db, workspace.id, user.id)
    statuses = [model.CourseStatus.processing, model.CourseStatus.deleting]

    for status in statuses:
        course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
        course.status = status
        db.flush()

        course_update_data = model.Course()
        course_update_data.lang = 'es'
        with pytest.raises(domain.exceptions.service_exceptions.IntegrityException, match=f'Unable to update the course. The course is {status}.'):
            course_service.update(course.id, course_update_data)

        with pytest.raises(domain.exceptions.service_exceptions.IntegrityException, match=f'Unable to delete the course. The course is {status}.'):
            course_service.schedule_delete(course.id)

        course.status = model.CourseStatus.finished
        db.flush()

        course_service.update(course.id, course_update_data)
        assert course.lang == 'es'
        course_service.delete(course.id)
        with pytest.raises(domain.exceptions.service_exceptions.NotFoundException):
            _ = course_service.load(course.id)


def test_enrollment_service_throws_on_add_if_course_not_found(db, workspace):
    user = utils.create_user(db, '5548991234567', workspace.id)
    with pytest.raises(domain.exceptions.service_exceptions.NotFoundException,
                       match='Course not found'):
        _ = utils.create_enrollment(db, user.id, 'pipoca', workspace.id, 'America/Sao_Paulo')


def test_enrollment_service_throws_on_add_if_course_is_not_finished(db, workspace):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    course.status = model.CourseStatus.creating
    db.flush()

    statuses = [model.CourseStatus.creating, model.CourseStatus.processing,
                model.CourseStatus.reviewing, model.CourseStatus.finished]
    for status in statuses:
        with pytest.raises(domain.exceptions.service_exceptions.IntegrityException,
                           match='Unable to enroll in the course. The course is not FINISHED.'):
            _ = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')

        course.status = status
        db.flush()
    _ = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')


def test_enrollment_service_throws_on_if_try_to_change_the_course_enrolled(db, workspace, enrollment_service):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course_pt_br = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    enrollment = utils.create_enrollment(db, user.id, course_pt_br.id, workspace.id, 'America/Sao_Paulo')
    new_enrollment = model.Enrollment()
    new_enrollment.course_id = enrollment.course_id
    new_enrollment.user_id = enrollment.user_id

    enrollment_service.update(enrollment.id, new_enrollment)

    def assert_update():
        with pytest.raises(domain.exceptions.service_exceptions.IntegrityException,
                           match='It is not possible to change the course or user enrolled. '
                                 'You must delete this enrollment and create another'):
            enrollment_service.update(enrollment.id, new_enrollment)

    new_enrollment.user_id = 'id mano veio'
    assert_update()
    new_enrollment.course_id = 'id mano veio'
    assert_update()


def test_delete_enrollment_in_batch(db, workspace, enrollment_service):
    ids = []
    max_index_to_delete = 21
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)

    for i in range(1, 40):
        user = utils.create_user(db, f'55489912345{i:02d}', workspace.id)
        enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')
        if i < max_index_to_delete:
            ids.append(enrollment.id)

    enrollments = enrollment_service.load_all()
    assert len(enrollments) == 39

    enrollment_service.delete_in_batch(ids)
    enrollments = enrollment_service.load_all()
    assert len(enrollments) == 19
    for i, enrollment in enumerate(enrollments):
        index_after_delete = i + max_index_to_delete
        assert enrollment.user.phone == f'55489912345{index_after_delete:02d}'


def test_chatter_bot_accept_enrollment(db, workspace, schedule_service, twilio_chatter_bot, tasks_client):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    _ = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')

    my_schedules = schedule_service.load_all()
    assert len(my_schedules) == 1

    reply_message, image_url = twilio_chatter_bot.process_message('5548991234567', model.ChatAnswerType.accept,
                                                                  'accept')
    assert not reply_message
    assert not image_url
    tasks_client.send_message.assert_called_once()

    # here its already anticipating contents... test the wrong answer
    tasks_client.reset_mock()
    reply_message, image_url = twilio_chatter_bot.process_message('5548991234567', model.ChatAnswerType.accept,
                                                                  'accept')

    twilio_chatter_bot.tasks_client.send_message.assert_called_with(
        '5548991234567',
        'enrollment_started',
        {},
        None,
        'pt-br',
        None
    )
    assert not image_url

    my_schedules = schedule_service.load_all()
    assert len(my_schedules) == 11


def test_chatter_bot_drop_out_enrollment(db, workspace, schedule_service, twilio_chatter_bot, tasks_client):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    _ = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')
    my_schedules = schedule_service.load_all()
    assert len(my_schedules) == 1

    reply_message, image_url = twilio_chatter_bot.process_message('5548991234567', model.ChatAnswerType.drop_out,'drop_out')
    assert reply_message
    assert not image_url
    assert reply_message == (
        '📚 Aviso Importante:\n'
         '\n'
         'Sua matrícula no curso *My Course* foi encerrada.\n'
         '\n'
         '🔄 Para Reiniciar o Curso:\n'
         'Por favor, entre em contato com o gestor responsável para discutir as opções '
         'ou para obter mais informações.\n'
         '\n'
         '💡 Suporte e Assistência:\n'
         'Se precisar de ajuda adicional ou tiver dúvidas, estamos disponíveis no '
         'seguinte link: https://keeps.page.link/suporte-whats'
    )


def test_chatter_bot_reject_enrollment(db, workspace, schedule_service, twilio_chatter_bot):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    _ = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')

    my_schedules = schedule_service.load_all()
    assert len(my_schedules) == 1

    reply_message, _ = twilio_chatter_bot.process_message('5548991234567', model.ChatAnswerType.reject,
                                                                  'reject')
    assert reply_message

    # next equals the reply is empty
    reply_message, _ = twilio_chatter_bot.process_message('5548991234567', model.ChatAnswerType.reject,
                                                                  'reject')
    assert not reply_message

    my_schedules = schedule_service.load_all()
    assert len(my_schedules) == 1


def test_chatter_bot_invalid_answer_enrollment(db, workspace, twilio_chatter_bot):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    _ = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')
    reply_message, _ = twilio_chatter_bot.process_message('5548991234567', None, 'none')
    assert not reply_message

    # next equals the reply is empty
    reply_message, _ = twilio_chatter_bot.process_message('5548991234567', None, 'none')
    assert not reply_message


def test_chatter_bot_next_content(db, workspace, twilio_chatter_bot, schedule_service):
    with freezegun.freeze_time('2020-02-29 09:08:07', tz_offset=-3):
        user = utils.create_user(db, '5548991234567', workspace.id)
        course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
        _ = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')

    with freezegun.freeze_time('2020-02-29 10:08:07', tz_offset=-3):
        schedules = schedule_service.load_pending_schedules(1, 99)
        for s in schedules:
            schedule_service.set_delivered(s.id)

    with freezegun.freeze_time('2020-02-29 10:15:00', tz_offset=-3):
        twilio_chatter_bot.process_message('5548991234567', model.ChatAnswerType.accept, 'accept')
        reply_message, _ = twilio_chatter_bot.process_message('5548991234567', model.ChatAnswerType.content,
                                                                      'content')
        assert reply_message.find('antecipado') != -1

        # next equals the reply alert for no more content
        reply_message, _ = twilio_chatter_bot.process_message('5548991234567', model.ChatAnswerType.content,
                                                                      'content')
        assert reply_message.find('não possui conteúdo') != -1


def test_chatter_bot_next_content_when_course_not_allow_content_anticipation(db, workspace, twilio_chatter_bot, schedule_service):
    with freezegun.freeze_time('2020-02-29 09:08:07', tz_offset=-3):
        user = utils.create_user(db, '5548991234567', workspace.id)
        course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id, False)
        _ = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')

    with freezegun.freeze_time('2020-02-29 10:08:07', tz_offset=-3):
        schedules = schedule_service.load_pending_schedules(1, 99)
        for schedule in schedules:
            schedule_service.set_delivered(schedule.id)

    with freezegun.freeze_time('2020-02-29 10:15:00', tz_offset=-3):
        twilio_chatter_bot.process_message('5548991234567', model.ChatAnswerType.accept, 'accept')
        reply_message, _ = twilio_chatter_bot.process_message(
            '5548991234567', model.ChatAnswerType.content, 'content'
        )
        assert reply_message == 'Você não possui conteúdo para antecipação neste momento.'


def test_chatter_bot_next_content_all_anticipated(db, workspace, twilio_chatter_bot, schedule_service):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    _ = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')
    twilio_chatter_bot.process_message('5548991234567', model.ChatAnswerType.accept, 'accept')

    # set all except course_end as delivered
    schedules = schedule_service.load_all()
    for s in schedules:
        if s.type != model.ScheduleType.course_end:
            s.status = model.ScheduleStatus.delivered
            schedule_service.update(s.id, s)

    reply_message, _ = twilio_chatter_bot.process_message('5548991234567', model.ChatAnswerType.content,
                                                          'content')
    twilio_chatter_bot.tasks_client.send_message.assert_called_with(
        '5548991234567',
        "lesson_content_nothing_pending",
        {},
        None,
        'pt-br',
        workspace.private_channel
    )

    reply_message, _ = twilio_chatter_bot.process_message(
        '5548991234567',
        model.ChatAnswerType.content,
        'content'
    )
    twilio_chatter_bot.tasks_client.send_message.assert_called_with(
        '5548991234567', "lesson_content_nothing_pending", {}, None, 'pt-br', workspace.private_channel
    )


def test_chatter_bot_next_content_invalid_answer(db, workspace, twilio_chatter_bot):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    _ = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')
    twilio_chatter_bot.process_message('5548991234567', model.ChatAnswerType.accept, 'accept')

    reply_message, _ = twilio_chatter_bot.process_message('5548991234567', model.ChatAnswerType.content,
                                                                  'content')
    assert reply_message.find('conteúdo') != -1

    # next equals the reply is empty
    reply_message, _ = twilio_chatter_bot.process_message('5548991234567', None, 'none')
    assert not reply_message


def test_chatter_bot_no_enrollment_message_by_lang(twilio_chatter_bot):
    expectations = [
        ('5548991234567', model.ChatAnswerType.content, '', True),
        ('5548991234567', model.ChatAnswerType.content, 'es-CO', True),
        ('5548991234567', model.ChatAnswerType.content, 'pt-BR', True),
        ('5548991234567', model.ChatAnswerType.content, 'es', True),
        ('5548991234567', model.ChatAnswerType.content, 'pt', True),
        ('5548991234567', model.ChatAnswerType.content, 'en-US', False),
    ]
    for phone, content_type, lang, _ in expectations:
        reply_message, _ = twilio_chatter_bot.process_message(phone, content_type, lang, 'content')
        assert not reply_message


def test_report_performance_type():
    assert service.CourseReportService.performance_type(1.0000001) == 100
    assert service.CourseReportService.performance_type(1.0) == 100
    assert service.CourseReportService.performance_type(0.7) == 100
    assert service.CourseReportService.performance_type(0.700001) == 100
    assert service.CourseReportService.performance_type(0.69999999) == 70
    assert service.CourseReportService.performance_type(0.69) == 70
    assert service.CourseReportService.performance_type(0.569) == 70
    assert service.CourseReportService.performance_type(0.500000000001) == 70
    assert service.CourseReportService.performance_type(0.50) == 70
    assert service.CourseReportService.performance_type(0.49999999) == 50
    assert service.CourseReportService.performance_type(0.49) == 50
    assert service.CourseReportService.performance_type(0.22) == 50
    assert service.CourseReportService.performance_type(0.01) == 50
    assert service.CourseReportService.performance_type(0.00000001) == 0
    assert service.CourseReportService.performance_type(0.0) == 0
    assert service.CourseReportService.performance_type(-0.0) == 0
    assert service.CourseReportService.performance_type(-0.1) == 0


def test_report_message_by_performance_with_certificate(templates_folder):
    def _load_message(message_type, lang):
        if message_type == 'end_course_0':
            return '0'
        if message_type == 'end_course_50':
            return '50'
        if message_type == 'end_course_70':
            return '70'
        if message_type == 'end_course_100':
            return '100'
        return ''

    fake_broker = MagicMock()
    fake_broker.send_media.return_value = ('id', True)
    dispatcher_mock = 'domain.service.MessageDispatcher._load_message'
    with mock.patch(dispatcher_mock) as mock_load_message:
        mock_load_message.side_effect = _load_message
        dispatcher = service.MessageDispatcher(fake_broker, templates_folder, 'image.jpg')
        dispatcher.dispatch_end_course('pt-br', '0', '0', '0', 0, 0, 0, 0, 0, 100, '0', 'certificate.pdf', '')
        assert fake_broker.send_message.call_args_list[0][0][1] == 'end_course_100'
        dispatcher.dispatch_end_course('pt-br', '0', '0', '0', 0, 0, 0, 0, 0, 22, '0', 'certificate.pdf', '')
        assert fake_broker.send_message.call_args_list[1][0][1] == 'end_course_100'
        dispatcher.dispatch_end_course('pt-br', '0', '0', '0', 0, 0, 0, 0, 0, 70, '0', 'certificate.pdf', '')
        assert fake_broker.send_message.call_args_list[2][0][1] == 'end_course_70'
        dispatcher.dispatch_end_course('pt-br', '0', '0', '0', 0, 0, 0, 0, 0, 50, '0', 'certificate.pdf', '')
        assert fake_broker.send_message.call_args_list[3][0][1] == 'end_course_50'
        dispatcher.dispatch_end_course('pt-br', '0', '0', '0', 0, 0, 0, 0, 0, 0, '0', 'certificate.pdf', '')
        assert fake_broker.send_message.call_args_list[4][0][1] == 'end_course_0'

        assert fake_broker.send_media.call_count == 5


def test_report_message_by_performance_no_certificate(templates_folder):
    def _load_message(message_type, lang):
        if message_type == 'end_course_certificate_0':
            return '0'
        if message_type == 'end_course_certificate_50':
            return '50'
        if message_type == 'end_course_certificate_70':
            return '70'
        if message_type == 'end_course_certificate_100':
            return '100'
        return ''

    fake_broker = MagicMock()
    fake_broker.send_media.return_value = ('id', True)
    dispatcher_mock = 'domain.service.MessageDispatcher._load_message'
    with mock.patch(dispatcher_mock) as mock_load_message:
        mock_load_message.side_effect = _load_message
        dispatcher = service.MessageDispatcher(fake_broker, templates_folder, 'image.jpg')
        dispatcher.dispatch_end_course('pt-br', '0', '0', '0', 0, 0, 0, 0, 0, 100, '0', '', '')
        assert fake_broker.send_message.call_args_list[0][0][1] == 'end_course_certificate_100'
        dispatcher.dispatch_end_course('pt-br', '0', '0', '0', 0, 0, 0, 0, 0, 22, '0', '', '')
        assert fake_broker.send_message.call_args_list[1][0][1] == 'end_course_certificate_100'
        dispatcher.dispatch_end_course('pt-br', '0', '0', '0', 0, 0, 0, 0, 0, 70, '0', '', '')
        assert fake_broker.send_message.call_args_list[2][0][1] == 'end_course_certificate_70'
        dispatcher.dispatch_end_course('pt-br', '0', '0', '0', 0, 0, 0, 0, 0, 50, '0', '', '')
        assert fake_broker.send_message.call_args_list[3][0][1] == 'end_course_certificate_50'
        dispatcher.dispatch_end_course('pt-br', '0', '0', '0', 0, 0, 0, 0, 0, 0, '0', '', '')
        assert fake_broker.send_message.call_args_list[4][0][1] == 'end_course_certificate_0'
        dispatcher.dispatch_end_course('pt-br', '0', '0', '0', 0, 0, 0, 0, 0, 100, '0', 'certificate.pdf', '', True)
        assert fake_broker.send_message.call_args_list[0][0][1] == 'end_course_certificate_100'
        dispatcher.dispatch_end_course('pt-br', '0', '0', '0', 0, 0, 0, 0, 0, 22, '0', 'certificate.pdf', '', True)
        assert fake_broker.send_message.call_args_list[1][0][1] == 'end_course_certificate_100'
        dispatcher.dispatch_end_course('pt-br', '0', '0', '0', 0, 0, 0, 0, 0, 70, '0', 'certificate.pdf', '', True)
        assert fake_broker.send_message.call_args_list[2][0][1] == 'end_course_certificate_70'
        dispatcher.dispatch_end_course('pt-br', '0', '0', '0', 0, 0, 0, 0, 0, 50, '0', 'certificate.pdf', '', True)
        assert fake_broker.send_message.call_args_list[3][0][1] == 'end_course_certificate_50'
        dispatcher.dispatch_end_course('pt-br', '0', '0', '0', 0, 0, 0, 0, 0, 0, '0', 'certificate.pdf', '', True)
        assert fake_broker.send_message.call_args_list[4][0][1] == 'end_course_certificate_0'

        assert not fake_broker.send_media.call_count


def test_content_message_without_anticipation(message_dispatcher):
    message_dispatcher.dispatch_lesson_content(
        'pt-br',
        '55489880780003',
        'User',
        'Lesson',
        'Content Name',
        'Content Description',
        True,
        'http://link.com',
        '0',
        7,
        '0',
        '',
        False,
        False,
    )
    sent_message_type = message_dispatcher.broker.send_message.call_args_list[0][0][1]
    assert sent_message_type == "lesson_content_without_content_anticipation"


def test_accept_disclamer(twilio_chatter_bot, db, workspace):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')

    with mock.patch("domain.service.MessageChatterBot._build_response_message") as build_response_message:
        twilio_chatter_bot.accept_disclaimer(enrollment, "yes")
        twilio_chatter_bot.tasks_client.send_message.assert_called_with(
            '5548991234567',
            'course_welcome',
            {'1': 'My course description', '2': '3'},
            None,
            'pt-br',
            None
        )


def test_accept_disclamer_when_course_not_allow_content_aticipation(twilio_chatter_bot, db, workspace):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id, False)
    enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')

    with mock.patch("domain.service.MessageChatterBot._build_response_message") as build_response_message:
        build_response_message.return_value = "message"
        twilio_chatter_bot.accept_disclaimer(enrollment, "yes")
        twilio_chatter_bot.tasks_client.send_message.assert_called_with(
            '5548991234567',
            COURSE_WELCOME_WITHOUT_CONTENT_ANTICIPATION,
            {'1': 'My course description', '2': '3'},
            None,
            'pt-br',
            None
        )

    twilio_chatter_bot.tasks_client.send_message.assert_called()


def test_content_message_with_anticipation_with_drop_out(message_dispatcher):
    message_dispatcher.dispatch_lesson_content(
        'pt-br',
        '55489880780003',
        'User',
        'Lesson',
        'Content Name',
        'Content Description',
        True,
        'http://link.com',
        '0',
        7,
        '0',
        '',
        False,
        True,
    )
    sent_message_type = message_dispatcher.broker.send_message.call_args_list[0][0][1]
    assert sent_message_type == 'lesson_content_without_content_anticipation_with_give_up_option'


def test_content_message_without_anticipation_with_drop_out(message_dispatcher):
    message_dispatcher.dispatch_lesson_content(
        'pt-br',
        '55489880780003',
        'User',
        'Lesson',
        'Content Name',
        'Content Description',
        False,
        'http://link.com',
        '0',
        7,
        '0',
        '',
        False,
        True,
    )
    message_rendered = message_dispatcher.broker.send_message.call_args_list[0][0][1]
    assert message_rendered == 'lesson_content_without_content_anticipation_with_give_up_option'


def test_chatter_bot_finish_course(db, workspace, twilio_chatter_bot, schedule_service, schedule_admin, mocker):
    mocker.patch(UPDATE_ENROLLMENT_PROGRESS_PATH)
    _ = utils.create_course_and_accept_enrollment(db, workspace, '5548991234567', 'pt-br', 'America/Sao_Paulo',
                                                  datetime.datetime(2020, 2, 29, 9, 8, 7), -3, schedule_admin)
    with freezegun.freeze_time('2020-03-08 00:00:01', tz_offset=-3):
        schedules = schedule_service.load_pending_schedules(1, 99)
        assert len(schedules) == 9
        for i in range(8):
            schedule = schedules[i]
            schedule_admin.dispatch(schedule.id)

        # can not finish the course with pending contents
        reply_message, _ = twilio_chatter_bot.process_message('5548991234567', model.ChatAnswerType.finish,
                                                                      'finish')
        assert reply_message.find('conteúdos com envio pendente') != -1

        # send the last content
        schedule = schedules[8]
        schedule_admin.dispatch(schedule.id)

        # finish
        reply_message, _ = twilio_chatter_bot.process_message('5548991234567', model.ChatAnswerType.finish,
                                                                      'finish')
        assert reply_message.find('antecipado') != -1

        # next equals the reply is empty
        reply_message, _ = twilio_chatter_bot.process_message('5548991234567', model.ChatAnswerType.finish,
                                                                      'finish')
        assert reply_message.find('Aparentemente você não possui cursos para antecipar') != -1


def test_enrollment_allow_access_if_is_started(db, workspace, enrollment_service, schedule_admin):
    enrollment = utils.create_course_and_accept_enrollment(db, workspace, '5548991234567', 'pt-br', 'America/Sao_Paulo',
                                                           datetime.datetime(2020, 2, 29, 9, 8, 7), -3, schedule_admin)
    assert enrollment_service.user_can_access_contents(enrollment.id)
    assert enrollment_service.user_can_access_contents(enrollment.user_id)

    for s in [model.EnrollmentStatus.waiting, model.EnrollmentStatus.completed, model.EnrollmentStatus.refused]:
        enrollment.status = s
        db.flush()
        assert not enrollment_service.user_can_access_contents(enrollment.id)
        assert not enrollment_service.user_can_access_contents(enrollment.user_id)


def test_enrollment_allow_access_when_at_least_a_user_has_started_enrollment(db, workspace, enrollment_service,
                                                                             schedule_admin):
    enrollment = utils.create_course_and_accept_enrollment(db, workspace, '5548991234567', 'pt-br', 'America/Sao_Paulo',
                                                           datetime.datetime(2020, 2, 29, 9, 8, 7), -3, schedule_admin)
    enrollment2 = utils.create_course_and_accept_enrollment(db, workspace, '5548991234566', 'pt-br', 'America/Sao_Paulo',
                                                            datetime.datetime(2020, 2, 29, 9, 8, 7), -3, schedule_admin)
    enrollment2.user_id = enrollment.user_id
    db.flush()

    enrollment.status = model.EnrollmentStatus.completed
    db.flush()
    assert enrollment_service.user_can_access_contents(enrollment.user_id)

    enrollment2.status = model.EnrollmentStatus.completed
    db.flush()
    assert not enrollment_service.user_can_access_contents(enrollment.user_id)


def test_twilio_private_channel_url_parse():
    for url in ['twilio://MeuU7uári0:MinhaS*!Senh?a@+5548991233',
                '://MeuU7uári0:MinhaS*!Senh?a@+5548991233',
                '//MeuU7uári0:MinhaS*!Senh?a@+5548991233',
                '/MeuU7uári0:MinhaS*!Senh?a@+5548991233',
                'MeuU7uári0:MinhaS*!Senh?a@+5548991233',
                'twilio://MeuU7u%C3%A1ri0:MinhaS%2A%21Senh%3Fa@%2B5548991233',
                'MeuU7u%C3%A1ri0:MinhaS%2A%21Senh%3Fa@%2B5548991233']:
        user, password, phone = client.WhatsAppTwilioClient.parse_channel_url(url)
        assert user == 'MeuU7uári0'
        assert password == 'MinhaS*!Senh?a'
        assert phone == '+5548991233'

    for url in ['', None, 'abc@+55489912398', ':abc@+55489912398', '@+55489912398']:
        user, password, phone = client.WhatsAppTwilioClient.parse_channel_url(url)
        assert not user
        assert not password
        assert not phone


def test_one_enrollment_at_time(db, workspace, enrollment_service, schedule_admin):
    phone1 = '5548991234567'
    phone2 = '5548997654321'
    enrollment1 = utils.create_course_and_accept_enrollment(db, workspace, phone1, 'pt-br', 'America/Sao_Paulo',
                                                            datetime.datetime(2020, 2, 29, 9, 8, 7), -3, schedule_admin)
    enrollment2 = utils.create_course_and_accept_enrollment(db, workspace, phone2, 'pt-br', 'America/Sao_Paulo',
                                                            datetime.datetime(2020, 2, 29, 9, 8, 7), -3, schedule_admin)
    # same course
    with pytest.raises(domain.exceptions.service_exceptions.IntegrityException,
                       match='The user has an enrollment in progress'):
        _ = utils.create_enrollment(db, enrollment1.user_id, enrollment1.course_id, workspace.id, 'America/Sao_Paulo')

    # other course
    with pytest.raises(domain.exceptions.service_exceptions.IntegrityException,
                       match='The user has an enrollment in progress'):
        _ = utils.create_enrollment(db, enrollment1.user_id, enrollment2.course_id, workspace.id, 'America/Sao_Paulo')

    # finish or refuse enrollment and everything is ok
    statuses = [model.EnrollmentStatus.refused, model.EnrollmentStatus.completed]
    for s in statuses:
        enrollment1.status = s
        db.flush()
        enrollment1 = utils.create_enrollment(db, enrollment1.user_id, enrollment1.course_id,
                                              workspace.id, 'America/Sao_Paulo')
        assert enrollment1.status == model.EnrollmentStatus.waiting


def test_notification_service_not_notify_if_type_not_found(db, notification_service):
    notification_service.notify_error('Error', 'An Error')
    notification_service.notify_report('Url', 'www.url.com')
    notification_service.notify_course('Course', 'course')
    notification_service.notify_course_download('Course', 'www.url.com')
    notifications = db.query(model.Notification).all()
    assert not notifications


def test_notification_service(db, notification_service):
    notification_types = [('Error', ''), ('Report', ''), ('Course', 'Redirect'), ('Course', 'Download')]
    for t, a in notification_types:
        notification = model.NotificationType()
        notification.type = t
        notification.action = a
        db.add(notification)
    db.commit()

    notification_service.notify_error('Error', 'An Error')
    notification_service.notify_report('Url', 'www.url.com')
    notification_service.notify_course('Course', 'course')
    notification_service.notify_course_download('Course', 'www.url.com')
    notifications = db.query(model.Notification).all()
    assert len(notifications) == 4


def test_notification_service_read_all_notifications(db, workspace, notification_service):
    user = utils.create_user(db, '5548996887365', workspace.id)
    notification = model.Notification()
    notification.message = 'Teste'
    notification.workspace_id = workspace.id
    notification.user_id = user.id
    db.add(notification)
    db.commit()
    notification_service.override_user(workspace.id, user.id)
    notification_service.read_all_notifications()
    notifications = db.query(model.Notification). \
            filter(model.Notification.workspace_id == workspace.id). \
            filter(model.Notification.user_id == user.id). \
            filter(model.Notification.read == True)
    assert notifications.count() == 1


def test_course_service_load_lesson_must_sort_by_order_as_default(db, workspace, course_service):
    user = utils.create_user(db, '5548996887365', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    lessons, _ = course_service.load_all_lessons(course.id, {})
    assert len(lessons) == 3
    assert lessons[0].order == 0
    assert lessons[0].name == 'Lesson 0'
    assert lessons[1].order == 1
    assert lessons[1].name == 'Lesson 1'
    assert lessons[2].order == 2
    assert lessons[2].name == 'Lesson 2'

    # change order
    lessons[0].order = 2
    lessons[1].order = 0
    lessons[2].order = 1
    db.commit()

    lessons, _ = course_service.load_all_lessons(course.id, {})
    assert len(lessons) == 3
    assert lessons[0].order == 0
    assert lessons[0].name == 'Lesson 1'
    assert lessons[1].order == 1
    assert lessons[1].name == 'Lesson 2'
    assert lessons[2].order == 2
    assert lessons[2].name == 'Lesson 0'


def test_user_service_add_must_update_user_if_already_exists(db, workspace):
    user = model.User()
    user.name = 'Leonardo'
    user.phone = '+5548910101010'
    user.email = '<EMAIL>'
    user.avatar = 'https://www.avatar.com/123456'
    user.tags = 'ABC'

    user_service = service.UserService(db, workspace.id)
    user_service.add(user)
    users = user_service.load_all()
    assert len(users) == 1
    assert users[0].name == 'Leonardo'

    user2 = model.User()
    user2.name = 'Leonardo Vitor da Silva'
    user2.phone = '+5548910101010'
    user_service.add(user2)
    users = user_service.load_all()
    assert len(users) == 1
    assert users[0].name == 'Leonardo Vitor da Silva'
    assert users[0].email == '<EMAIL>'
    assert users[0].avatar == 'https://www.avatar.com/123456'
    assert users[0].tags == 'ABC'


def test_chatter_bot_answer_type(twilio_chatter_bot):
    assert twilio_chatter_bot.answer_type('sim, quero continuar') == model.ChatAnswerType.accept
    assert twilio_chatter_bot.answer_type('não') == model.ChatAnswerType.reject
    assert twilio_chatter_bot.answer_type('quero o conteúdo já') == model.ChatAnswerType.content
    assert twilio_chatter_bot.answer_type('próximo conteúdo') == model.ChatAnswerType.content
    assert twilio_chatter_bot.answer_type('receber agora') == model.ChatAnswerType.content
    assert twilio_chatter_bot.answer_type('finalizar matrícula') == model.ChatAnswerType.finish
    assert twilio_chatter_bot.answer_type('receber certificado') == model.ChatAnswerType.certificate
    assert twilio_chatter_bot.answer_type('quero desistir') == model.ChatAnswerType.drop_out


def test_filter_builder():
    with freezegun.freeze_time('2020-03-08 07:00:01', tz_offset=-3):
        expected = {
            'filter_spec': [
                {'field': 'status', 'op': '==', 'value': 'waiting'},
                {'field': 'start_date', 'op': '<=', 'value': datetime.datetime(2020, 3, 8, 7, 0, 1)}
            ],
            'sort_spec': [
                {'direction': 'asc', 'field': 'start_date', 'nullsfirst': True}
            ],
            'pag_spec': {
                'page': 1,
                'per_page': 50
            }
        }
        filters = service.FilterBuilder().eq('status', 'waiting').lte('start_date', datetime.datetime.utcnow()). \
            order_by('start_date').page(1, 50).build()
        assert not deepdiff.DeepDiff(filters, expected)

        filters = service.FilterBuilder().eq('status', 'waiting').lte('start_date', datetime.datetime.utcnow()). \
            order_by('-start_date').page(2, 40).page(1, 50).build()
        expected['sort_spec'] = [{'direction': 'desc', 'field': 'start_date', 'nullslast': True}]
        assert not deepdiff.DeepDiff(filters, expected)


def test_message_dispatcher_language_name(message_dispatcher):
    message_expected_es = 'Respuesta no reconocida, sea más específico en su solicitud.'
    message = message_dispatcher.build_message('answer_not_recognized', 'es', {})
    assert message == message_expected_es

    message = message_dispatcher.build_message('answer_not_recognized', 'es-co', {})
    assert message == message_expected_es

    message_expected_pt = 'Resposta não reconhecida, por favor seja mais especifico na sua solicitação.'
    message = message_dispatcher.build_message('answer_not_recognized', 'pt-BR', {})
    assert message == message_expected_pt


def test_course_backup_only_bkp_and_delete_if_status_is_deleting(db, workspace, course_service):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)

    general_mock = mock.MagicMock()
    course_backup = service.CourseBackupService(course_service, general_mock, general_mock, general_mock, general_mock)

    backup_fund_mock = 'domain.service.CourseBackupService.backup_course'
    with mock.patch(backup_fund_mock) as mock_backup_func:
        assert not course_backup.backup_and_delete(course.id)
        assert not mock_backup_func.call_count


def test_course_backup_verify_if_is_internal_content():
    internal_urls = ['http://keepsdev.com', 'https://keepsdev.com/123',
                     'http://cc.keepsdev.com', 'https://abc.keepsdev.com/123',
                     'http://s3.amazonaws.com', 'https://s3.amazonaws.com', 'https://s3.amazonaws.com/123']
    external_urls = ['http://cc.workspace.com.br/123', 'https://cc.aaaa.com', 'https://youtube.com/123']
    invalid_urls = [None, '', 'keepsdev.com', 's3.amazonaws.com']
    for url in internal_urls:
        assert service.CourseBackupService.is_internal_content(url)
    for url in external_urls:
        assert not service.CourseBackupService.is_internal_content(url)
    for url in invalid_urls:
        assert not service.CourseBackupService.is_internal_content(url)


def test_course_backup_shortname_generator():
    urls = ['http://keepsdev.com/123.jpg', 'https://keepsdev.com/123/ooo111.png',
            'http://cc.keepsdev.com', 'https://abc.keepsdev.com/123.png']
    content_names = [None, '', 'Aulão legal', 'Conteúdo 01']
    expectations = ['123.jpg', 'ooo111.png', 'Aulo_legal.com', 'Contedo_01.png']
    for i, expected in enumerate(expectations):
        url = urls[i]
        content_name = content_names[i]
        assert service.CourseBackupService.define_file_shortname(content_name, url) == expected


def test_course_backup_deletes_after_backup():
    course = model.Course()
    course.status = model.CourseStatus.deleting
    course_service = mock.MagicMock()
    course_service.load.return_value = course
    course_backup = service.CourseBackupService(course_service, None, None, None, None)

    callbacks = []

    def _backup(_):
        callbacks.append('B')
        return 'file show'

    def _delete(_, __):
        callbacks.append('D')

    backup_func_mock = 'domain.service.CourseBackupService.backup_course'
    delete_func_mock = 'domain.service.CourseBackupService.delete_course'
    with mock.patch(backup_func_mock) as mock_backup_func:
        with mock.patch(delete_func_mock) as mock_delete_func:
            mock_backup_func.side_effect = _backup
            mock_delete_func.side_effect = _delete
            assert course_backup.backup_and_delete(course.id) == 'file show'
            assert callbacks == ['B', 'D']


def test_course_backup_delete_notifies_first():
    course = model.Course()
    course.status = model.CourseStatus.deleting
    course_service = mock.MagicMock()
    course_service.load.return_value = course
    callbacks = []

    def _notify(_, __):
        callbacks.append('N')

    def _delete(_):
        callbacks.append('D')

    course_service.delete.side_effect = _delete
    notification_service = mock.MagicMock()
    notification_service.notify_course_download.side_effect = _notify
    backup_func_mock = 'domain.service.CourseBackupService.backup_course'
    with mock.patch(backup_func_mock) as mock_backup_func:
        course_backup = service.CourseBackupService(course_service, notification_service, None, None, None)
        course_backup.backup_and_delete(course.id)
        assert mock_backup_func.call_count == 1
        assert callbacks == ['N', 'D']


@mock.patch('domain.service.CourseBackupService.delete_course')
@mock.patch('domain.service.CourseBackupService.clean_temp_dir')
@mock.patch('domain.service.CourseBackupService.upload_compacted_file')
@mock.patch('domain.service.CourseBackupService.save_structure')
@mock.patch('domain.service.CourseBackupService.save_quizzes')
@mock.patch('domain.service.CourseBackupService.download_files_and_urls')
@mock.patch('domain.service.CourseBackupService.build_temp_dirs')
def test_course_backup_assert_mocks_call(mock_build_temp_dirs, mock_download_files_and_urls, mock_save_quizzes,
                                         mock_save_structure, mock_upload_compacted_file, mock_clean_temp_dir,
                                         mock_delete_course):
    course = model.Course()
    course.status = model.CourseStatus.deleting
    course_service = mock.MagicMock()
    course_service.load.return_value = course
    callbacks = []

    def _temp_dirs():
        callbacks.append('1')
        return 'dir', 'dir_file'

    def _download_files_and_urls(_, _2):
        callbacks.append('2')
        return {}

    def _save_quizzes(_, _2):
        callbacks.append('3')
        return 'quizzes_filename'

    def _save_structure(_, _2, _3, _4):
        callbacks.append('4')

    def _upload_compacted_file(_):
        callbacks.append('5')
        return 'result_file'

    def _clean_temp_dir(_):
        callbacks.append('6')

    mock_build_temp_dirs.side_effect = _temp_dirs
    mock_download_files_and_urls.side_effect = _download_files_and_urls
    mock_save_quizzes.side_effect = _save_quizzes
    mock_save_structure.side_effect = _save_structure
    mock_upload_compacted_file.side_effect = _upload_compacted_file
    mock_clean_temp_dir.side_effect = _clean_temp_dir
    course_backup = service.CourseBackupService(course_service, None, None, None, None)
    assert course_backup.backup_and_delete(course.id) == 'result_file'
    assert callbacks == ['1', '2', '3', '4', '5', '6']


def test_percentage_format(report_service):
    values = [
        (0.0, '0%'),
        (0.222, '23%'),
        (0.01000, '1%'),
        (0.010002, '2%'),
        (0.012, '2%'),
        (0.5, '50%'),
        (0.62, '62%'),
        (0.625, '63%'),
        (1.0, '100%'),
        (1.1, '100%'),
        (100.9, '100%'),
        (101, '100%'),
    ]
    for p, p_formatted in values:
        assert report_service.format_percentage(p) == p_formatted


def test_do_not_generate_certificate_for_low_performance(report_service, certificate_service_mock, workspace):
    invalid_percentages = [0, 0.1, 0.00001, 0.2, 0.4, 0.49]
    for p in invalid_percentages:
        course_performance = {'percentage': p, 'name': 'my-name', 'learn_duration': 1}
        certificate_service_mock.generate_certificate.assert_not_called()


def test_generate_content_url(mocker, db):
    create_token = mocker.patch('domain.service.ScheduleAdminService.create_token')
    short_link = mocker.patch("domain.client.FirebaseClient.short_link")

    content_url = 'www.perdeu.com'
    schedule_admin = _schedule_admin_service(db, content_url)
    user_id = uuid.uuid4()
    enrollment_id = uuid.uuid4()
    token_expiration = 2
    lang = 'pt-br'
    private_channel = '{"pt-br": "user:1234@+55999999999999"}'

    workspace = utils.create_workspace(db, private_channel=private_channel)
    user = utils.create_user(db, '+55999999999992', workspace.id)
    course = utils.create_course_finished(db, lang, workspace.id, user.id)
    utils.create_lessons(db, course)
    content = db.query(model.Content).first()

    params = {
        'token': 'token',
        'lang': lang,
        'client_id': workspace.id,
        'phone': '+55999999999999'
    }
    create_token.return_value = 'token'

    schedule_admin.generate_content_url(content, user_id, enrollment_id, token_expiration, lang)

    create_token.assert_called_with(user_id, lang, workspace.id, token_expiration, enrollment_id)
    short_link.assert_called_with(
        f'{content_url}/{content.id}?{urllib.parse.urlencode(params)}',
        content.lesson.name,
        content.name,
        None
    )


def test_should_raise_exception_when_channel_type_invalid_in_private_channel_service_init(db):
    with pytest.raises(ValueError, match="Invalid channels param, expected LanguageChannels instance"):
        PrivateChannelService('texto')


def _schedule_admin_service(db, content_url):
    chat_service = service.ChatService(db)
    firebase_client = client.FirebaseClient('', '')
    certificate_service = MagicMock(spec=CertificateService)
    schedule_admin = service.ScheduleAdminService(
        MagicMock(),
        MagicMock(),
        MagicMock(),
        chat_service,
        content_url,
        'secretfoda',
        MagicMock(),
        'www.callback.com',
        firebase_client,
        certificate_service,
        PrivateChannelService(model.LanguageChannels(''))
    )
    return schedule_admin
