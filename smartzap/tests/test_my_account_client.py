import unittest
from unittest.mock import patch, MagicMock

from domain.client import MyA<PERSON>unt<PERSON><PERSON>
from domain.clients.token_service import TokenService


class TestMyAccountClient(unittest.TestCase):

    def setUp(self):
        self.url = "https://mock-api.myaccount.com"
        self.api_token = "mock-api-token"
        self.cache = MagicMock()
        self.token_service = MagicMock(spec=TokenService)
        self.client = MyAccountClient(self.url, self.api_token, self.cache, self.token_service)

    @patch("requests.post")
    def test_create_user_success(self, mock_post):
        token = "mock-integration"
        self.token_service.get_integration_token.return_value = token

        mock_response = MagicMock()
        mock_response.status_code = 201
        mock_response.json.return_value = [
            {
                "id": "mock-user-id",
                "email": "<EMAIL>"
            }
        ]
        mock_post.return_value = mock_response

        user_data = {
            "email": "<EMAIL>",
            "name": "Test User"
        }
        workspace_id = "mock-workspace-id"
        response = self.client.create_user(user_data, workspace_id)

        expected_user_payload = {
            **user_data, 'language': 'ea636f50-fdc4-49b0-b2de-9e5905de456b'
        }

        self.assertEqual(response["id"], "mock-user-id")
        self.assertEqual(response["email"], "<EMAIL>")
        mock_post.assert_called_once_with(
            f"{self.client._url_users.format(workspace_id)}",
            json={
                "permissions": [self.client.smartzap_user_role_id],
                "users": [expected_user_payload]
            },
            headers={
                "Authorization": f"{token}",
                "x-client": workspace_id
            }
        )

