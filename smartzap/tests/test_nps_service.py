import unittest
from unittest import mock
from unittest.mock import MagicMock
from domain.model import Enrollment, EnrollmentStatus, Course
from domain.service import CourseService, EnrollmentService
from domain.services.nps_service import NPSService
from domain.exceptions.nps_exceptions import EnrollmentNotCompletedException, FeedbackA<PERSON>readyRegisteredException, \
    CourseNotAcceptingNPSSurveyException, NPSScoreOutOfRangeException
from domain.signals.events import CREATED_KEY


class TestNPSService(unittest.TestCase):
    def setUp(self):
        self.database_session = MagicMock()
        self.course_service = MagicMock(spec=CourseService)
        self.enrollment_service = MagicMock(spec=EnrollmentService)

        self.nps_service = NPSService(self.database_session, self.course_service, self.enrollment_service)

    @mock.patch('domain.signals.events.course_nps_score_event.emit')
    def test_register_enrollment_score_success(self, course_nps_score_event_emit):
        course = MagicMock(allow_nps_survey=True)
        enrollment = Enrollment(id="1", status=EnrollmentStatus.completed, nps_score=None, course=course)
        self.enrollment_service.update_entity = MagicMock()

        self.nps_service.register_enrollment_score(enrollment, score=9)

        self.enrollment_service.update_entity.assert_called_once_with(enrollment.id, {"nps_score": 9})
        course_nps_score_event_emit.assert_called_once_with(CREATED_KEY, course=enrollment.course)

    def test_register_enrollment_score_not_completed(self):
        course = MagicMock(allow_nps_survey=True)
        enrollment = Enrollment(id="1", status=EnrollmentStatus.started, nps_score=None, course=course)

        with self.assertRaises(EnrollmentNotCompletedException):
            self.nps_service.register_enrollment_score(enrollment, score=9)

    def test_register_enrollment_score_already_registered(self):
        course = MagicMock(allow_nps_survey=True)
        enrollment = Enrollment(id="1", status=EnrollmentStatus.completed, nps_score=8, course=course)

        with self.assertRaises(FeedbackAlreadyRegisteredException):
            self.nps_service.register_enrollment_score(enrollment, score=9)

    def test_register_enrollment_score_course_not_accepting(self):
        course = MagicMock(allow_nps_survey=False)
        enrollment = Enrollment(id="1", status=EnrollmentStatus.completed, nps_score=None, course=course)

        with self.assertRaises(CourseNotAcceptingNPSSurveyException):
            self.nps_service.register_enrollment_score(enrollment, score=9)

    def test_register_enrollment_score_nps_score_out_of_range(self):
        course = MagicMock(allow_nps_survey=True)
        enrollment = Enrollment(id="1", status=EnrollmentStatus.completed, nps_score=None, course=course)

        with self.assertRaises(NPSScoreOutOfRangeException):
            self.nps_service.register_enrollment_score(enrollment, score=11)

    def test_calc_course_score_success(self):
        enrollments = [
            Enrollment(id="1", status=EnrollmentStatus.completed, nps_score=10),
            Enrollment(id="2", status=EnrollmentStatus.completed, nps_score=9),
            Enrollment(id="3", status=EnrollmentStatus.completed, nps_score=6)
        ]
        course = MagicMock(spec=Course(id="1"))
        self.course_service.load.return_value = course
        self.course_service.update_entity = MagicMock()
        self.enrollment_service.db = MagicMock()
        self.enrollment_service.db.query.return_value.filter.return_value = enrollments
        expected_score = 33

        self.nps_service.calc_course_score(course.id)

        self.course_service.update_entity.assert_called_once_with(course.id, {"nps_score": expected_score})

    def test_calc_no_responses(self):
        nps = self.nps_service.calc([])

        self.assertEqual(nps, 0)

    def test_calc_with_scores(self):
        scores = [10, 9, 8, 7, 6, 5, 10, 9, 3]
        nps = self.nps_service.calc(scores)

        self.assertEqual(nps, 11)  # 4 Promoters, 3 Detractors, 2 Passives
