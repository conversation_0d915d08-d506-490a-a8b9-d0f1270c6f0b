import marshmallow
import pytest

from domain import schema


def test_power_text_validator_length():
    validator = schema.PowerTextValidator(10, False, False, False)
    with pytest.raises(marshmallow.validate.ValidationError, match='Longer than maximum length 10.'):
        validator('Meu testo tem mais que 10 caracteres')


def test_power_text_validator_linebreak():
    validator = schema.PowerTextValidator(100, True, False, False)
    with pytest.raises(marshmallow.validate.ValidationError, match='Line breaks are not accepted.'):
        validator('Meu testo com\n quebras de linha.')
    with pytest.raises(marshmallow.validate.ValidationError, match='Line breaks are not accepted.'):
        validator('Meu testo com\r return.')


def test_power_text_validator_url():
    validator = schema.PowerTextValidator(100, False, True, False)
    with pytest.raises(marshmallow.validate.ValidationError, match='Url inside text is not accepted.'):
        validator('Meu sitio na internet: www.sitiomeu.com.br/inscrição. Obrigado. ')
    with pytest.raises(marshmallow.validate.ValidationError, match='Url inside text is not accepted.'):
        validator('Inscreva-se aqui: https://www.sitiomeu.com.br/inscricao?newUser=true. Obrigado. ')


def test_power_text_validator_empty():
    validator = schema.PowerTextValidator(10, False, False, True)
    with pytest.raises(marshmallow.validate.ValidationError, match='Empty is not accepted.'):
        validator('')
    with pytest.raises(marshmallow.validate.ValidationError, match='Empty is not accepted.'):
        validator(None)
    with pytest.raises(marshmallow.validate.ValidationError, match='Empty is not accepted.'):
        validator('    ')
