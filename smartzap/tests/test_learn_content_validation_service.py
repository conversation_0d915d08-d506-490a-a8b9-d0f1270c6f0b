import unittest
from typing import Dict

from domain.services.learn_content_validation_service import BaseMimeTypeValidator, VideoMimeTypeValidator, \
    LearnContentValidationService


class LearnContentValidationServiceTestCase(unittest.TestCase):
    def setUp(self):
        self.validators: Dict[str, BaseMimeTypeValidator] = {
            "video/mp4": VideoMimeTypeValidator(),
            "video/3gp": VideoMimeTypeValidator(),
        }
        self.service = LearnContentValidationService(validators=self.validators)

    def test_valid_mp4_under_limit(self):
        content = {"file_mime_type": "video/mp4", "file_size": 10}
        self.assertTrue(self.service.is_valid_whatsapp_content(content))

    def test_valid_3gp_at_limit(self):
        content = {"file_mime_type": "video/3gp", "file_size": 16}
        self.assertTrue(self.service.is_valid_whatsapp_content(content))

    def test_invalid_mp4_over_limit(self):
        content = {"file_mime_type": "video/mp4", "file_size": 20}
        self.assertFalse(self.service.is_valid_whatsapp_content(content))

    def test_invalid_3gp_none_size(self):
        content = {"file_mime_type": "video/3gp", "file_size": None}
        self.assertFalse(self.service.is_valid_whatsapp_content(content))

    def test_invalid_mp4_missing_size(self):
        content = {"file_mime_type": "video/mp4"}
        self.assertFalse(self.service.is_valid_whatsapp_content(content))

    def test_unsupported_mime_type_returns_true_by_default(self):
        content = {"file_mime_type": "application/pdf", "file_size": 5}
        self.assertTrue(self.service.is_valid_whatsapp_content(content))

    def test_missing_mime_type_uses_default_validator(self):
        content = {"file_size": 5}
        self.assertTrue(self.service.is_valid_whatsapp_content(content))
