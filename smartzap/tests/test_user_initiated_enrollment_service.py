import unittest
import uuid
from unittest.mock import MagicMock
from domain.model import Enrollment, EnrollmentStatus, User
from domain.service import EnrollmentService, UserService, CourseService
from domain.exceptions.service_exceptions import UserNotFound, CourseNotFound
from domain.services.user_initiated_enrollment_service import UserInitiatedEnrollmentService


class TestUserInitiatedEnrollmentService(unittest.TestCase):
    def setUp(self):
        self.enrollment_service = MagicMock(spec=EnrollmentService)
        self.user_service = MagicMock(spec=UserService)
        self.course_service = MagicMock(spec=CourseService)

        self.service = UserInitiatedEnrollmentService(
            enrollment_service=self.enrollment_service,
            user_service=self.user_service,
            course_service=self.course_service,
        )

    def test_enroll_success(self):
        user = User(id="user-1", phone="+123456789")
        course_id = "course-1"
        workspace_id = "workspace-1"

        self.user_service.get_by_phone.return_value = user
        self.user_service.get_user_workspace_ids.return_value = [workspace_id]
        self.course_service.db = MagicMock()
        self.course_service.db.query.return_value.join.return_value.filter.return_value.scalar.return_value = workspace_id
        self.enrollment_service.add.return_value = Enrollment(
            user_id=user.id,
            course_id=course_id,
            workspace_id=workspace_id,
            status=EnrollmentStatus.started,
        )

        enrollment = self.service.enroll(user_phone=user.phone, course_id=course_id)

        self.user_service.get_by_phone.assert_called_once_with(phone=user.phone, raise_exception=True)
        self.user_service.get_user_workspace_ids.assert_called_once_with(user.id)
        self.course_service.db.query.assert_called_once()
        self.enrollment_service.add.assert_called_once()

        self.assertEqual(enrollment.user_id, user.id)
        self.assertEqual(enrollment.course_id, course_id)
        self.assertEqual(enrollment.workspace_id, workspace_id)
        self.assertEqual(enrollment.status, EnrollmentStatus.started)

    def test_enroll_course_not_found(self):
        user = User(id="user-1", phone="+123456789")
        self.user_service.get_by_phone.return_value = user
        self.user_service.get_user_workspace_ids.return_value = ["workspace-1"]

        self.course_service.db = MagicMock()
        self.course_service.db.query.return_value.join.return_value.filter.return_value.scalar.return_value = None

        with self.assertRaises(CourseNotFound):
            self.service.enroll(user_phone=user.phone, course_id="course-1")

        self.user_service.get_by_phone.assert_called_once_with(phone=user.phone, raise_exception=True)
        self.user_service.get_user_workspace_ids.assert_called_once_with(user.id)
        self.course_service.db.query.assert_called_once()
