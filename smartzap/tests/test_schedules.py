import datetime
from unittest import mock
from unittest.mock import MagicMock
import uuid

import dateutil
import freezegun
import pytest

import domain.exceptions.service_exceptions
from domain import model, service
from . import utils

UPDATE_ENROLLMENT_PROGRESS_PATH = 'tasks.task.update_enrollment_progress.delay'


def test_create_schedule_in_brazil(db, workspace, schedule_service):
    with freezegun.freeze_time('2020-02-29 09:08:07', tz_offset=-3):
        user = utils.create_user(db, '5548991234567', workspace.id)
        course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
        enrollment = utils.create_enrollment(
            db, user.id, course.id, workspace.id, 'America/Sao_Paulo'
        )

        my_schedules = schedule_service.load_all()
        assert len(my_schedules) == 1

        schedule_service.create_schedules(enrollment)
        expected_schedules = [
            (model.ScheduleType.course_introduction, '2020-02-29 09:09:07'),
            (model.ScheduleType.lesson_content, '2020-03-01 12:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-02 22:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-04 12:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-05 17:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-06 12:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-06 22:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-07 12:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-07 17:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-07 22:00:00'),
            (model.ScheduleType.course_end, '2020-03-09 12:00:00'),
        ]
        my_schedules = schedule_service.load_all()
        for i, s in enumerate(my_schedules):
            expected_type, expected_date = expected_schedules[i]
            parsed_expected_date = dateutil.parser.parse(expected_date)
            assert s.type == expected_type
            assert s.send_date == parsed_expected_date


def test_create_schedules_without_send_date_when_twilio_studio_is_enabled(
    db,
    workspace_with_messages_by_twilio_studio,
    schedule_service
):
    workspace = workspace_with_messages_by_twilio_studio
    with freezegun.freeze_time('2020-02-29 09:08:07', tz_offset=-3):
        user = utils.create_user(db, '5548991234567', workspace.id)
        course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
        enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')

        my_schedules = schedule_service.load_all()
        assert len(my_schedules) == 1

        schedule_service.create_schedules(enrollment)
        expected_schedules = [
            (model.ScheduleType.course_introduction, '2020-02-29 09:09:07'),
            (model.ScheduleType.lesson_content, '2020-03-01 12:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-02 22:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-04 12:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-05 17:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-06 12:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-06 22:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-07 12:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-07 17:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-07 22:00:00'),
            (model.ScheduleType.course_end, None),
        ]

        my_schedules = schedule_service.load_all()
        for i, s in enumerate(my_schedules):
            expected_type, expected_date = expected_schedules[i]
            parsed_expected_date = None
            if expected_date:
                parsed_expected_date = dateutil.parser.parse(expected_date)
            assert s.type == expected_type
            assert s.send_date == parsed_expected_date


def test_create_schedule_colombia(db, workspace, schedule_service):
    with freezegun.freeze_time('2020-02-29 09:08:07', tz_offset=-5):
        user = utils.create_user(db, '5548991234567', workspace.id)
        course = utils.create_course_finished(db, 'es', workspace.id, user.id)
        enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Bogota')
        schedule_service.create_schedules(enrollment)
        expected_schedules = [
            (model.ScheduleType.course_introduction, '2020-02-29 09:09:07'),
            (model.ScheduleType.lesson_content, '2020-03-01 14:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-03 00:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-04 14:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-05 19:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-06 14:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-07 00:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-07 14:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-07 19:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-08 00:00:00'),
            (model.ScheduleType.course_end, '2020-03-09 14:00:00'),
        ]
        my_schedules = schedule_service.load_all()
        for i, s in enumerate(my_schedules):
            expected_type, expected_date = expected_schedules[i]
            assert s.type == expected_type
            assert s.send_date == dateutil.parser.parse(expected_date)


def test_schedule_must_use_only_date_from_scheduling(db, workspace, schedule_service):
    with freezegun.freeze_time('2020-03-01 01:08:00', tz_offset=-5):
        user = utils.create_user(db, '5548991234567', workspace.id)
        course = utils.create_course_finished(db, 'es', workspace.id, user.id)
        enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Bogota')
        schedule_service.create_schedules(enrollment)
        my_schedules = schedule_service.load_all()
        schedule1 = my_schedules[0]
        schedule2 = my_schedules[1]
        assert schedule1.send_date.day == 1
        assert schedule2.send_date.day == 1


def test_create_schedule_in_utc_if_enrollment_timezone_is_missing(db, workspace, schedule_service):
    with freezegun.freeze_time('2020-02-29 09:08:07'):
        user = utils.create_user(db, '5548991234567', workspace.id)
        course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
        enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, '')
        schedule_service.create_schedules(enrollment)
        expected_schedules = [
            (model.ScheduleType.course_introduction, '2020-02-29 09:09:07'),
            (model.ScheduleType.lesson_content, '2020-03-01 09:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-02 19:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-04 09:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-05 14:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-06 09:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-06 19:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-07 09:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-07 14:00:00'),
            (model.ScheduleType.lesson_content, '2020-03-07 19:00:00'),
            (model.ScheduleType.course_end, '2020-03-09 09:00:00'),
        ]
        my_schedules = schedule_service.load_all()
        for i, s in enumerate(my_schedules):
            expected_type, expected_date = expected_schedules[i]
            parsed_expected_date = dateutil.parser.parse(expected_date)
            assert s.type == expected_type
            assert s.send_date == parsed_expected_date


def test_simulate_schedules_dispatching(db, workspace, schedule_service, mocker):
    mocker.patch(UPDATE_ENROLLMENT_PROGRESS_PATH)
    course_report = MagicMock()
    certificate_service = MagicMock()
    course_report.build_performance = MagicMock(return_value={'percentage': 0.2, 'points': 30, 'duration': 1,
                                                              'learn_points': 2, 'learn_duration': 1})
    course_report.performance_type.return_value = 70
    dispatcher = MagicMock()
    dispatcher.dispatch_course_introduction = MagicMock(return_value=('111', True))
    dispatcher.dispatch_lesson_content = MagicMock(return_value=('444', True))
    dispatcher.dispatch_end_course = MagicMock(return_value=('555', True))
    enrollment_service = MagicMock()
    chat_service = MagicMock()
    firebase_client = MagicMock()
    private_channel_service = MagicMock()
    schedule_admin = service.ScheduleAdminService(
        schedule_service,
        course_report,
        enrollment_service,
        chat_service,
        'www.perdeu.com',
        'secretfoda', dispatcher,
        'www.callback.com',
        firebase_client,
        certificate_service,
        private_channel_service
    )

    phone_br = '5548991234567'
    with freezegun.freeze_time('2020-02-29 09:08:07', tz_offset=-3):
        br_user = utils.create_user(db, phone_br, workspace.id)
        course_br = utils.create_course_finished(db, 'pt-br', workspace.id, br_user.id)
        e_br = utils.create_enrollment(db, br_user.id, course_br.id, workspace.id, 'America/Sao_Paulo')
        schedule_service.create_schedules(e_br)

    # 15 seconds later
    phone_co = '5548991234576'
    with freezegun.freeze_time('2020-02-29 09:08:15', tz_offset=-5):
        co_user = utils.create_user(db, phone_co, workspace.id)
        course_co = utils.create_course_finished(db, 'es', workspace.id, co_user.id)
        e_co = utils.create_enrollment(db, co_user.id, course_co.id, workspace.id, 'America/Bogota')
        schedule_service.create_schedules(e_co)

    schedules_expectation = {
        '2020-02-29 09:11:15': {
            'dispatch_course_introduction': [phone_br, phone_co]
        },
        # send: lesson 1, intro & content 1, morning - pt-br
        '2020-03-01 12:01:00': {
            'dispatch_lesson_content': [phone_br]
        },
        # nothing here
        '2020-03-01 12:16:00': {},
        # send: lesson 1, intro & content 1, morning - es
        '2020-03-01 14:01:00': {
            'dispatch_lesson_content': [phone_co]
        },
        # send: lesson 1, content 2, night - pt-br, es
        '2020-03-03 00:00:01': {
            'dispatch_lesson_content': [phone_br, phone_co]
        },
        # send: lesson 1, content 3, morning - pt-br, es
        '2020-03-04 14:00:01': {
            'dispatch_lesson_content': [phone_br, phone_co]
        },
        # send: (lesson 2, content 1, afternoon - pt-br) and (lesson 2 introduction, afternoon - es)
        '2020-03-05 18:45:01': {
            'dispatch_lesson_content': [phone_br]
        },
        # send: lesson 2, content 1, afternoon - es
        '2020-03-05 19:00:01': {
            'dispatch_lesson_content': [phone_co]
        },
        # send: (lesson 2, content 2 & 3, morning & night - pt-br, es) and (lesson 3, intro & content 1 - pt-br)
        '2020-03-07 12:00:01': {
            'dispatch_lesson_content': [phone_br, phone_co, phone_br, phone_co, phone_br]
        },
        # send: lesson 3, content 1, morning - es
        '2020-03-07 14:00:01': {
            'dispatch_lesson_content': [phone_co]
        },
        # send: lesson 3, content 2, afternoon - pt-br
        '2020-03-07 17:00:01': {
            'dispatch_lesson_content': [phone_br]
        },
        # send: lesson 3, content 2, afternoon - es
        '2020-03-07 19:00:01': {
            'dispatch_lesson_content': [phone_co]
        },
        # send: lesson 3, content 3, night - ptr-br, es
        '2020-03-08 00:00:01': {
            'dispatch_lesson_content': [phone_br, phone_co]
        },
        # send: end course, morning - ptr-br
        '2020-03-09 12:00:01': {
            'dispatch_end_course': [phone_br]
        },
        # send: end course, morning - ptr-co
        '2020-03-09 14:00:01': {
            'dispatch_end_course': [phone_co]
        }
    }
    calls = [
        'dispatch_course_introduction',
        'dispatch_lesson_content',
        'dispatch_end_course'
    ]
    for d, exp in schedules_expectation.items():
        with freezegun.freeze_time(d):
            pending_schedules = schedule_service.load_pending_schedules(1, 10000)
            for schedule in pending_schedules:
                schedule_admin.dispatch(schedule.id)

            for c in calls:
                phones = exp.get(c, [])
                dispatcher_m = getattr(dispatcher, c)
                assert dispatcher_m
                assert dispatcher_m.call_count == len(phones)
                for i, phone in enumerate(phones):
                    assert phone == dispatcher_m.call_args_list[i][0][1]
            dispatcher.reset_mock()

    assert enrollment_service.complete_course.call_count == 2


def test_dispatch_message_error_schedules_must_be_status_error(db, workspace, schedule_service):
    course_report = MagicMock()
    course_report.build_text_performance_report = MagicMock(return_value=('Boitata', {'percentage': 0.2, 'points': 30}))
    dispatcher = MagicMock()
    dispatcher.dispatch_course_introduction = MagicMock(return_value=('wrong number', False))
    enrollment_service = MagicMock()
    chat_service = MagicMock()
    firebase_client = MagicMock()
    private_channel_service = MagicMock()
    certificate_template = MagicMock()
    schedule_admin = service.ScheduleAdminService(
        schedule_service,
        course_report,
        enrollment_service,
        chat_service,
        'www.perdeu.com',
        'secretfoda', dispatcher,
        'www.callback.com',
        firebase_client,
        certificate_template,
        private_channel_service
    )

    phone_br = '5548991234567'
    with freezegun.freeze_time('2020-02-29 09:08:07', tz_offset=-3):
        br_user = utils.create_user(db, phone_br, workspace.id)
        course_br = utils.create_course_finished(db, 'pt-br', workspace.id, br_user.id)
        _ = utils.create_enrollment(db, br_user.id, course_br.id, workspace.id, 'America/Sao_Paulo')

    with freezegun.freeze_time('2020-02-29 09:11:07'):
        pending_schedules = schedule_service.load_pending_schedules(1, 10000)
        for schedule in pending_schedules:
            schedule_admin.dispatch(schedule.id)
        assert dispatcher.dispatch_course_introduction.called

        assert phone_br == dispatcher.dispatch_course_introduction.call_args_list[0][0][1]
        dispatcher.reset_mock()

    my_schedules = schedule_service.load_all()
    assert len(my_schedules) == 1
    for schedule in my_schedules:
        if schedule.type == model.ScheduleType.course_introduction:
            assert schedule.status == model.ScheduleStatus.error
            assert schedule.message_id == 'wrong number'

    my_schedules = schedule_service.load_all()
    assert len(my_schedules) == 1
    for schedule in my_schedules:
        assert schedule.status == model.ScheduleStatus.error
        assert schedule.message_id == 'wrong number'


def test_schedule_service_anticipate_content(db, workspace, schedule_service, schedule_admin, mocker):
    mocker.patch(UPDATE_ENROLLMENT_PROGRESS_PATH)
    enrollment = utils.create_course_and_accept_enrollment(db, workspace, '5548991234567', 'pt-br', 'America/Sao_Paulo',
                                                           datetime.datetime(2020, 2, 29, 9, 8, 7), -3, schedule_admin)

    # 2 minutes later - anticipate content
    with freezegun.freeze_time('2020-02-29 09:25:07', tz_offset=-3):
        status, schedule_id = schedule_service.anticipate_next_content(enrollment.id)
        assert status == 'anticipated'
        assert schedule_id
        pending_schedules = schedule_service.load_pending_schedules(1, 99)
        assert len(pending_schedules) == 1
        content_schedule = pending_schedules[0]
        assert content_schedule.type == model.ScheduleType.lesson_content
        assert content_schedule.anticipated
        schedule_admin.dispatch(content_schedule.id)


def test_schedule_service_drop_out_enrrolment(db, workspace, schedule_service, schedule_admin, mocker):
    enrollment = utils.create_course_and_accept_enrollment(
        db,
        workspace,
        '5548991234567',
        'pt-br',
        'America/Sao_Paulo',
        datetime.datetime(2020, 2, 29, 9, 8, 7), -3,
        schedule_admin
    )
    schedule_service.give_up_enrollment(enrollment.id)
    enrollment.status = model.EnrollmentStatus.dropout


def test_schedule_service_anticipate_last_content(db, workspace, schedule_service, schedule_admin, mocker):
    schedule_admin.course_report_service.performance_type.return_value = 100
    mocker.patch(UPDATE_ENROLLMENT_PROGRESS_PATH)
    enrollment = utils.create_course_and_accept_enrollment(db, workspace, '5548991234567', 'pt-br', 'America/Sao_Paulo',
                                                           datetime.datetime(2020, 2, 29, 9, 8, 7), -3, schedule_admin)

    # send all other except the last content
    with freezegun.freeze_time('2020-03-07 19:00:01', tz_offset=-3):
        pending_schedules = schedule_service.load_pending_schedules(1, 99)
        assert len(pending_schedules) == 8
        for schedule in pending_schedules:
            assert not schedule.anticipated
            schedule_admin.dispatch(schedule.id)

    # 10 minutes later - anticipate content
    with freezegun.freeze_time('2020-03-07 19:10:01', tz_offset=-3):
        status, schedule_id = schedule_service.anticipate_next_content(enrollment.id)
        assert status == 'anticipated'
        assert schedule_id

    # 5 minutes later
    with freezegun.freeze_time('2020-03-07 19:15:01', tz_offset=-3):
        pending_schedules = schedule_service.load_pending_schedules(1, 99)
        assert len(pending_schedules) == 1
        content_schedule = pending_schedules[0]
        assert content_schedule.anticipated
        assert content_schedule.type == model.ScheduleType.lesson_content
        schedule_admin.dispatch(content_schedule.id)

    # 2 days later (end course)
    with freezegun.freeze_time('2020-03-09 19:15:01', tz_offset=-3):
        pending_schedules = schedule_service.load_pending_schedules(1, 99)
        assert len(pending_schedules) == 1
        end_schedule = pending_schedules[0]
        assert not end_schedule.anticipated
        assert end_schedule.type == model.ScheduleType.course_end
        assert end_schedule.send_date == datetime.datetime(2020, 3, 9, 12, 0, 0)
        schedule_admin.dispatch(end_schedule.id)


def test_schedule_service_not_anticipate_content_already_anticipated(db, workspace, schedule_service, schedule_admin):
    enrollment = utils.create_course_and_accept_enrollment(db, workspace, '5548991234567', 'pt-br', 'America/Sao_Paulo',
                                                           datetime.datetime(2020, 2, 29, 9, 8, 7), -3, schedule_admin)

    # 2 minutes later - anticipate content
    with freezegun.freeze_time('2020-02-29 09:25:07', tz_offset=-3):
        status, schedule_id = schedule_service.anticipate_next_content(enrollment.id)
        assert status == 'anticipated'
        assert schedule_id
        pending_schedules = schedule_service.load_pending_schedules(1, 99)
        assert len(pending_schedules) == 1
        content_schedule = pending_schedules[0]
        assert content_schedule.type == model.ScheduleType.lesson_content
        assert content_schedule.anticipated
        assert content_schedule.send_date == datetime.datetime(2020, 2, 29, 9, 25, 7)

    # 5 minutes later - anticipate content
    with freezegun.freeze_time('2020-02-29 09:30:07', tz_offset=-3):
        status, schedule_id = schedule_service.anticipate_next_content(enrollment.id)
        assert status == 'nothing'
        assert not schedule_id
        pending_schedules = schedule_service.load_pending_schedules(1, 99)
        assert len(pending_schedules) == 1
        content_schedule = pending_schedules[0]
        assert content_schedule.type == model.ScheduleType.lesson_content
        assert content_schedule.anticipated
        assert content_schedule.send_date == datetime.datetime(2020, 2, 29, 9, 25, 7)


def test_schedule_service_anticipate_content_if_is_lesson_content(db, workspace, schedule_service, schedule_admin, mocker):
    mocker.patch(UPDATE_ENROLLMENT_PROGRESS_PATH)
    enrollment = utils.create_course_and_accept_enrollment(db, workspace, '5548991234567', 'pt-br', 'America/Sao_Paulo',
                                                           datetime.datetime(2020, 2, 29, 9, 8, 7), -3, schedule_admin)

    # send all other except the last end course
    with freezegun.freeze_time('2020-03-08 19:00:01', tz_offset=-3):
        pending_schedules = schedule_service.load_pending_schedules(1, 99)
        assert len(pending_schedules) == 9
        for schedule in pending_schedules:
            assert not schedule.anticipated
            schedule_admin.dispatch(schedule.id)
        status, schedule_id = schedule_service.anticipate_next_content(enrollment.id)
        assert status == 'waiting_end'
        assert not schedule_id

    # 10 minutes later... nothing anticipated
    with freezegun.freeze_time('2020-03-08 19:10:01', tz_offset=-3):
        pending_schedules = schedule_service.load_pending_schedules(1, 99)
        assert not len(pending_schedules)


def test_schedule_service_anticipate_content_already_scheduled(db, workspace, schedule_service, schedule_admin):
    enrollment = utils.create_course_and_accept_enrollment(db, workspace, '5548991234567', 'pt-br', 'America/Sao_Paulo',
                                                           datetime.datetime(2020, 2, 29, 9, 8, 7), -3, schedule_admin)
    with freezegun.freeze_time('2020-03-01 12:10:00', tz_offset=-3):
        pending_schedules = schedule_service.load_pending_schedules(1, 99)
        assert len(pending_schedules) == 1
        status, schedule_id = schedule_service.anticipate_next_content(enrollment.id)
        assert status == 'anticipated'
        assert schedule_id
        pending_schedules = schedule_service.load_pending_schedules(1, 99)
        assert len(pending_schedules) == 1

        # the content is already schedule to 2020-03-01 12:00:00
        content_schedule = pending_schedules[0]
        assert content_schedule.type == model.ScheduleType.lesson_content
        assert content_schedule.anticipated
        assert content_schedule.send_date == datetime.datetime(2020, 3, 1, 12, 0, 0)


def test_re_schedule_content(db, workspace, schedule_service):
    with freezegun.freeze_time('2020-02-27 09:08:00', tz_offset=-3):
        workspace2 = utils.create_workspace(db)
        user = utils.create_user(db, '5548991234567', workspace.id)
        user2 = utils.create_user(db, '5548997654321', workspace2.id)
        course = utils.create_course_finished(db, 'pt-br', workspace2.id, user2.id)
        course_service = service.CourseService(db, workspace.id, user.id)
        course_service.add(course)

    with freezegun.freeze_time('2020-02-29 09:00:00', tz_offset=-3):
        enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')

    with freezegun.freeze_time('2020-03-03 10:00:00', tz_offset=-3):
        enrollment2 = utils.create_enrollment(db, user2.id, course.id, workspace2.id, 'America/Sao_Paulo')

    # accept enrollments
    enrollment_service = service.EnrollmentService(db, schedule_service)
    enrollment_service.accept_disclaimer(enrollment)
    enrollment_service.accept_disclaimer(enrollment2)

    content = course.lessons[0].contents[2]
    content_schedule = db.query(model.Schedule).filter(model.Schedule.reference_id == content.id,
                                                       model.Schedule.workspace_id == workspace.id).first()
    end_schedule = db.query(model.Schedule).filter(model.Schedule.enrollment_id == enrollment.id,
                                                   model.Schedule.type == model.ScheduleType.course_end).first()
    # default schedule 4 days after enrollment
    assert content_schedule.send_date == datetime.datetime(2020, 3, 4, 12, 0, 0)
    # default schedule 9 days after enrollment
    assert end_schedule.send_date == datetime.datetime(2020, 3, 9, 12, 0, 0)

    # re-schedule
    schedule_service.re_schedule_content(content, datetime.datetime(2020, 3, 12, 12, 0, 0), model.ContentPeriod.night)

    content_schedule = db.query(model.Schedule).filter(model.Schedule.reference_id == content.id,
                                                       model.Schedule.workspace_id == workspace.id).first()
    end_schedule = db.query(model.Schedule).filter(model.Schedule.enrollment_id == enrollment.id,
                                                   model.Schedule.type == model.ScheduleType.course_end).first()
    assert content_schedule.send_date == datetime.datetime(2020, 3, 12, 22, 0, 0)
    assert end_schedule.send_date == datetime.datetime(2020, 3, 14, 12, 0, 0)


def test_schedules_registry_chat(db, workspace, schedule_service, schedule_admin, mocker):
    schedule_admin.course_report_service.performance_type.return_value = 100
    mocker.patch(UPDATE_ENROLLMENT_PROGRESS_PATH)
    _ = utils.create_course_and_accept_enrollment(db, workspace, '5548991234567', 'pt-br', 'America/Sao_Paulo',
                                                  datetime.datetime(2020, 2, 29, 9, 8, 7), -3, schedule_admin)
    with freezegun.freeze_time('2020-03-12 12:10:00', tz_offset=-3):
        pending_schedules = schedule_service.load_pending_schedules(1, 99)

        chat_types = {
            model.ScheduleType.course_introduction: model.ChatType.course_introduction,
            model.ScheduleType.lesson_content: model.ChatType.lesson_content,
            model.ScheduleType.course_end: model.ChatType.course_end
        }
        expected_chats = [(model.ChatType.course_introduction, '')]
        for s in pending_schedules:
            schedule_admin.dispatch(s.id)
            expected_chats.append((chat_types[s.type], s.reference_id))

        chats = db.query(model.Chat).order_by(model.Chat.created.asc()).all()
        for i, chat in enumerate(chats):
            c_type, ref_id = expected_chats[i]
            assert chat.type == c_type
            if chat.type == model.ChatType.lesson_content:
                assert chat.content_id == ref_id


def test_schedule_service_anticipate_finish_course_all_cases(db, workspace, schedule_service, schedule_admin, mocker):
    schedule_admin.course_report_service.performance_type.return_value = 100
    mocker.patch(UPDATE_ENROLLMENT_PROGRESS_PATH)
    enrollment = utils.create_course_and_accept_enrollment(db, workspace, '5548991234567', 'pt-br', 'America/Sao_Paulo',
                                                           datetime.datetime(2020, 2, 29, 9, 8, 7), -3, schedule_admin)
    # send all other except the course end
    with freezegun.freeze_time('2020-03-08 00:00:01', tz_offset=-3):
        pending_schedules = schedule_service.load_pending_schedules(1, 99)
        assert len(pending_schedules) == 9
        for i in range(8):
            schedule = pending_schedules[i]
            assert not schedule.anticipated
            schedule_admin.dispatch(schedule.id)

        # still have contents to send
        status, schedule_id = schedule_service.anticipate_course_end(enrollment.id)
        assert status == 'content_pending'
        assert not schedule_id

        # send the last content
        schedule = pending_schedules[8]
        assert not schedule.anticipated
        schedule_admin.dispatch(schedule.id)
        status, schedule_id = schedule_service.anticipate_course_end(enrollment.id)
        assert status == 'anticipated'
        assert schedule_id

    with freezegun.freeze_time('2020-03-08 00:01:01', tz_offset=-3):
        status, schedule_id = schedule_service.anticipate_course_end(enrollment.id)
        assert status == 'nothing'
        assert not schedule_id

    # send the course end
    with freezegun.freeze_time('2020-03-08 00:02:01', tz_offset=-3):
        pending_schedules = schedule_service.load_pending_schedules(1, 99)
        assert len(pending_schedules) == 1
        schedule = pending_schedules[0]
        schedule_admin.dispatch(schedule.id)
        status, schedule_id = schedule_service.anticipate_course_end(enrollment.id)
        assert status == 'nothing'
        assert not schedule_id


def test_dispatching_empty_content_should_use_the_content_name(db, workspace, course_service, schedule_service, mocker):
    mocker.patch(UPDATE_ENROLLMENT_PROGRESS_PATH)
    course_report = MagicMock()
    course_report.build_performance = MagicMock(return_value={'percentage': 0.2, 'points': 30, 'duration': 1,
                                                              'learn_points': 2, 'learn_duration': 1})
    course_report.performance_type.return_value = 100
    dispatcher = MagicMock()
    dispatcher.dispatch_course_introduction = MagicMock(return_value=('111', True))
    dispatcher.dispatch_end_course = MagicMock(return_value=('555', True))
    enrollment_service = MagicMock()
    chat_service = MagicMock()
    firebase_client = MagicMock()
    private_channel_service = MagicMock()
    certificate_template = MagicMock()
    schedule_admin = service.ScheduleAdminService(
        schedule_service,
        course_report,
        enrollment_service,
        chat_service,
        'www.perdeu.com',
        'secretfoda',
        dispatcher,
        'www.callback.com',
        firebase_client,
        certificate_template,
        private_channel_service
    )
    enrollment = utils.create_course_and_accept_enrollment(db, workspace, '5548991234567', 'pt-br', 'America/Sao_Paulo',
                                                           datetime.datetime(2020, 2, 29, 9, 8, 7), -3, schedule_admin)

    # Set all descriptions to None
    lessons, _ = course_service.load_all_lessons(enrollment.course_id, {})
    for lesson in lessons:
        for c in lesson.contents:
            c.description = None
    db.commit()

    # fake content dispatching, Name and Description must be equals
    def _name_and_content_equals(lang, phone, user_name, lesson_name, content_name, content_description,
                                 anticipated, content_link, content_image_url, callback_url, content_days,
                                 private_channel, allow_anticipation, allow_drop_out):
        assert content_name == content_description
        return '444', True
    dispatcher.dispatch_lesson_content.side_effect = _name_and_content_equals

    # dispatch pending messages
    with freezegun.freeze_time('2020-03-12 12:10:00', tz_offset=-3):
        pending_schedules = schedule_service.load_pending_schedules(1, 10000)
        for schedule in pending_schedules:
            schedule_admin.dispatch(schedule.id)


def test_calculate_enrollment_progress(db, workspace, schedule_service, schedule_admin, enrollment_service, mocker):
    mocker.patch(UPDATE_ENROLLMENT_PROGRESS_PATH)
    enrollment = utils.create_course_and_accept_enrollment(db, workspace, '5548991234567', 'pt-br', 'America/Sao_Paulo',
                                                           datetime.datetime(2020, 2, 29, 9, 8, 7), -3, schedule_admin)
    with freezegun.freeze_time('2020-03-03 00:00:01', tz_offset=-3):
        utils.dispatch_schedules(schedule_service, schedule_admin)

    enrollment = enrollment_service.update_progress(enrollment.id)
    assert enrollment.progress == 22

    with freezegun.freeze_time('2020-03-08 00:00:01', tz_offset=-3):
        utils.dispatch_schedules(schedule_service, schedule_admin)

    enrollment = enrollment_service.update_progress(enrollment.id)
    assert enrollment.progress == 100


def test_should_notify_schedule_sent(db, workspace, schedule_service, schedule_admin, mocker):
    mocker.patch(UPDATE_ENROLLMENT_PROGRESS_PATH)
    schedule_admin.course_report_service.performance_type.return_value = 100
    notifies = []

    @service.schedule_sent_signal.on('sent')
    def handler(schedule):
        notifies.append(1 if schedule.type == model.ScheduleType.lesson_content else 0)

    utils.create_course_and_accept_enrollment(db, workspace, '5548991234567', 'pt-br', 'America/Sao_Paulo',
                                              datetime.datetime(2020, 2, 29, 9, 8, 7), -3, schedule_admin)

    with freezegun.freeze_time('2020-03-03 00:00:01', tz_offset=-3):
        utils.dispatch_schedules(schedule_service, schedule_admin)

    assert len(notifies) == 3
    assert sum(notifies) == 2

    with freezegun.freeze_time('2020-03-12 00:00:01', tz_offset=-3):
        utils.dispatch_schedules(schedule_service, schedule_admin)

    assert len(notifies) == 11
    assert sum(notifies) == 9


def test_resend_content(db, workspace, schedule_service, schedule_admin, mocker):
    mocker.patch(UPDATE_ENROLLMENT_PROGRESS_PATH)
    enrollment = utils.create_course_and_accept_enrollment(db, workspace, '5548991234567', 'pt-br', 'America/Sao_Paulo',
                                                           datetime.datetime(2020, 2, 29, 9, 8, 7), -3, schedule_admin)
    with freezegun.freeze_time('2020-03-03 00:00:01', tz_offset=-3):
        utils.dispatch_schedules(schedule_service, schedule_admin)

        schedules = db.query(model.Schedule).filter(model.Schedule.enrollment_id == enrollment.id).filter(
            model.Schedule.type == model.ScheduleType.lesson_content).all()
        assert schedules

        current_count = len(schedules)
        schedule = schedules[0]
        assert schedule.status == model.ScheduleStatus.sent

        new_schedule = schedule_service.resend_content(enrollment.id, schedule.reference_id)

        schedules = db.query(model.Schedule).filter(model.Schedule.enrollment_id == enrollment.id).filter(
            model.Schedule.type == model.ScheduleType.lesson_content).all()
        assert len(schedules) == (current_count + 1)

        new_schedule = db.query(model.Schedule).filter_by(id=new_schedule.id).first()
        assert new_schedule.status == model.ScheduleStatus.pending
        assert new_schedule.send_date == datetime.datetime(2020, 3, 3, 0, 0, 1)


def test_resend_throws_not_found(db, workspace, schedule_service, schedule_admin, mocker):
    mocker.patch(UPDATE_ENROLLMENT_PROGRESS_PATH)
    enrollment = utils.create_course_and_accept_enrollment(db, workspace, '5548991234567', 'pt-br', 'America/Sao_Paulo',
                                                           datetime.datetime(2020, 2, 29, 9, 8, 7), -3, schedule_admin)
    with freezegun.freeze_time('2020-03-03 00:00:01', tz_offset=-3):
        utils.dispatch_schedules(schedule_service, schedule_admin)

        with pytest.raises(domain.exceptions.service_exceptions.NotFoundException):
            schedule_service.resend_content(enrollment.id, str(uuid.uuid4()))


def test_dispatching_end_course_must_generate_certificate():
    course_report = MagicMock()
    course_report.build_performance = MagicMock(return_value={'percentage': 0.5, 'points': 30, 'duration': 1,
                                                              'learn_points': 2, 'learn_duration': 1})
    course_report.performance_type.return_value = 100
    schedule = model.Schedule(enrollment_id='my-enrollment-id', workspace_id='my-workspace-id')
    schedule.type = model.ScheduleType.course_end
    schedule.status = model.ScheduleStatus.pending
    schedule_service = MagicMock()
    private_channel_service = MagicMock()
    schedule_service.load.return_value = schedule
    schedule_service.load_model.return_value = MagicMock()
    schedule_service.load_enrollment_and_user.return_value = MagicMock(), MagicMock()

    enrollment_service = MagicMock()
    dispatcher = MagicMock()
    certificate_template = MagicMock()
    dispatcher.dispatch_end_course.return_value = None, False
    with mock.patch('domain.service.ScheduleAdminService._local_date'):
        schedule_admin = service.ScheduleAdminService(
            schedule_service,
            course_report,
            enrollment_service,
            MagicMock(),
            'www.site.com',
            'secret', dispatcher,
            None,
            MagicMock(),
            certificate_template,
            private_channel_service
        )
        schedule_admin.dispatch('my-schedule-id')
        certificate_template.generate_certificate.assert_called_once()
        enrollment_service.update_certificate_url.assert_called_once()


def test_dispatch_end_course_send_certificate_if_chat_in_24_window(db, workspace, schedule_service, schedule_admin,
                                                                   dispatcher, mocker, certificate_service_mock):
    mocker.patch(UPDATE_ENROLLMENT_PROGRESS_PATH)
    certificate_service_mock.generate_certificate.return_value = 'my-certificate-url'
    schedule_admin.course_report_service.performance_type.return_value = 100
    _ = utils.create_course_and_accept_enrollment(db, workspace, '5548991234567', 'pt-br', 'America/Sao_Paulo',
                                                  datetime.datetime(2020, 2, 29, 9, 8, 7), -3, schedule_admin)
    with freezegun.freeze_time('2020-03-08 20:00:01', tz_offset=-3):
        utils.dispatch_schedules(schedule_service, schedule_admin)

    # course end
    with freezegun.freeze_time('2020-03-09 19:15:01', tz_offset=-3):
        utils.dispatch_schedules(schedule_service, schedule_admin)
        assert dispatcher.dispatch_end_course.call_args_list[0][0][11] == 'my-certificate-url'


def test_dispatch_end_course_without_certificate_to_enrollment_with_low_performance(
        db, workspace, schedule_service, schedule_admin, dispatcher, mocker
):
    schedule_admin.course_report_service.performance_type.return_value = 70
    mocker.patch(UPDATE_ENROLLMENT_PROGRESS_PATH)
    _ = utils.create_course_and_accept_enrollment(db, workspace, '5548991234567', 'pt-br', 'America/Sao_Paulo',
                                                  datetime.datetime(2020, 2, 29, 9, 8, 7), -3, schedule_admin)
    with freezegun.freeze_time('2020-03-08 20:00:01', tz_offset=-3):
        utils.dispatch_schedules(schedule_service, schedule_admin)

    # course end
    with freezegun.freeze_time('2020-03-09 19:15:01', tz_offset=-3):
        utils.dispatch_schedules(schedule_service, schedule_admin)
        assert dispatcher.dispatch_end_course.call_args_list[0][0][11] == None


def test_dispatch_end_course_not_send_certificate_outside_24_window(db, workspace, schedule_service, schedule_admin,
                                                                    dispatcher, mocker):
    mocker.patch(UPDATE_ENROLLMENT_PROGRESS_PATH)
    schedule_admin.course_report_service.performance_type.return_value = 100
    _ = utils.create_course_and_accept_enrollment(db, workspace, '5548991234567', 'pt-br', 'America/Sao_Paulo',
                                                  datetime.datetime(2020, 2, 29, 9, 8, 7), -3, schedule_admin)
    with freezegun.freeze_time('2020-03-08 19:15:01', tz_offset=-3):
        utils.dispatch_schedules(schedule_service, schedule_admin)

    # course end
    with freezegun.freeze_time('2020-03-09 19:15:01', tz_offset=-3):
        utils.dispatch_schedules(schedule_service, schedule_admin)
        assert not dispatcher.dispatch_end_course.call_args_list[0][0][11]


def test_create_schedule_delete_enrollment_set_enrollment_id_null(db, workspace, schedule_service, schedule_admin, dispatcher, mocker):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')

    my_schedules = schedule_service.load_all()
    assert len(my_schedules) == 1

    schedule_service.create_schedules(enrollment)

    db.delete(enrollment)
    db.commit()

    my_schedules = schedule_service.load_all()
    for schedule in my_schedules:
        assert schedule.enrollment_id is None

def test_update_schedule_status(db, workspace, schedule_service):
    user = utils.create_user(db, '5548991234567', workspace.id)
    course = utils.create_course_finished(db, 'pt-br', workspace.id, user.id)
    enrollment = utils.create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo')

    with freezegun.freeze_time('2020-02-29 09:08:07', tz_offset=-3):
        schedule = schedule_service._create_schedule(
            enrollment=enrollment,
            schedule_type=model.ScheduleType.course_introduction,
            reference_id=course.id,
            send_date=datetime.datetime.utcnow(),
            lang='pt-br',
            tmz='America/Sao_Paulo'
        )
        db.add(schedule)
        db.commit()

    original_schedule_id = schedule.id
    schedule_service.update_schedule_status(schedule, "dispatch-id-123", success=True)
    
    updated_schedule = db.query(model.Schedule).filter_by(id=original_schedule_id).first()
    assert updated_schedule.message_id == "dispatch-id-123"
    assert updated_schedule.status == model.ScheduleStatus.sent

    schedule.status = model.ScheduleStatus.pending
    schedule.message_id = None
    db.commit()
    
    schedule_service.update_schedule_status(schedule, "dispatch-error-456", success=False)
    
    updated_schedule = db.query(model.Schedule).filter_by(id=original_schedule_id).first()
    assert updated_schedule.message_id == "dispatch-error-456"
    assert updated_schedule.status == model.ScheduleStatus.error

    schedule.status = model.ScheduleStatus.pending
    schedule.message_id = None
    db.commit()
    
    schedule_service.db.commit = MagicMock()
    schedule_service.update_schedule_status(schedule, None, success=True)
    
    updated_schedule = db.query(model.Schedule).filter_by(id=original_schedule_id).first()
    assert updated_schedule.message_id is None
    assert updated_schedule.status == model.ScheduleStatus.pending
    schedule_service.db.commit.assert_not_called()

    schedule_service.db.commit = db.commit