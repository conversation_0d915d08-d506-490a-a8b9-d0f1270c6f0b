import unittest
from unittest.mock import MagicMock, patch
from datetime import datetime
from domain.model import ScheduleStatus, ScheduleType, Content, Enrollment, User, Workspace, Course, Schedule
from domain.client import KontentClient
from domain.service import EnrollmentService, ScheduleService, ScheduleAdminService
from domain.services.content_delivery_service import ContentMessageDataService
from domain.services.learn_content_validation_service import LearnContentValidationService


class TestContentMessageDataService(unittest.TestCase):
    def setUp(self):
        self.enrollment_service = MagicMock(spec=EnrollmentService)
        self.schedule_service = MagicMock(spec=ScheduleService)
        self.schedule_admin_service = MagicMock(spec=ScheduleAdminService)
        self.kontent_client = MagicMock(spec=KontentClient)
        self.learn_content_validation_service = MagicMock(spec=LearnContentValidationService)
        self.database_session = MagicMock()

        self.service = ContentMessageDataService(
            enrollment_service=self.enrollment_service,
            schedule_service=self.schedule_service,
            schedule_admin_service=self.schedule_admin_service,
            kontent_client=self.kontent_client,
            learn_content_validation_service=self.learn_content_validation_service,
            database_session=self.database_session,
        )

    @patch("datetime.datetime")
    def test_get_next_content_data_embed_content_valid_for_whatsapp(self, mock_datetime):
        phone = "1234567890"
        execution_sid = "test_execution_sid"
        mock_datetime.now.return_value = datetime(2024, 12, 18)

        enrollment = self._build_mock_enrollment(embed_enabled=True)
        schedule = self._build_mock_schedule(enrollment.id)

        content = MagicMock(spec=Content)
        content.id = 101
        content.name = "Sample Content"
        content.description = "Sample Description"
        content.learn_content = "learn_content_id"
        content.type.image_url = "https://example.com/image.jpg"

        self.enrollment_service.load_first_active_from_phone.return_value = enrollment
        self.schedule_service.get_next_schedule.return_value = schedule
        self.schedule_service.load_model.return_value = content
        self.kontent_client.load_content.return_value = {
            "url": "https://cdn.example.com/valid_content.mp4",
            "is_valid_whatsapp_content": True
        }

        content_data, status = self.service.get_next_content_data(phone, execution_sid)

        self.assertEqual(status, "anticipated")
        self.assertTrue(content_data["is_content_embed"])
        self.assertEqual(content_data["content_link"], "https://cdn.example.com/valid_content.mp4")
        self.assertEqual(content_data["user_phone"], "1234567890")
        self.kontent_client.load_content.assert_called_once_with(content.learn_content)

    @patch("datetime.datetime")
    def test_get_next_content_data_embed_content_invalid_for_whatsapp(self, mock_datetime):
        phone = "1234567890"
        execution_sid = "test_execution_sid"
        mock_datetime.now.return_value = datetime(2024, 12, 18)
        self.learn_content_validation_service.is_valid_whatsapp_content.return_value = False

        enrollment = self._build_mock_enrollment(embed_enabled=True)
        schedule = self._build_mock_schedule(enrollment.id)

        content = MagicMock(spec=Content)
        content.id = 101
        content.name = "Sample Content"
        content.description = "Sample Description"
        content.learn_content = "learn_content_id"
        content.type.image_url = "https://example.com/image.jpg"

        self.enrollment_service.load_first_active_from_phone.return_value = enrollment
        self.schedule_service.get_next_schedule.return_value = schedule
        self.schedule_service.load_model.return_value = content
        self.kontent_client.load_content.return_value = {
            "url": "https://cdn.example.com/invalid_content.mp4",
        }
        self.schedule_admin_service.generate_content_url.return_value = "https://smartzap.com/custom_link"

        content_data, status = self.service.get_next_content_data(phone, execution_sid)

        self.assertEqual(status, "anticipated")
        self.assertFalse(content_data["is_content_embed"])
        self.assertEqual(content_data["content_link"], "https://smartzap.com/custom_link")
        self.schedule_admin_service.generate_content_url.assert_called_once()

    def test_get_next_content_data_with_no_enrollment(self):
        phone = "1234567890"
        self.enrollment_service.load_first_active_from_phone.return_value = None

        content_data, status = self.service.get_next_content_data(phone)

        self.assertEqual(status, "no_enrollment")
        self.assertEqual(content_data, {})

    def test_get_next_content_data_with_no_schedule(self):
        phone = "1234567890"
        enrollment = MagicMock()
        enrollment.id = 1

        self.enrollment_service.load_first_active_from_phone.return_value = enrollment
        self.schedule_service.get_next_schedule.return_value = None

        content_data, status = self.service.get_next_content_data(phone)

        self.assertEqual(status, "any_message_scheduled")
        self.assertEqual(content_data, {})

    def test_get_next_content_data_with_course_end_schedule(self):
        phone = "1234567890"
        enrollment = MagicMock()
        enrollment.id = 1

        schedule = MagicMock()
        schedule.type = ScheduleType.course_end

        self.enrollment_service.load_first_active_from_phone.return_value = enrollment
        self.schedule_service.get_next_schedule.return_value = schedule

        content_data, status = self.service.get_next_content_data(phone)

        self.assertEqual(status, "waiting_end")
        self.assertEqual(content_data, {})

    def _build_mock_enrollment(self, embed_enabled: bool) -> Enrollment:
        """Helper para criar enrollment de teste."""
        enrollment = MagicMock(spec=Enrollment)
        enrollment.id = 1
        enrollment.user = MagicMock(spec=User)
        enrollment.user.phone = "1234567890"
        enrollment.user.name = "Test User"
        enrollment.workspace = MagicMock(spec=Workspace)
        enrollment.workspace.messages_content_embed = embed_enabled
        enrollment.workspace.user_token_expiration = "2024-12-31"
        enrollment.course = MagicMock(spec=Course)
        enrollment.course.allow_content_anticipation = True
        enrollment.course.allow_drop_out = True
        return enrollment

    def _build_mock_schedule(self, enrollment_id: int) -> Schedule:
        """Helper para criar schedule de teste."""
        schedule = MagicMock(spec=Schedule)
        schedule.id = 1
        schedule.lang = "en"
        schedule.enrollment_id = enrollment_id
        schedule.type = ScheduleType.lesson_content
        schedule.reference_id = 101
        schedule.anticipated = False
        schedule.timezone = "UTC"
        schedule.status = ScheduleStatus.pending
        return schedule
