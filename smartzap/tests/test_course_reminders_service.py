import uuid

import pytest
from datetime import datetime, timedelta, timezone
import pytz

from domain.model import Enrollment, Schedule, ScheduleType, ScheduleStatus, EnrollmentStatus
from domain.services.course_reminders_service import CourseRemindersService
from tests import utils


@pytest.fixture
def sample_data(db, workspace, user):
    # Create an enrollment that has been idle
    course = utils.create_course_finished(db, 'es', workspace.id, user.id)
    enrollment = Enrollment(
        id=str(uuid.uuid4()),
        workspace_id=workspace.id,
        user_id=user.id,
        course_id=course.id,
        timezone='America/Sao_Paulo',
        status=EnrollmentStatus.started
    )
    db.add(enrollment)

    # Create an old schedule (lesson content) that marks the enrollment as "idle"
    schedule = Schedule(
        enrollment=enrollment,
        workspace_id=workspace.id,
        type=ScheduleType.lesson_content,
        status=ScheduleStatus.sent,
        send_date=datetime.now(tz=timezone.utc) - timedelta(hours=25),  # More than 23 hours ago
        lang="en",
        timezone=str(pytz.UTC),
    )
    db.add(schedule)

    db.commit()

    return enrollment


@pytest.fixture
def course_reminders_service(db, schedule_service, enrollment_service, schedule_admin):
    return CourseRemindersService(
        db=db,
        schedule_service=schedule_service,
        enrollment_service=enrollment_service
    )


def test_schedule_reminders_for_idle_enrollments(course_reminders_service, db, sample_data, workspace):
    """Test scheduling reminders for idle enrollments with a real database."""
    workspace_id = workspace.id
    enrollment = sample_data

    course_reminders_service.schedule_reminders_for_idle_enrollments(workspace_id)

    # Verify if a new reminder schedule was created
    scheduled_reminder = db.query(Schedule).filter(
        Schedule.type == ScheduleType.course_reminder
    ).first()

    assert scheduled_reminder is not None, "No reminder was scheduled!"
    assert scheduled_reminder.enrollment_id == enrollment.id
    assert scheduled_reminder.type == ScheduleType.course_reminder
    assert scheduled_reminder.status == ScheduleStatus.pending
