import os
from unittest.mock import MagicMock
import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import scoped_session, sessionmaker

from domain import model, service, config
from domain.services.certificate_service import CertificateService
from . import utils


@pytest.fixture
def kontent_map():
    return utils.learn_contents()


@pytest.fixture
def kontent_client(kontent_map):
    def load_content(learn_id):
        contents = kontent_map
        return contents.get(learn_id, {})

    kontent_c = MagicMock()
    kontent_c.load_content = MagicMock(side_effect=load_content)
    return kontent_c


@pytest.fixture
def slack_client():
    return MagicMock()


@pytest.fixture
def templates_folder():
    current_di_path = os.path.dirname(os.path.abspath(__file__))
    templates_path = os.path.join(current_di_path, '../domain/templates')
    return templates_path


@pytest.fixture
def message_dispatcher(templates_folder):
    fake_broker = MagicMock()
    dispatcher = service.MessageDispatcher(fake_broker, templates_folder, 'http://www.img.com/img.jpg')
    return dispatcher


@pytest.fixture(scope='function')
def db():
    engine = create_engine('sqlite://', convert_unicode=True)
    model.Base.metadata.create_all(engine)
    session = scoped_session(sessionmaker(autocommit=False, autoflush=True, bind=engine))
    return session()


@pytest.fixture
def workspace(db):
    workspace = utils.create_workspace(db)
    return workspace


@pytest.fixture
def workspace_with_messages_by_twilio_studio(db):
    return utils.create_workspace(db, messages_by_twilio_studio=True)


@pytest.fixture
def user(db, workspace):
    return utils.create_user(db, '5548991111111', workspace.id)


@pytest.fixture
def workspace_service(db):
    return service.WorkspaceService(db)


@pytest.fixture
def course_service(db, workspace, user):
    return service.CourseService(db, workspace.id, user.id)


@pytest.fixture
def schedule_service(db, workspace):
    return service.ScheduleService(db, workspace.id)


@pytest.fixture
def enrollment_service(db, schedule_service, workspace):
    return service.EnrollmentService(db, schedule_service, workspace.id)


@pytest.fixture
def certificate_client():
    c = MagicMock()
    c.generate_end_course_report.return_value = 'my-certificate'
    return c


@pytest.fixture
def report_service(db, kontent_client, schedule_service, enrollment_service, certificate_client):
    def load_t(identifier):
        return identifier

    i18n = MagicMock()
    i18n.t = MagicMock(side_effect=load_t)
    return service.CourseReportService(schedule_service, enrollment_service, kontent_client, i18n, 50)


@pytest.fixture
def tasks_client():
    return MagicMock()


@pytest.fixture
def twilio_chatter_bot(db, schedule_service, course_service, templates_folder, message_dispatcher, tasks_client):
    enrollment_service = service.EnrollmentService(db, schedule_service)
    chat_service = service.ChatService(db)
    fake_broker = MagicMock()
    answers = config.d.DEFAULT_ANSWERS
    chatter_bot = service.MessageChatterBot(enrollment_service, course_service, schedule_service, chat_service,
                                            fake_broker, templates_folder, message_dispatcher, tasks_client, answers)
    return chatter_bot


@pytest.fixture
def dispatcher():
    d = MagicMock()
    d.dispatch_course_introduction = MagicMock(return_value=('111', True))
    d.dispatch_lesson_content = MagicMock(return_value=('444', True))
    d.dispatch_end_course = MagicMock(return_value=('555', True))
    d.broker_name.return_value = 'broker_test'
    return d


@pytest.fixture
def private_channel_service():
    return MagicMock()


@pytest.fixture
def certificate_service_mock():
    return MagicMock(spec=CertificateService)


@pytest.fixture
def schedule_admin(db, schedule_service, dispatcher, private_channel_service, certificate_service_mock):
    course_report = MagicMock()
    course_report.build_performance = MagicMock(return_value={'percentage': 0.2, 'points': 30, 'duration': 1,
                                                              'learn_points': 2, 'learn_duration': 1})
    course_report.generate_certificate.return_value = 'my-certificate-url'

    firebase_client = MagicMock()
    enrollment_service = MagicMock()
    chat_service = service.ChatService(db)
    default_channel = '{"pt-BR":"Aadsdadsadasda@+551149332548", "es":"Aadsadsadsdadsdsadsad@+554831810468"}'
    private_channel_service = service.PrivateChannelService(model.LanguageChannels(default_channel))
    schedule_admin = service.ScheduleAdminService(
        schedule_service,
        course_report,
        enrollment_service,
        chat_service,
        'www.perdeu.com',
        'secretfoda', dispatcher,
        'www.callback.com',
        firebase_client,
        certificate_service_mock,
        private_channel_service
    )
    return schedule_admin


@pytest.fixture
def notification_service(db, workspace, user):
    return service.NotificationService(db, workspace.id, user.id)


@pytest.fixture
def pdf_encoded():
    return b"""
    JVBERi0xLjQKJcfsj6IKNSAwIG9iago8PAovQ3JlYXRvciAoQWRvYmUgRmlsbGVyIDkuMCkgCi9Q
    cm9kdWNlciAoQWRvYmUgUERGIGxpYnJhcikgCi9DcmVhdGlvbkRhdGUgKEQ6MjAyMTEwMTAxMTAx
    MDErMDAwMCkpCi9Nb2REYXRlIChEOjIwMjExMDEwMTEwMTAxKzAwMDApCj4+CmVuZG9iagoxIDAg
    b2JqCjw8Ci9UeXBlIC9DYXRhbG9nCi9QYWdlcyAyIDAgUgo+PgplbmRvYmoKMiAwIG9iago8PAov
    VHlwZSAvUGFnZXMKL0tpZHMgWzMgMCBSIF0KL01lZGlhQm94IFswIDAgNjEyIDc5Ml0KPj4KZW5k
    b2JqCjMgMCBvYmoKPDwKL1R5cGUgL1BhZ2UKL1BhcmVudCAyIDAgUgovUmVzb3VyY2VzIDw8Ci9G
    b250IDw8Ci9GMSA0IDAgUgo+PgovUHJvY1NldCBbL1BERiAvVGV4dF0KPj4KL0NvbnRlbnRzIDUg
    MCBSCj4+CmVuZG9iago0IDAgb2JqCjw8Ci9UeXBlIC9Gb250Ci9TdWJ0eXBlIC9UeXBlMQovTmFt
    ZSAvRjEKL0Jhc2VGb250IC9IZWx2ZXRpY2EKL0VuY29kaW5nIC9XaW5BbnNpRW5jb2RpbmcKPj4K
    ZW5kb2JqCjUgMCBvYmoKPDwgL0xlbmd0aCAxMDcgPj4Kc3RyZWFtCkJUQyAwIDAgMCAwIDAgMF9U
    cmFucyAwLjUgMC4yIHRkXyB0eGVtXzAgX19uXyAKU29tb3BhdCBIZWxsbywgV29ybGQhCmVuZHN0
    cmVhbQplbmRvYmoKNiAwIG9iago8PAovQXV0aG9yIChQREYgR2VuZXJhdG9yKQovU3ViamVjdCAo
    SGVsbG8gV29ybGQpCj4+CmVuZG9iago3IDAgb2JqCjw8Ci9UeXBlIC9YUmVmCi9XaWR0aCAzCi9S
    b290IDIgMCBSCi9TaXplIDgKPj4KZW5kb2JqCnhyZWYKMAo4CjAwMDAwMDAwMDAgNjU1MzUgZiAK
    MDAwMDAwMDAxNiAwMDAwMCBuIAowMDAwMDAwMDI2IDAwMDAwIG4gCjAwMDAwMDAwNzAgMDAwMDAg
    biAKMDAwMDAwMDA5NCAwMDAwMCBuIAowMDAwMDAwMTI2IDAwMDAwIG4gCnRyYWlsZXIKPDwgL1Np
    emUgOC9Sb290IDIgMCBSL0luZm8gNyAwIFIKPj4Kc3RhcnR4cmVmCjE0NwowCiUlRU9G
    """


@pytest.fixture
def templates_sids():
    templates = {
        "001": {
            "pt-br": {
                "message": "HX1111234123412341234123412341234123412341234"
            },
            "es": {
                "message": "HX52345678912341234123412341234123412341234"
            }
        },
        "002": {
            "pt-br": {
                "message": "HX1111234123412341234123412341234123412341234"
            },
            "es": {
                "message": "HX52345678912341234123412341234123412341234"
            }
        },
        "003": {
            "pt-br": {
                "message": "HX1111234123412341234123412341234123412341234"
            },
            "es": {
                "message": "HX52345678912341234123412341234123412341234"
            }
        }
    }
    return templates
