import datetime
from unittest import TestCase
from unittest.mock import MagicMock, patch, call

from domain import model
from domain.exceptions.service_exceptions import NotFoundException
from domain.model import Course, Workspace, Enrollment, User, Schedule, ScheduleType
from domain.services.message_dispatcher_v2 import MessageDispatcherV2
from domain.services.certificate_service import CertificateService
from domain.services.schedule_admin_service_v2 import (
    ScheduleAdminServiceV2,
    ScheduleHandlerFactory,
    CourseIntroductionHandler,
    CourseEndHandler,
)
from domain.config import Config


class TestScheduleAdminServiceV2(TestCase):
    def setUp(self):
        self.schedule_service = MagicMock()
        self.course_report_service = MagicMock()
        self.enrollment_service = MagicMock()
        self.chat_service = MagicMock()
        self.dispatcher = MagicMock(spec=MessageDispatcherV2)
        self.certificate_service = MagicMock(spec=CertificateService)
        self.handler_factory = MagicMock(spec=ScheduleHandlerFactory)

        self.service = ScheduleAdminServiceV2(
            schedule_service=self.schedule_service,
            course_report_service=self.course_report_service,
            enrollment_service=self.enrollment_service,
            chat_service=self.chat_service,
            dispatcher=self.dispatcher,
            certificate_service=self.certificate_service,
            handler_factory=self.handler_factory
        )

        self.mock_course = MagicMock(spec=Course)
        self.mock_course.name = "Test Course"
        self.mock_course.points = 10.0
        self.mock_course.duration = 300
        self.mock_course.allow_nps_survey = True
        self.mock_course.disable_send_certificate = False

        self.mock_workspace = MagicMock(spec=Workspace)
        self.mock_workspace.name = "Test Workspace"
        self.mock_workspace.private_channel = "#general"
        self.mock_workspace.send_courses_recommendation_message = True

        self.mock_enrollment = MagicMock(spec=Enrollment)
        self.mock_enrollment.workspace = self.mock_workspace
        self.mock_enrollment.course = self.mock_course

        self.mock_user = MagicMock(spec=User)
        self.mock_user.name = "John Doe"
        self.mock_user.phone = "+5511999999999"

    def test_dispatch_course_introduction(self):
        schedule = MagicMock(spec=Schedule)
        schedule.id = "test-schedule"
        schedule.type = ScheduleType.course_introduction
        schedule.status = model.ScheduleStatus.pending
        schedule.reference_id = "course-123"
        schedule.enrollment_id = "enrollment-123"

        mock_handler = MagicMock(spec=CourseIntroductionHandler)
        mock_handler.handle.return_value = "dispatch-id-123"
        self.handler_factory.create_handler.return_value = mock_handler

        self.schedule_service.load.return_value = schedule
        self.schedule_service.load_enrollment_and_user.return_value = (self.mock_enrollment, self.mock_user)
        self.schedule_service.load_model.return_value = self.mock_course

        def update_schedule_status_side_effect(schedule, dispatch_id, success=True):
            if dispatch_id and success:
                schedule.message_id = dispatch_id
                schedule.status = model.ScheduleStatus.sent

        self.schedule_service.update_schedule_status.side_effect = update_schedule_status_side_effect

        self.service.dispatch(schedule.id)

        self.handler_factory.create_handler.assert_called_once_with(ScheduleType.course_introduction)
        mock_handler.handle.assert_called_once_with(schedule, self.mock_enrollment, self.mock_user)
        self.schedule_service.update_schedule_status.assert_called_once_with(schedule, "dispatch-id-123", True)
        self.chat_service.registry_schedule.assert_called_once_with(schedule)
        self.assertEqual(schedule.status, model.ScheduleStatus.sent)
        self.assertEqual(schedule.message_id, "dispatch-id-123")

    def test_dispatch_not_pending_schedule(self):
        schedule = MagicMock(spec=Schedule)
        schedule.id = "test-schedule"
        schedule.type = ScheduleType.course_introduction
        schedule.status = model.ScheduleStatus.sent

        self.schedule_service.load.return_value = schedule

        self.service.dispatch("test-schedule")

        self.handler_factory.create_handler.assert_not_called()
        self.schedule_service.update_schedule_status.assert_not_called()
        self.chat_service.registry_schedule.assert_not_called()

    def test_dispatch_not_found_exception(self):
        self.schedule_service.load.side_effect = NotFoundException("Schedule not found")
        schedule = MagicMock(spec=Schedule)
        schedule.status = model.ScheduleStatus.pending

        with self.assertLogs(level='ERROR') as log:
            self.service.dispatch("invalid-schedule")

        self.handler_factory.create_handler.assert_not_called()
        self.schedule_service.update_schedule_status.assert_not_called()
        self.chat_service.registry_schedule.assert_not_called()
        self.assertIn("Schedule not found", log.output[0])

    @patch('domain.services.schedule_admin_service_v2.CourseEndHandler._local_date')
    def test_dispatch_course_end(self, mock_local_date):
        mock_local_date.return_value = datetime.date(2023, 1, 1)

        schedule = MagicMock(spec=Schedule)
        schedule.id = "test-schedule"
        schedule.type = ScheduleType.course_end
        schedule.status = model.ScheduleStatus.pending
        schedule.reference_id = "course-123"
        schedule.enrollment_id = "enrollment-123"
        schedule.send_date = datetime.datetime(2023, 1, 1, 12, 0, tzinfo=datetime.timezone.utc)
        schedule.timezone = "America/Sao_Paulo"

        mock_handler = MagicMock(spec=CourseEndHandler)
        mock_handler.handle.return_value = "dispatch-id-456"
        self.handler_factory.create_handler.return_value = mock_handler

        self.schedule_service.load.return_value = schedule
        self.schedule_service.load_enrollment_and_user.return_value = (self.mock_enrollment, self.mock_user)
        self.schedule_service.load_model.return_value = self.mock_course

        def update_schedule_status_side_effect(schedule, dispatch_id, success=True):
            if dispatch_id and success:
                schedule.message_id = dispatch_id
                schedule.status = model.ScheduleStatus.sent

        self.schedule_service.update_schedule_status.side_effect = update_schedule_status_side_effect

        self.service.dispatch(schedule.id)

        self.handler_factory.create_handler.assert_called_once_with(ScheduleType.course_end)
        mock_handler.handle.assert_called_once_with(schedule, self.mock_enrollment, self.mock_user)
        self.schedule_service.update_schedule_status.assert_called_once_with(schedule, "dispatch-id-456", True)
        self.chat_service.registry_schedule.assert_called_once_with(schedule)
        self.assertEqual(schedule.status, model.ScheduleStatus.sent)
        self.assertEqual(schedule.message_id, "dispatch-id-456")

    def test_local_date_conversion(self):
        utc_date = datetime.datetime(2023, 1, 1, 12, 0, tzinfo=datetime.timezone.utc)
        local_date = CourseEndHandler._local_date(utc_date, "America/Sao_Paulo")
        self.assertEqual(local_date, datetime.date(2023, 1, 1))

    def test_dispatch_course_end_with_disabled_certificate(self):
        self.mock_course.disable_send_certificate = True

        schedule = MagicMock(spec=Schedule)
        schedule.id = "test-schedule"
        schedule.type = ScheduleType.course_end
        schedule.status = model.ScheduleStatus.pending
        schedule.reference_id = "course-123"
        schedule.enrollment_id = "enrollment-123"

        mock_handler = MagicMock(spec=CourseEndHandler)
        mock_handler.handle.return_value = "dispatch-id-789"
        self.handler_factory.create_handler.return_value = mock_handler

        self.schedule_service.load.return_value = schedule
        self.schedule_service.load_enrollment_and_user.return_value = (self.mock_enrollment, self.mock_user)
        self.schedule_service.load_model.return_value = self.mock_course

        def update_schedule_status_side_effect(schedule, dispatch_id, success=True):
            if dispatch_id and success:
                schedule.message_id = dispatch_id
                schedule.status = model.ScheduleStatus.sent

        self.schedule_service.update_schedule_status.side_effect = update_schedule_status_side_effect

        self.service.dispatch(schedule.id)

        self.handler_factory.create_handler.assert_called_once_with(ScheduleType.course_end)
        mock_handler.handle.assert_called_once_with(schedule, self.mock_enrollment, self.mock_user)
        self.schedule_service.update_schedule_status.assert_called_once_with(schedule, "dispatch-id-789", True)
        self.chat_service.registry_schedule.assert_called_once_with(schedule)
        self.assertEqual(schedule.status, model.ScheduleStatus.sent)
        self.assertEqual(schedule.message_id, "dispatch-id-789")