from unittest import mock

import re
import pytest
import uuid
from datetime import date, datetime
from unittest.mock import MagicMock

from domain.service import CertificateService
from domain.model import WorkspaceCertificateTemplate, Workspace, EnrollmentStatus, default_lang
from domain.exceptions.service_exceptions import IntegrityException
from domain.client import S3Client, MyAccountClient
from domain.clients.report_generator_client import ReportGenerator<PERSON>lient
import json

from domain.services.workspace_certificate_template_service import WorkspaceCertificateTemplateService
from tests.utils import create_user, create_course_finished, create_enrollment


@pytest.fixture
def report_generator_client():
    return MagicMock(spec=ReportGeneratorClient)


@pytest.fixture
def aws_s3_client():
    return MagicMock(spec=S3Client)


@pytest.fixture
def account_client():
    return MagicMock(spec=MyAccountClient)


@pytest.fixture
def workspace_certificate_template_service():
    return MagicMock(spec=WorkspaceCertificateTemplateService)


@pytest.fixture
def course(db, workspace, user):
    return create_course_finished(db, default_lang, workspace.id, user.id)


@pytest.fixture
def enrollment(db, workspace, user, course, status: str = EnrollmentStatus.completed):
    user = create_user(db, '*************', workspace.id)
    enrollment = create_enrollment(db, user.id, course.id, workspace.id, 'America/Sao_Paulo', status)
    enrollment.end_date = datetime.utcnow().date()
    enrollment.performance = 0.8
    return enrollment


@pytest.fixture
def certificate_service(
    report_generator_client,
    aws_s3_client,
    account_client,
    tmp_path,
    workspace_certificate_template_service
):
    workspace_color_path = tmp_path / "workspace_colors.json"
    workspace_color_path.write_text(json.dumps({"default": "#FFFFFF"}))

    return CertificateService(
        report_generator_client=report_generator_client,
        aws_s3_client=aws_s3_client,
        account_client=account_client,
        workspace_color_path=str(workspace_color_path),
        default_templates={"en": "default_en_template", "pt-br": "default_pt_template", "es": "default_es_template"},
        workspace_certificate_template_service=workspace_certificate_template_service
    )


def test_generate_certificate_started_status(certificate_service, enrollment, course):
    enrollment.status = EnrollmentStatus.started
    with pytest.raises(IntegrityException):
        certificate_service.generate_certificate(enrollment, course)


def test_generate_certificate_success(certificate_service, enrollment, course, report_generator_client, aws_s3_client):
    report_generator_client.generate_report.return_value = "/path/to/generated_certificate.pdf"
    aws_s3_client.send_file_from_path.return_value = {"url": "http://s3.amazonaws.com/bucket/certificate.pdf"}

    certificate_url = certificate_service.generate_certificate(enrollment, course)

    assert certificate_url == "http://s3.amazonaws.com/bucket/certificate.pdf"
    aws_s3_client.send_file_from_path.assert_called_once()


def test_certificate_file_name(certificate_service, enrollment, course, report_generator_client, aws_s3_client):
    report_generator_client.generate_report.return_value = "/path/to/generated_certificate.pdf"
    aws_s3_client.send_file_from_path.return_value = {"url": "http://s3.amazonaws.com/bucket/certificate.pdf"}
    course.name = "COURSE_NAME"
    enrollment.user.name = "USER_NAME"

    certificate_service.generate_certificate(enrollment, course)

    certificate_pattern = r'^certificates/smartzap-course/certificado_COURSE_NAME_USER_NAME_[a-zA-Z0-9]{4,}\.pdf$'

    certificate_filename = aws_s3_client.send_file_from_path.call_args.args[1]
    assert re.match(certificate_pattern, certificate_filename)


def test_format_time_duration(certificate_service):
    assert certificate_service.format_time_duration(3661, "hm") == "1h01m"
    assert certificate_service.format_time_duration(3600, "h") == "1h"
    assert certificate_service.format_time_duration(180, "m") == "3m"


def test_format_long_date(certificate_service):
    test_date = date(2024, 10, 1)
    assert certificate_service.format_long_date(test_date, "pt-br") == "1 de outubro de 2024"
    assert certificate_service.format_long_date(test_date, "en") == "October 1, 2024"
    assert certificate_service.format_long_date(test_date, "es") == "1 de octubre de 2024"


def test_format_time_date(certificate_service):
    test_date = date(2024, 10, 1)
    formatted_date = certificate_service.format_time_date(test_date)
    assert formatted_date == "01/10/2024"


def test_get_certificate_template_existing(certificate_service, db, workspace_certificate_template_service):
    workspace = MagicMock(spec=Workspace)
    workspace.id = uuid.uuid4()

    mock_template = MagicMock(spec=WorkspaceCertificateTemplate)
    mock_template.certificate_template_key = "existing_template"
    workspace_certificate_template_service.find_by_workspace.return_value = mock_template

    template_key = certificate_service._get_certificate_template(workspace, "en")
    assert template_key == "existing_template"


def test_get_certificate_template_default(certificate_service, db, workspace_certificate_template_service):
    workspace = MagicMock(spec=Workspace)
    workspace.id = uuid.uuid4()

    mock_template = MagicMock(spec=WorkspaceCertificateTemplate)
    mock_template.certificate_template_key = "existing_template"
    workspace_certificate_template_service.find_by_workspace.return_value = None

    template_key = certificate_service._get_certificate_template(workspace, "en")
    assert template_key == "default_en_template"


def test_upload(certificate_service, aws_s3_client):
    aws_s3_client.send_file_from_path.return_value = {"url": "http://s3.amazonaws.com/bucket/file.pdf"}
    file_path = "/path/to/file.pdf"
    uploaded_url = certificate_service._upload(file_path, "file.pdf")
    assert uploaded_url == "http://s3.amazonaws.com/bucket/file.pdf"
    aws_s3_client.send_file_from_path.assert_called_once_with(
        file_path,
        mock.ANY,
        "application/pdf"
    )

