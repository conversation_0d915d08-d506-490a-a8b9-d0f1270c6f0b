from unittest.mock import MagicMock

from domain.config import Config
from tasks.task import send_message, completed_enrollments_report_csv, in_progress_enrollments_report_csv, \
    activities_report_csv

CLIENT_ID = '4332'
COURSE_ID = '1234'
USER_ID = '4321'
USER_TOKEN = 'token'
REPORT_ID = '243241'


class ResponseStub:
    def __init__(self, data: dict):
        self._data = data
    def json(self):
        return self._data


def test_send_message(mocker):
    send_to_twilio_spy = mocker.patch('domain.client.WhatsAppTwilioClient._send_to_twilio')
    get_template_sid_spy = mocker.patch('domain.client.WhatsAppTwilioClient.get_template_sid')
    get_template_sid_spy.return_value = "HX23131"
    phone = '55489220078003'
    message_type = 'message'
    callback_url = 'callbackurl'
    private_channels_in_string = '{"pt-br": "channel_user:channe_pass@channel_phone"}'

    send_message(
        phone,
        message_type,
        {},
        callback_url,
        'pt',
        private_channels_in_string,
        None
    )

    send_to_twilio_spy.assert_called_once_with(
        'channel_user',
        'channe_pass',
        {
            'from_': 'whatsapp:channel_phone',
            'content_sid': "HX23131",
            'content_variables': '{}',
            'to': f'whatsapp:{phone}',
            'status_callback': callback_url
        }
    )


def test_completed_enrollments_report_csv(mocker):
    notify_mock = mocker.patch("tasks.task.notify_user_when_analytics_report_processed.delay")
    post_request_mock = mocker.patch("requests.Session.post")
    create_engine_and_session_mock = mocker.patch("domain.database.create_engine_and_session")
    create_engine_and_session_mock.return_value = MagicMock(), MagicMock()

    post_request_mock.return_value = ResponseStub({"report": {"id": REPORT_ID}})

    completed_enrollments_report_csv(CLIENT_ID, USER_ID, COURSE_ID, USER_TOKEN)

    notify_mock.assert_called_with(REPORT_ID, "Completed Enrollments Report CSV", CLIENT_ID, USER_ID)
    post_request_mock.assert_called_with(
        f'{Config.LEARN_ANALYTICS_URL}reports/smartzap-enrollments-exports',
        json={'report_format': 'XLSX', 'filters': {'status__in': ['COMPLETED'], 'course_id': '1234'}},
        timeout=20
    )


def test_in_progress_enrollments_report_csv(mocker):
    notify_mock = mocker.patch("tasks.task.notify_user_when_analytics_report_processed.delay")
    post_request_mock = mocker.patch("requests.Session.post")
    create_engine_and_session_mock = mocker.patch("domain.database.create_engine_and_session")
    create_engine_and_session_mock.return_value = MagicMock(), MagicMock()

    post_request_mock.return_value = ResponseStub({"report": {"id": REPORT_ID}})

    in_progress_enrollments_report_csv(CLIENT_ID, USER_ID, COURSE_ID, USER_TOKEN)

    notify_mock.assert_called_with(REPORT_ID, "In Progress Enrollments Report CSV", CLIENT_ID, USER_ID)
    post_request_mock.assert_called_with(
        f'{Config.LEARN_ANALYTICS_URL}reports/smartzap-enrollments-exports',
        json={'report_format': 'XLSX', 'filters': {'status__in': ['STARTED', 'WAITING'], 'course_id': '1234'}},
        timeout=20
    )


def test_activities_report_csv(mocker):
    notify_mock = mocker.patch("tasks.task.notify_user_when_analytics_report_processed.delay")
    post_request_mock = mocker.patch("requests.Session.post")
    create_engine_and_session_mock = mocker.patch("domain.database.create_engine_and_session")
    create_engine_and_session_mock.return_value = MagicMock(), MagicMock()

    post_request_mock.return_value = ResponseStub({"report": {"id": REPORT_ID}})

    activities_report_csv(CLIENT_ID, USER_ID, COURSE_ID, USER_TOKEN)

    notify_mock.assert_called_with(REPORT_ID, "Activities Report CSV", CLIENT_ID, USER_ID)
    post_request_mock.assert_called_with(
        f'{Config.LEARN_ANALYTICS_URL}reports/smartzap-user-activities-exports',
        json={'report_format': 'XLSX', 'filters': {'enrollment__course_id': '1234'}},
        timeout=20
    )
