import datetime
import random
import uuid
from unittest.mock import patch

import freezegun

from domain import model, service
from tasks.celery_app import app

workspace_counter = 0
SEND_TASK = 'tasks.celery_app.app.send_task'


def learn_contents():
    contents = {
        '0563d6ca-2c28-40e5-be8b-455bd51b394d': {'duration': 171.0, 'points': 4.0, 'analyzed': True},
        '1e8686b5-5288-48c1-9576-89248cb769cf': {'duration': 5.3999999999999995, 'points': 27.0, 'analyzed': True},
        '28e0d09f-a86a-4541-ac71-1a2db0d556ca': {'duration': 182.0, 'points': 6.0, 'analyzed': True},
        '4002d05e-d50f-4a90-830b-192420fb88be': {'duration': 2.6, 'points': 13.0, 'analyzed': True},
        '5db61f5e-61cf-43f0-86bf-a17e77fb60ed': {'duration': 542.5574603174604, 'points': 18.0, 'analyzed': True},
        '62a078f9-3dc8-44ac-9ff9-c18413a41e86': {'duration': 79.39999999999999, 'points': 90.0, 'analyzed': True},
        '7c3041dc-ac37-40cc-b4fa-879684c41848': {'duration': 144.0, 'points': 4.0, 'analyzed': True},
        'a977cf2c-2d14-4f36-a3c1-076c419d2545': {'duration': 38.8, 'points': 194.0, 'analyzed': True},
        'ca91b6cb-7f47-4e6c-9cc1-5ca2d6aea111': {'duration': 122.2, 'points': 30.55, 'analyzed': True},
    }
    return contents


def create_course_category(db):
    course_category = model.CourseCategory()
    course_category.name = 'Course Category'
    course_category.description = 'My Course Category'
    db.add(course_category)
    db.commit()
    return course_category


def create_course_finished(db, lang, client_id, user_id, allow_content_antecipation=True):
    course_category = create_course_category(db)
    course = model.Course()
    course.name = 'My Course'
    course.description = 'My course description'
    course.holder_image = 'http://www.imagens.com.br/imagem-legal'
    course.thumb_image = 'http://www.imagens.com.br/imagem-legal'
    course.is_active = False
    course.status = model.CourseStatus.finished
    course.lang = lang
    course.category_id = course_category.id
    course.allow_content_anticipation = allow_content_antecipation
    course_service = service.CourseService(db, client_id, user_id)
    course_service.add(course)
    create_lessons(db, course)
    return course


def create_lessons(db, course):
    lessons = []
    for i in reversed(range(3)):
        lesson = model.Lesson()
        lesson.name = f'Lesson {i}'
        lesson.description = f'Lesson {i} Description'
        lesson.order = i
        lesson.course_id = course.id
        db.add(lesson)
        lessons.append(lesson)
    db.commit()
    create_contents(db, lessons)


def create_content_type(db, content_type_name: str = 'Content Type'):
    content_type = model.ContentType()
    content_type.name = content_type_name
    content_type.description = 'My Content Type'
    db.add(content_type)
    db.commit()
    return content_type


def create_contents(db, lessons):
    learn_content_ids = [key for key, value in learn_contents().items()]
    content_type = create_content_type(db)
    s_lessons = sorted(lessons, key=lambda l: l.order)
    dispatch_days = [
        (1, model.ContentPeriod.morning), (2, model.ContentPeriod.night), (4, model.ContentPeriod.morning),
        (5, model.ContentPeriod.afternoon), (6, model.ContentPeriod.morning), (6, model.ContentPeriod.night),
        (7, model.ContentPeriod.night), (7, model.ContentPeriod.morning), (7, model.ContentPeriod.afternoon)
    ]
    content_number = 0
    for lesson in s_lessons:
        for i in range(3):
            days, period = dispatch_days[content_number]
            content = model.Content()
            content.name = f'Content {content_number}'
            content.description = f'Description of content {content_number}'
            content.order = i
            content.dispatch_in = days
            content.dispatch_period = period
            content.learn_content = learn_content_ids[content_number]
            content.lesson_id = lesson.id
            content.type_id = content_type.id
            content_number += 1
            db.add(content)
    db.commit()


def create_content(db, lesson_id, content_type_id, order: int = 1):
    content = model.Content()
    content.name = f'Content 2'
    content.description = f'Description of content'
    content.dispatch_in = 5
    content.order = order
    content.learn_content = str(uuid.uuid4())
    content.lesson_id = lesson_id
    content.type_id = content_type_id
    db.add(content)
    db.commit()
    return content


def create_user(db, phone, workspace_id):
    user = model.User()
    user.name = 'Pessoa'
    user.phone = phone
    db.add(user)
    db.flush()

    user_workspace = model.UserWorkspace()
    user_workspace.workspace_id = workspace_id
    user_workspace.user_id = user.id
    db.add(user_workspace)
    db.commit()
    return user


def create_enrollment(
    db, user_id, course_id, workspace_id, tmz, status=model.EnrollmentStatus.waiting
):
    enrollment = model.Enrollment()
    enrollment.start_date = datetime.date.today()
    enrollment.workspace_id = workspace_id
    enrollment.user_id = user_id
    enrollment.course_id = course_id
    enrollment.timezone = tmz
    enrollment.status = status

    schedule_service = service.ScheduleService(db, workspace_id)
    enrollment_service = service.EnrollmentService(db, schedule_service)

    with patch.object(app, 'send_task'):
        enrollment_service.add(enrollment)
    return enrollment


def create_enrollments(db, count, course_id, workspace_id, tmz, status=model.EnrollmentStatus.waiting):
    enrollments = []
    for _ in range(count):
        new_user = create_user(db, generate_random_phone(), workspace_id)
        enrollments.append(create_enrollment(db, new_user.id, course_id, workspace_id, tmz, status))
    return enrollments


def create_course_and_accept_enrollment(
        db, workspace, phone, lang, timezone, when, offset, admin, allow_content_antecipation: bool = True
):
    with freezegun.freeze_time(when, tz_offset=offset):
        user = create_user(db, phone, workspace.id)
        course = create_course_finished(db, lang, workspace.id, user.id, allow_content_antecipation)
        enrollment = create_enrollment(db, user.id, course.id, workspace.id, timezone)
        schedule_service = service.ScheduleService(db, workspace.id)
        enrollment_service = service.EnrollmentService(db, schedule_service)

    when = when + datetime.timedelta(minutes=3)
    with freezegun.freeze_time(when, tz_offset=offset):
        pending_schedules = schedule_service.load_pending_schedules(1, 99)
        introduction_schedule = pending_schedules[0]
        admin.dispatch(introduction_schedule.id)
        enrollment_service.accept_disclaimer(enrollment)
        return enrollment


def create_workspace(db, monthly_plan=50, cycle_day=10, private_channel=None, messages_by_twilio_studio: bool = False):
    global workspace_counter
    workspace_counter += 1

    workspace = model.Workspace()
    workspace.id = str(uuid.uuid4())
    workspace.name = f'Companhia {workspace_counter}'
    workspace.user_token_expiration = 3
    workspace.end_course_schedule = 2
    workspace.monthly_plan = monthly_plan
    workspace.billing_cycle_day = cycle_day
    workspace.private_channel = private_channel
    workspace.messages_by_twilio_studio = messages_by_twilio_studio
    workspace.send_course_reminder_message = True
    db.add(workspace)
    db.flush()
    return workspace


def create_activities(db, activity_service, enrollment_id):
    contents = db.query(model.Content).all()

    activities = {
        0: [57, 15],
        1: [12],
        2: [92, 4, 4],
        3: [30],
        4: [31, 42, 68],
        5: [90],
        6: [29],
        7: [13],
        8: [62, 3]
    }

    base_datetime = datetime.datetime.utcnow() + datetime.timedelta(days=-1)
    for index, seconds in activities.items():
        for sec in seconds:
            activity = model.Activity()
            activity.enrollment_id = enrollment_id
            activity.content_id = contents[index].id
            activity.start_at = base_datetime
            activity.stop_at = base_datetime + datetime.timedelta(seconds=sec)
            activity_service.add(activity)
            base_datetime = base_datetime + datetime.timedelta(minutes=2)


def dispatch_schedules(schedule_service, schedule_admin):
    pending_schedules = schedule_service.load_pending_schedules(1, 99)
    for schedule in pending_schedules:
        schedule_admin.dispatch(schedule.id)


def generate_random_phone():
    second = str(random.randint(1, 888)).zfill(4)
    last = (str(random.randint(1, 9998)).zfill(4))
    while last in ['1111', '2222', '3333', '4444', '5555', '6666', '7777', '8888']:
        last = (str(random.randint(1, 9998)).zfill(4))
    return '559{}{}'.format(second, last)


def full_clean_string(text: str) -> str:
    return text.replace(" ", "").replace("\n", "")