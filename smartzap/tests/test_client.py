from unittest import mock

from domain import client, model


def mock_twilio_sender_func(account_id, _, __):
    ids_by_account = {
        '001': (1, True),
        '002': (2, True),
        '003': (3, True),
    }

    return ids_by_account.get(account_id, ('error', False))


def build_channels(lang, account_id):
    channel = f'{account_id}:pass@************'
    return model.LanguageChannels('{' + f'"{lang}": "{channel}"' + '}')


def test_override_standard_channels_while_sending_whatsapp_message(templates_sids):
    with mock.patch('domain.client.WhatsAppTwilioClient._send_to_twilio') as twilio_sender:
        twilio_sender.side_effect = lambda _id, _key, _message: mock_twilio_sender_func(_id, _key, _message)
        whatsapp_client = client.WhatsAppTwilioClient(build_channels('pt', '001'), templates_sids)
        private_channels = build_channels('pt', '002')
        sid, successful = whatsapp_client.send_message(
            '',
            'message',
            {},
            '',
            'pt-BR',
            private_channels
        )
        assert sid == 2
        assert successful


def test_private_channel_do_not_use_global_channel_if_lang_is_missing(templates_sids):
    with mock.patch('domain.client.WhatsAppTwilioClient._send_to_twilio') as twilio_sender:
        twilio_sender.side_effect = lambda _id, _key, _message: mock_twilio_sender_func(_id, _key, _message)
        whatsapp_client = client.WhatsAppTwilioClient(build_channels('es', '001'), templates_sids)
        private_channels = build_channels('pt', '003')
        sid, successful = whatsapp_client.send_message(
            '',
            'message',
            {},
            '',
            'es',
            private_channels
        )
        assert sid == 3
        assert successful


def test_invalid_private_channel(templates_sids, mocker):
    with mock.patch('domain.client.WhatsAppTwilioClient._send_to_twilio') as twilio_sender:
        get_template_sid_mock = mocker.patch('domain.client.WhatsAppTwilioClient.get_template_sid')
        get_template_sid_mock.return_value = 'template_sid'

        twilio_sender.side_effect = lambda _id, _key, _message: mock_twilio_sender_func(_id, _key, _message)
        whatsapp_client = client.WhatsAppTwilioClient(build_channels('pt', '001'), templates_sids)
        private_channels = model.LanguageChannels('')
        sid, successful = whatsapp_client.send_message(
            '',
            'message',
            {},
            '',
            'pt',
            private_channels
        )
        assert sid == 'error'
        assert not successful
