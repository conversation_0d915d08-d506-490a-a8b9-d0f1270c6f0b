import flask
import pytest

from flask_extensions.webfilters import load_filters


DEFAULT_FIELD = "default_field"

@pytest.fixture
def flask_app():
    return flask.Flask('test_flask_app')


def test_load_filters_with_overrides(flask_app):
    with flask_app.test_request_context():
        flask.request.args = {"some_field": "xpto"}
        override_filters = {DEFAULT_FIELD: "default_value"}
        filters = load_filters(override_filters=override_filters)

    default_spec = list(filter(lambda spec: spec["field"] == DEFAULT_FIELD, filters["filter_spec"]))[0]

    assert default_spec
    assert default_spec["value"] == override_filters[DEFAULT_FIELD]


def test_filters_override_request_filters(flask_app):
    with flask_app.test_request_context():
        flask.request.args = {DEFAULT_FIELD: "request_value"}
        override_filters = {DEFAULT_FIELD: "default_value"}
        filters = load_filters(override_filters=override_filters)

    default_specs = list(filter(lambda spec: spec["field"] == DEFAULT_FIELD, filters["filter_spec"]))
    default_spec = default_specs[0]

    assert len(default_specs) == 1
    assert default_spec["value"] == override_filters[DEFAULT_FIELD]
