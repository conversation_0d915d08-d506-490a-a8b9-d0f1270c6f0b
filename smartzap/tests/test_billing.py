import datetime
import uuid

import freezegun

from smartzap.domain import model
from . import utils


def _dt(y, m, d):
    return datetime.date(y, m, d)


def _assert_date(lhs, rhs):
    assert lhs.year == rhs.year
    assert lhs.month == rhs.month
    assert lhs.day == rhs.day


def _assert_cycles_days(days, expected):
    assert len(days) == len(expected)
    for i in range(len(expected)):
        assert days[i] == expected[i]


def _create_schedule(workspace_id, schedule_service):
    schedule = model.Schedule()
    schedule.enrollment_id = str(uuid.uuid4())
    schedule.reference_id = str(uuid.uuid4())
    schedule.type = model.ScheduleType.lesson_content
    schedule.send_date = datetime.datetime.now()
    schedule.lang = 'pt-br'
    schedule.timezone = 'UTC'
    schedule.workspace_id = workspace_id
    schedule.status = model.ScheduleStatus.delivered
    schedule_service.add(schedule)


def _process_billing_charges(workspace_ids, workspace_service, schedule_service):
    schedules_dates = {
        '2019-12-12 09:00:00': 13,
        '2019-12-28 09:00:00': 30,
        '2020-01-01 14:00:00': 8,
        '2020-01-09 14:00:00': 4,
        '2020-01-22 14:00:00': 33,
        '2020-02-17 19:00:00': 2,
        '2020-02-27 19:00:00': 18,
        '2020-02-29 19:00:00': 21,
        '2020-03-05 13:00:00': 54,
        '2020-03-25 13:00:00': 34,
        '2020-04-02 09:00:00': 5,
        '2020-04-14 09:00:00': 9,
        '2020-04-22 09:00:00': 11,
    }
    for s_dt, count in schedules_dates.items():
        with freezegun.freeze_time(s_dt):
            for _ in range(count):
                for _id in workspace_ids:
                    _create_schedule(_id, schedule_service)

    for m in ['01', '02', '03', '04', '05']:
        dates = [f'2020-{m}-05 00:00:00', f'2020-{m}-10 00:00:00']
        for d in dates:
            with freezegun.freeze_time(d):
                workspace_service.process_billing_charges(datetime.date.today())


def test_billing_days_for_processing(workspace_service):
    with freezegun.freeze_time('2020-03-01 00:00:00'):
        expected = [30, 31, 1]
        days = workspace_service._processing_cycle_days(datetime.date.today())
        _assert_cycles_days(days, expected)

    with freezegun.freeze_time('2019-03-01 00:00:00'):
        expected = [29, 30, 31, 1]
        days = workspace_service._processing_cycle_days(datetime.date.today())
        _assert_cycles_days(days, expected)

    with freezegun.freeze_time('2019-10-01 00:00:00'):
        expected = [31, 1]
        days = workspace_service._processing_cycle_days(datetime.date.today())
        _assert_cycles_days(days, expected)

    with freezegun.freeze_time('2019-10-02 00:00:00'):
        expected = [2]
        days = workspace_service._processing_cycle_days(datetime.date.today())
        _assert_cycles_days(days, expected)


def test_billing_charge_period(workspace_service):
    processing_date = {
        '2019-02-12 00:00:00': {
            12: (_dt(2019, 1, 12), _dt(2019, 2, 11)),
        },
        '2019-02-28 00:00:00': {
            28: (_dt(2019, 1, 28), _dt(2019, 2, 27)),
        },
        '2019-03-01 00:00:00': {
            29: (_dt(2019, 1, 29), _dt(2019, 2, 28)),
            30: (_dt(2019, 1, 30), _dt(2019, 2, 28)),
            31: (_dt(2019, 1, 31), _dt(2019, 2, 28)),
            1: (_dt(2019, 2, 1), _dt(2019, 2, 28))
        },
        '2020-02-29 00:00:00': {
            29: (_dt(2020, 1, 29), _dt(2020, 2, 28)),
        },
        '2020-03-01 00:00:00': {
            30: (_dt(2020, 1, 30), _dt(2020, 2, 29)),
            31: (_dt(2020, 1, 31), _dt(2020, 2, 29)),
            1: (_dt(2020, 2, 1), _dt(2020, 2, 29))
        },
        '2020-03-02 00:00:00': {
            2: (_dt(2020, 2, 2), _dt(2020, 3, 1))
        },
        '2020-04-01 00:00:00': {
            1: (_dt(2020, 3, 1), _dt(2020, 3, 31))
        },
        '2020-04-02 00:00:00': {
            2: (_dt(2020, 3, 2), _dt(2020, 4, 1))
        },
        '2020-04-30 00:00:00': {
            30: (_dt(2020, 3, 30), _dt(2020, 4, 29))
        },
        '2020-05-01 00:00:00': {
            31: (_dt(2020, 3, 31), _dt(2020, 4, 30)),
            1: (_dt(2020, 4, 1), _dt(2020, 4, 30))
        },
        '2020-05-15 00:00:00': {
            15: (_dt(2020, 4, 15), _dt(2020, 5, 14))
        },
    }
    for p_date, data in processing_date.items():
        with freezegun.freeze_time(p_date):
            cycle_days = [k for k, _ in data.items()]
            _assert_cycles_days(workspace_service._processing_cycle_days(datetime.date.today()), cycle_days)
            for d in cycle_days:
                start, end = workspace_service._processing_cycle_period(datetime.date.today(), d)
                _assert_date(start, data[d][0])
                _assert_date(end, data[d][1])


def test_billing_open_cycle_period(workspace_service):
    cycle_date = {
        '2019-02-12 00:00:00': {
            13: (_dt(2019, 1, 13), _dt(2019, 2, 12)),
            29: (_dt(2019, 1, 29), _dt(2019, 2, 28)),
            30: (_dt(2019, 1, 30), _dt(2019, 2, 28)),
            31: (_dt(2019, 1, 31), _dt(2019, 2, 28)),
            1: (_dt(2019, 2, 1), _dt(2019, 2, 28)),
            11: (_dt(2019, 2, 11), _dt(2019, 3, 10)),
            12: (_dt(2019, 2, 12), _dt(2019, 3, 11)),
        },
        '2019-02-28 00:00:00': {
            29: (_dt(2019, 1, 29), _dt(2019, 2, 28)),
            30: (_dt(2019, 1, 30), _dt(2019, 2, 28)),
            31: (_dt(2019, 1, 31), _dt(2019, 2, 28)),
            1: (_dt(2019, 2, 1), _dt(2019, 2, 28)),
            2: (_dt(2019, 2, 2), _dt(2019, 3, 1)),
            12: (_dt(2019, 2, 12), _dt(2019, 3, 11)),
            28: (_dt(2019, 2, 28), _dt(2019, 3, 27)),
        },
        '2019-03-01 00:00:00': {
            29: (_dt(2019, 3, 1), _dt(2019, 3, 28)),
            30: (_dt(2019, 3, 1), _dt(2019, 3, 29)),
            31: (_dt(2019, 3, 1), _dt(2019, 3, 30)),
            1: (_dt(2019, 3, 1), _dt(2019, 3, 31)),
        },
        '2019-03-15 00:00:00': {
            29: (_dt(2019, 3, 1), _dt(2019, 3, 28)),
            30: (_dt(2019, 3, 1), _dt(2019, 3, 29)),
            31: (_dt(2019, 3, 1), _dt(2019, 3, 30)),
            1: (_dt(2019, 3, 1), _dt(2019, 3, 31)),
            13: (_dt(2019, 3, 13), _dt(2019, 4, 12)),
            14: (_dt(2019, 3, 14), _dt(2019, 4, 13)),
            15: (_dt(2019, 3, 15), _dt(2019, 4, 14)),
        },
        '2019-03-30 00:00:00': {
            29: (_dt(2019, 3, 29), _dt(2019, 4, 28)),
            30: (_dt(2019, 3, 30), _dt(2019, 4, 29)),
            31: (_dt(2019, 3, 1), _dt(2019, 3, 30)),
            1: (_dt(2019, 3, 1), _dt(2019, 3, 31)),
            11: (_dt(2019, 3, 11), _dt(2019, 4, 10)),
        },
        '2020-03-15 00:00:00': {
            29: (_dt(2020, 2, 29), _dt(2020, 3, 28)),
            14: (_dt(2020, 3, 14), _dt(2020, 4, 13)),
            15: (_dt(2020, 3, 15), _dt(2020, 4, 14)),
        },
    }
    for p_date, data in cycle_date.items():
        with freezegun.freeze_time(p_date):
            cycle_days = [k for k, _ in data.items()]
            for d in cycle_days:
                start, end = workspace_service._open_cycle_period(datetime.date.today(), d)
                _assert_date(start, data[d][0])
                _assert_date(end, data[d][1])


def test_billing_charges(db, workspace_service, schedule_service):
    workspace1 = utils.create_workspace(db, 60, 5)
    workspace2 = utils.create_workspace(db, 38, 10)
    _process_billing_charges([workspace1.id, workspace2.id], workspace_service, schedule_service)
    billing1 = workspace_service.load_billing_charges(workspace1.id)
    billing2 = workspace_service.load_billing_charges(workspace2.id)
    expected_data = {
        1: [
            {'start': _dt(2020, 4, 5), 'end': _dt(2020, 5, 4), 'used': 20, 'balance': 40},
            {'start': _dt(2020, 3, 5), 'end': _dt(2020, 4, 4), 'used': 93, 'balance': -33},
            {'start': _dt(2020, 2, 5), 'end': _dt(2020, 3, 4), 'used': 41, 'balance': 19},
            {'start': _dt(2020, 1, 5), 'end': _dt(2020, 2, 4), 'used': 37, 'balance': 23},
            {'start': _dt(2019, 12, 5), 'end': _dt(2020, 1, 4), 'used': 51, 'balance': 9},
        ],
        2: [
            {'start': _dt(2020, 4, 10), 'end': _dt(2020, 5, 9), 'used': 20, 'balance': 18},
            {'start': _dt(2020, 3, 10), 'end': _dt(2020, 4, 9), 'used': 39, 'balance': -1},
            {'start': _dt(2020, 2, 10), 'end': _dt(2020, 3, 9), 'used': 95, 'balance': -57},
            {'start': _dt(2020, 1, 10), 'end': _dt(2020, 2, 9), 'used': 33, 'balance': 5},
            {'start': _dt(2019, 12, 10), 'end': _dt(2020, 1, 9), 'used': 55, 'balance': -17},
        ],
    }
    assert len(billing1) == 5
    assert len(billing2) == 5

    def _assert_billing(billing, expect_data):
        assert billing.used == expect_data['used'], 'used'
        assert billing.balance == expect_data['balance'], 'balance'
        _assert_date(billing.start_at, expect_data['start'])
        _assert_date(billing.end_at, expect_data['end'])

    for i in range(5):
        _assert_billing(billing1[i], expected_data[1][i])
        _assert_billing(billing2[i], expected_data[2][i])


def test_billing_charges_avoid_open_periods(db, workspace_service, schedule_service):
    workspace1 = utils.create_workspace(db, 60, 5)
    _create_schedules(workspace1.id, '2020-02-15 00:00:00', 13, schedule_service)
    _create_schedules(workspace1.id, '2020-03-02 00:00:00', 20, schedule_service)
    with freezegun.freeze_time('2020-03-03 00:00:00'):
        workspace_service.process_billing_charges(datetime.date.today())
    billing1 = workspace_service.load_billing_charges(workspace1.id)
    assert not billing1


def test_billing_charges_avoid_duplicity(db, workspace_service, schedule_service):
    workspace1 = utils.create_workspace(db, 60, 5)
    _create_schedules(workspace1.id, '2020-02-15 00:00:00', 13, schedule_service)
    _create_schedules(workspace1.id, '2020-03-02 00:00:00', 20, schedule_service)
    with freezegun.freeze_time('2020-03-05 00:00:00'):
        workspace_service.process_billing_charges(datetime.date.today())
        workspace_service.process_billing_charges(datetime.date.today())
    billing1 = workspace_service.load_billing_charges(workspace1.id)
    assert len(billing1) == 1


def _create_schedules(workspace_ids, date, count, schedule_service):
    with freezegun.freeze_time(date):
        for _ in range(count):
            for _id in workspace_ids:
                _create_schedule(_id, schedule_service)


def test_billing_report(db, workspace_service, schedule_service):
    workspace1 = utils.create_workspace(db, 60, 5)
    workspace2 = utils.create_workspace(db, 38, 10)

    billing_data = {
        '2019-12-13 09:00:00': {
            'schedules': {
                '2019-12-12 09:00:00': 13,
            },
            'processing_dates': [],
            'companies': {
                1: {'available_zaps': 47, 'zaps_sent': 13,
                    'start': _dt(2019, 12, 5), 'end': _dt(2020, 1, 4), 'charges': 0},
                2: {'available_zaps': 25, 'zaps_sent': 13,
                    'start': _dt(2019, 12, 10), 'end': _dt(2020, 1, 9), 'charges': 0},
            }
        },
        '2020-01-08 09:00:00': {
            'schedules': {
                '2019-12-28 09:00:00': 30,
                '2020-01-01 14:00:00': 8,
            },
            'processing_dates': ['2020-01-05 00:00:00'],
            'companies': {
                1: {'available_zaps': 69, 'zaps_sent': 0,
                    'start': _dt(2020, 1, 5), 'end': _dt(2020, 2, 4), 'charges': 1},
                2: {'available_zaps': -13, 'zaps_sent': 51,
                    'start': _dt(2019, 12, 10), 'end': _dt(2020, 1, 9), 'charges': 0},
            }
        },
        '2020-02-02 09:00:00': {
            'schedules': {
                '2020-01-09 14:00:00': 4,
                '2020-01-22 14:00:00': 33,
            },
            'processing_dates': ['2020-01-10 00:00:00'],
            'companies': {
                1: {'available_zaps': 32, 'zaps_sent': 37,
                    'start': _dt(2020, 1, 5), 'end': _dt(2020, 2, 4), 'charges': 1},
                2: {'available_zaps': -12, 'zaps_sent': 33,
                    'start': _dt(2020, 1, 10), 'end': _dt(2020, 2, 9), 'charges': 1},
            }
        },
        '2020-03-05 19:00:00': {
            'schedules': {
                '2020-02-17 19:00:00': 2,
                '2020-02-27 19:00:00': 18,
                '2020-02-29 19:00:00': 21,
                '2020-03-05 13:00:00': 54,
            },
            'processing_dates': ['2020-02-05 00:00:00', '2020-02-10 00:00:00', '2020-03-05 00:00:00'],
            'companies': {
                1: {'available_zaps': 48, 'zaps_sent': 54,
                    'start': _dt(2020, 3, 5), 'end': _dt(2020, 4, 4), 'charges': 2},
                2: {'available_zaps': -69, 'zaps_sent': 95,
                    'start': _dt(2020, 2, 10), 'end': _dt(2020, 3, 9), 'charges': 2},
            }
        },
        '2020-03-12 19:00:00': {
            'schedules': {
            },
            'processing_dates': ['2020-03-10 00:00:00'],
            'companies': {
                1: {'available_zaps': 48, 'zaps_sent': 54,
                    'start': _dt(2020, 3, 5), 'end': _dt(2020, 4, 4), 'charges': 2},
                2: {'available_zaps': -14, 'zaps_sent': 0,
                    'start': _dt(2020, 3, 10), 'end': _dt(2020, 4, 9), 'charges': 2},
            }
        },
        '2020-04-25 19:00:00': {
            'schedules': {
                '2020-03-25 13:00:00': 34,
                '2020-04-02 09:00:00': 5,
                '2020-04-14 09:00:00': 9,
                '2020-04-22 09:00:00': 11,
            },
            'processing_dates': ['2020-04-05 00:00:00', '2020-04-10 00:00:00'],
            'companies': {
                1: {'available_zaps': 26, 'zaps_sent': 20,
                    'start': _dt(2020, 4, 5), 'end': _dt(2020, 5, 4), 'charges': 2},
                2: {'available_zaps': -40, 'zaps_sent': 20,
                    'start': _dt(2020, 4, 10), 'end': _dt(2020, 5, 9), 'charges': 2},
            }
        },
    }

    def _assert_report(report, expect_data):
        assert report['available_zaps'] == expect_data['available_zaps'], 'available_zaps'
        assert report['zaps_sent'] == expect_data['zaps_sent'], 'zaps_sent'
        assert len(report['charges']) == expect_data['charges'], 'charges count'
        _assert_date(report['current_billing']['start_at'], expect_data['start'])
        _assert_date(report['current_billing']['end_at'], expect_data['end'])

    workspace_ids = [workspace1.id, workspace2.id]
    for p_date, e_data in billing_data.items():
        for date, count in e_data['schedules'].items():
            _create_schedules(workspace_ids, date, count, schedule_service)

        for date in e_data['processing_dates']:
            with freezegun.freeze_time(date):
                workspace_service.process_billing_charges(datetime.date.today())

        with freezegun.freeze_time(p_date):
            report1 = workspace_service.billing_report(workspace1)
            report2 = workspace_service.billing_report(workspace2)
            _assert_report(report1, e_data['companies'][1])
            _assert_report(report2, e_data['companies'][2])
