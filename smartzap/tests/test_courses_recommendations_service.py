import unittest
from unittest.mock import MagicMock, patch
from datetime import datetime
from domain.model import Course, Enrollment
from domain.services.courses_recommendations_service import CoursesRecommendationsService


class TestCoursesRecommendationsService(unittest.TestCase):
    def setUp(self):
        self.mock_db_session = MagicMock()
        self.mock_message_dispatcher = MagicMock()
        self.mock_user_service = MagicMock()
        self.schedule_admin_service = MagicMock()
        self.schedule_service = MagicMock()
        self.enrollment_service = MagicMock()
        self.service = CoursesRecommendationsService(
            db=self.mock_db_session,
            user_service=self.mock_user_service,
            schedule_admin_service=self.schedule_admin_service,
            schedule_service=self.schedule_service,
            enrollment_service=self.enrollment_service,
            days_without_new_enrollment=[1, 3, 5]
        )

    @patch("domain.services.courses_recommendations_service.datetime")
    def test_get_recently_completed_enrollment_user_phones(self, mock_datetime):
        mock_datetime.utcnow.return_value = datetime(2024, 1, 10)

        (
            self.mock_db_session.
            query.return_value.
            join.return_value.
            filter.return_value.
            group_by.return_value.
            having.return_value.
            having.return_value.
            all
         ).return_value = [
            ("4899991111",), ("4899992222",)
        ]

        user_phones = self.service._get_recently_completed_enrollment_user_phones(1, "workspace1")

        self.mock_db_session.query.assert_called()
        self.assertEqual(user_phones, ["4899991111", "4899992222"])

    @patch("domain.services.courses_recommendations_service.datetime")
    def test_get_courses(self, mock_datetime):
        mock_datetime.utcnow.return_value = datetime(2024, 1, 10)

        self.mock_user_service.get_by_phone.return_value = MagicMock(id="user1")

        self.mock_db_session.query.return_value.filter.return_value.order_by.return_value.limit.return_value.all.return_value = [
            Course(id="course1", name="Course 1"),
            Course(id="course2", name="Course 2"),
        ]

        courses = self.service.get_courses("user_phone", "workspace1", 2)

        self.mock_user_service.get_by_phone.assert_called_with("user_phone", True)
        self.mock_db_session.query.assert_called()
        self.assertEqual(len(courses), 2)
        self.assertEqual(courses[0].id, "course1")

    @patch("domain.services.courses_recommendations_service.datetime")
    def test_send_recommendation_message_for_all_users(self, mock_datetime):
        mock_datetime.utcnow.return_value = datetime(2024, 1, 10)

        self.service._get_recently_completed_enrollment_user_phones = MagicMock()
        self.service._get_recently_completed_enrollment_user_phones.side_effect = [
            ["user1", "user2"],  # For 1 day
            ["user3"],           # For 3 days
            []                   # For 5 days
        ]

        users = self.service.list_workspace_users_to_notify("workspace1")

        self.service._get_recently_completed_enrollment_user_phones.assert_any_call(1, "workspace1")
        self.service._get_recently_completed_enrollment_user_phones.assert_any_call(3, "workspace1")
        self.service._get_recently_completed_enrollment_user_phones.assert_any_call(5, "workspace1")

        self.assertEqual(users, {'user1', 'user2', 'user3'})

    def test_get_portal_link(self):
        self.mock_db_session.query.return_value.filter.return_value.scalar.return_value = "https://portal.example.com"

        portal_link = self.service.get_portal_link("user_phone")

        self.mock_db_session.query.assert_called()
        self.assertEqual(portal_link, "https://portal.example.com")
