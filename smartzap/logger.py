import os
import sys

from loguru import logger

# Configuração do loguru
logger.remove()  # Remove o handler padr<PERSON>

# Adiciona handler para console com cores
logger.add(
    sys.stderr,
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
    level="INFO",
    colorize=True
)

# Adiciona handler para arquivo
log_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "logs")
os.makedirs(log_path, exist_ok=True)
logger.add(
    os.path.join(log_path, "app_{time}.log"),
    rotation="500 MB",
    retention="10 days",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
    level="INFO"
)
