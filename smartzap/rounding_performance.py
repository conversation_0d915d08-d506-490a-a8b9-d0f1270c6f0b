import ucache
from domain import config, database, di, model
from domain.utils import round_performance
from logger import logger


def build_container():
    cache = ucache.MemoryCache()
    db, db_session = database.create_engine_and_session(config.d.DATABASE_URL)
    database.shared_session = db_session
    container = di.Container(config.d, cache)
    return container, db


def rounding_performance():
    """
    """
    build_container()
    enrollments = database.shared_session.query(model.Enrollment).filter(model.Enrollment.performance.isnot(None)).all()

    for enrollment in enrollments:
        if len(str(enrollment.performance)) > 4:
            with database.transaction(database.shared_session):
                new_performance = round_performance(enrollment.performance)
                logger.info(f"{enrollment.id} | {enrollment.performance} | {new_performance}")
                enrollment.performance = new_performance


rounding_performance()
