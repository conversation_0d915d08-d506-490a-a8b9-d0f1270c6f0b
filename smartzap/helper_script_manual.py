import datetime

import ucache
from domain import config, database, di, model
from logger import logger


def build_container(db_url=None):
    db_url = db_url or config.d.DATABASE_URL
    cache = ucache.MemoryCache()
    db, db_session = database.create_engine_and_session(db_url)
    database.shared_session = db_session
    container = di.Container(config.d, cache)
    return container, db


def re_schedule_message_by_sid(message_id_list):
    """
    Re-scheduler messages with broker error.

    Need identify on twilio or sms fire messages id with error,
    get ID to find in Smartzap DB and change status to pending
    to be send again.

    :param message_id_list: list of broker message ID.
    """
    build_container()
    sent_schedules = database.shared_session\
        .query(model.Schedule)\
        .filter(model.Schedule.message_id.in_(message_id_list))\
        .all()

    for scheduler in sent_schedules:
        with database.transaction(database.shared_session):
            scheduler.status = model.ScheduleStatus.pending
            scheduler.message_id = None
            scheduler.send_date = '2020-07-05 12:00:00'

        logger.info(scheduler.enrollment_id)


def user_enrolments_by_message_id(message_id_list):
    """
    """
    build_container()
    schedules = database.shared_session\
        .query(model.Schedule)\
        .filter(model.Schedule.message_id.in_(message_id_list))\
        .all()

    for scheduler in schedules:
        logger.info(scheduler.enrollment_id)


def change_schedule_date_by_reference_id(reference_id):
    """
    Re-scheduler messages with broker error.

    :param reference_id: list of broker message ID.
    """
    build_container()
    sent_schedules = database.shared_session\
        .query(model.Schedule)\
        .filter(model.Schedule.reference_id == reference_id, model.Schedule.status == 'PENDING')\
        .all()

    count = 0
    for scheduler in sent_schedules:
        with database.transaction(database.shared_session):
            scheduler.send_date = '2020-07-08 20:00:00'
        logger.info(scheduler.id)
        count = count + 1

    logger.info(count)


def change_schedule_date_by_reference_id_and_enrolment(enrolment_id_list, reference_id):
    """
    """
    build_container()

    for enrolment in enrolment_id_list:
        schedule = database.shared_session\
            .query(model.Schedule)\
            .filter(model.Schedule.enrollment_id == enrolment, model.Schedule.reference_id == reference_id)\
            .first()

        logger.info(schedule.id)

        with database.transaction(database.shared_session):
            schedule.send_date = '2020-06-29 18:00:00'
            schedule.status = model.ScheduleStatus.pending
            schedule.message_id = None


def user_info(users_id):
    """
    """
    build_container()

    for user in users_id:
        instance = database.shared_session.query(model.User).filter(model.User.id == user).first()
        logger.info(instance.name)


def new_user_token(enrollment_id, lang="pt-br", client_id="e919f759-c99d-4c37-9c17-33df3d3fae4f"):
    cont, db = build_container()
    enrollment_service = cont.enrollment()
    enrollment = enrollment_service.load(enrollment_id)
    token_expiration = enrollment.workspace.user_token_expiration
    token = cont.schedule_admin().create_token(enrollment.user_id, lang, client_id, token_expiration, enrollment_id)
    logger.info(token)


def activities_analyze():
    """
    """
    build_container()

    enrollments = database.shared_session.query(model.Enrollment.id).all()
    activities_error = database.shared_session\
        .query(model.Activity)\
        .filter(model.Activity.user_id.in_(enrollments))\
        .all()

    for activity in activities_error:
        filtered = list(filter(lambda x: x.id == activity.user_id, enrollments))[0]
        logger.info("{}   |   {}  |   {}".format(activity.id, activity.user_id, filtered.id))

        enrollment_instance = database.shared_session.query(model.Enrollment).filter(
            model.Enrollment.id == filtered.id).first()

        with database.transaction(database.shared_session):
            activity.user_id = enrollment_instance.user_id
            activity.enrollment_id = enrollment_instance.id


def check_enrollments_ended(enrolls):
    build_container()

    for enroll in enrolls:
        enrollment = database.shared_session.query(model.Enrollment).filter(model.Enrollment.id == enroll).first()
        if enrollment.status == 'COMPLETED':
            schedule = database.shared_session\
                .query(model.Schedule)\
                .filter(model.Schedule.enrollment_id == enroll, model.Schedule.type == 'COURSE_END')\
                .first()

            with database.transaction(database.shared_session):
                enrollment.status = 'STARTED'
                enrollment.points = None
                enrollment.performance = None
                schedule.status = 'PENDING'

            logger.info("{};{};{};{};{};{};{}".format(
                enrollment.id, enrollment.status, enrollment.points,
                enrollment.performance, enrollment.user.name, enrollment.user.id, enrollment.user.phone))


def processing_billing():
    cont, db = build_container()

    june = datetime.date(2020, 6, 1)
    july = datetime.date(2020, 7, 1)
    august = datetime.date(2020, 8, 1)
    cont.workspace().process_billing_charges(june)
    cont.workspace().process_billing_charges(july)
    cont.workspace().process_billing_charges(august)


def calculate_performance(enroll_id, course_id):
    container, db = build_container()
    enrollment = database.shared_session.query(model.Enrollment).filter(model.Enrollment.id == enroll_id).first()
    course = database.shared_session.query(model.Course).filter(model.Course.id == course_id).first()
    performance = container.course_report().build_performance(course=course,
                                                              enrollment=enrollment)
    # with database.transaction(database.session):
    #     enrollment.performance = performance["percentage"]
    #     enrollment.points = performance["points"]

    logger.info(f'{"learn_duration"},{"duration"},{"type"},{"percentage"}')
    for perf in performance['lessons']:
        for cont in perf['contents']:
            logger.info(f'{cont["learn_duration"]},{cont["duration"]},{cont["type"]},{cont["percentage"]}')


enrolls = {
    "fb0ef5f1-19e7-406c-8934-ee45c655adb8"
}

for en in enrolls:
    calculate_performance(en, "585e69c3-2f96-40a5-b345-d1bfc257f23a")
