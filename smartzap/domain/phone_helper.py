import re


def normalize_brazilian_9_digits(phone: str) -> str:
    phone = re.sub(r'\D', '', phone)
    if phone.startswith('55'):
        # Remove leading '0' in DDD if present
        phone = phone[:2] + phone[3:] if phone[2] == '0' else phone

        # Add '9' for mobile numbers if necessary
        if len(phone) == 12 and phone[4] in '789':
            phone = phone[:4] + '9' + phone[4:]

    return phone
