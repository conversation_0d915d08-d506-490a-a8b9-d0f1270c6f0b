from datetime import datetime, timedelta, timezone

from domain.idle_enrollments.interfaces.idle_enrollments_repository import IIdleEnrollmentsRepository
from domain.model import Enrollment, EnrollmentStatus, Schedule, ScheduleStatus, ScheduleType, Workspace
from sqlalchemy import case, func


class IdleEnrollmentRepository(IIdleEnrollmentsRepository):
    def __init__(self, db):
        self.db = db

    def _get_workspace_limits(self):
        return (
            self.db.query(
                Workspace.id,
                Workspace.enrollment_idle_days_limit
            ).filter(Workspace.enrollment_idle_days_limit.isnot(None), Workspace.enrollment_idle_days_limit > 0).all()
        )

    def _get_latest_activity_subquery(self):
        current_date = datetime.now(timezone.utc)

        workspace_limits = self._get_workspace_limits()
        workspace_min_dates = {
            w.id: (current_date - timedelta(days=w.enrollment_idle_days_limit)) for w in workspace_limits
        }
        workspace_ids = workspace_min_dates.keys()

        return (
            self.db.query(
                Schedule.enrollment_id,
                func.max(Schedule.send_date).label("last_activity_date")
            )
            .filter(
                Schedule.status.in_([ScheduleStatus.sent, ScheduleStatus.delivered, ScheduleStatus.error]),
                Schedule.type.in_([ScheduleType.lesson_content, ScheduleType.course_introduction]),
                Schedule.workspace_id.in_(workspace_ids)
            )
            .group_by(Schedule.enrollment_id, Schedule.workspace_id)
            .having(
                func.max(Schedule.send_date) <
                case(
                    [(Schedule.workspace_id == k, v) for k, v in workspace_min_dates.items()],
                    else_=current_date
                )
            )
            .subquery()
        )

    def get_enrollments_without_recent_activity(self):
        latest_activity_subquery = self._get_latest_activity_subquery()
        return (
            self.db.query(Enrollment)
            .join(latest_activity_subquery, latest_activity_subquery.c.enrollment_id == Enrollment.id)
            .filter(Enrollment.status == EnrollmentStatus.started)
            .all()
        )
