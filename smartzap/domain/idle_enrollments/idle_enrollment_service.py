from domain.idle_enrollments.interfaces.idle_enrollments_repository import IIdleEnrollmentsRepository
from domain.idle_enrollments.interfaces.idle_enrollments_service import IIdleEnrollmentsService
from domain.service import EnrollmentService


class IdleEnrollmentService(IIdleEnrollmentsService):
    def __init__(self, repository: IIdleEnrollmentsRepository, enrollment_repository: EnrollmentService):
        self.repository = repository
        self.enrollment_repository = enrollment_repository

    def cancel_all(self):
        enrollments_without_recent_activity = self.repository.get_enrollments_without_recent_activity()
        self.enrollment_repository.cancel_in_batch(enrollments_without_recent_activity)
