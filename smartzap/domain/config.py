import json
import os
from distutils import util

from domain.utils import format_string_to_list


class Config:
    BASE_DIR = os.path.dirname(os.path.dirname(__file__))

    APP_NAME = os.getenv('APP_NAME', 'smartzap')
    APP_ID = os.getenv('APP_ID', '84d6715e-9b75-436d-ad44-b74c5a7f6729')
    DEBUG = os.getenv('DEBUG', False)
    ENVIRONMENT = os.getenv('ENVIRONMENT', 'development')
    DATABASE_URL = os.environ.get('DATABASE_URL', None)
    KONTENT_DATABASE_URL = os.environ.get('KONTENT_DATABASE_URL')
    MYACC_URL = os.environ.get('MYACC_URL', 'http://myaccount-svc:3000')
    MYACC_V2_URL = os.environ.get('MYACC_V2_URL', 'http://myaccount-v2-svc:3000')
    KEYCLOAK_SERVER_URL = os.environ.get('KEYCLOAK_SERVER_URL')
    KEYCLOAK_REALM = os.environ.get('KEYCLOAK_REALM')
    KEEPS_SECRET_TOKEN_INTEGRATION = os.getenv('KEEPS_SECRET_TOKEN_INTEGRATION')
    KEYCLOAK_CLIENT_ID = os.environ.get('KEYCLOAK_CLIENT_ID', 'smartzap')
    KEYCLOAK_CLIENT_SECRET = os.environ.get('KEYCLOAK_CLIENT_SECRET')
    KONTENT_URL = os.getenv('KONTENT_URL')
    LEARN_ANALYTICS_URL = os.getenv('LEARN_ANALYTICS_URL', "http://analytics-svc:8000/api/v1/")
    GCM_FIREBASE_KEY = os.getenv('GCM_FIREBASE_KEY')
    GCM_FIREBASE_URL = os.getenv('GCM_FIREBASE_URL')
    SMARTVIEW_URL = os.getenv('SMARTVIEW_URL')
    SCHEDULE_CALLBACK_URL = os.getenv('SCHEDULE_CALLBACK_URL')
    DEFAULT_IMAGE_END_COURSE = os.getenv('DEFAULT_IMAGE_END_COURSE', '')
    SCHEDULER_INTERVAL_MINUTES = int(os.getenv('SCHEDULER_INTERVAL_MINUTES', 10))
    COURSE_PROCESSOR_INTERVAL_MINUTES = int(os.getenv('COURSE_PROCESSOR_INTERVAL_MINUTES', 12))
    PREPARE_COURSE_REMINDERS_INTERVAL_MINUTES = int(os.getenv('PREPARE_COURSE_REMINDERS_INTERVAL_MINUTES', 30))
    AWS_BUCKET_NAME = os.getenv('AWS_BUCKET_NAME')
    AWS_BASE_S3_URL = os.getenv('AWS_BASE_S3_URL')
    AWS_CDN_BASE_URL = os.getenv("AWS_CDN_BASE_URL", "https://media-stage.keepsdev.com")
    AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY_ID')
    AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY')
    AWS_REGION_NAME = os.getenv('AWS_REGION_NAME')
    AWS_LAMBDA_ACCESS_KEY_ID = os.getenv('AWS_LAMBDA_ACCESS_KEY_ID')
    AWS_LAMBDA_SECRET_ACCESS_KEY = os.getenv('AWS_LAMBDA_SECRET_ACCESS_KEY')
    AWS_LAMBDA_REGION_NAME = os.getenv('AWS_LAMBDA_REGION_NAME')
    CELERY_BROKER_URL = os.getenv('CELERY_BROKER_URL')
    CELERY_QUEUE = os.getenv('CELERY_QUEUE')
    # Whether to store the task return values or not (tombstones).
    # If you still want to store errors, just not successful return
    # values, you can set task_store_errors_even_if_ignored.
    CELERY_IGNORE_RESULT = bool(util.strtobool(os.getenv("CELERY_IGNORE_RESULT", "True")))

    TWILIO_CHANNELS = os.getenv('TWILIO_CHANNELS')
    TWILIO_FLOWS_CONFIG_FILE = os.getenv(
        'TWILIO_FLOWS_CONFIG_FILE', os.path.join(BASE_DIR, 'twilio_flows.json')
    )

    SMARTZAP_SECRET = '1FDF03A49C02CD5E918A0BA151E15F0663D10B728F0DC42BF7C7B68B1C4275D0'
    TWILIO_API_KEY = os.getenv('TWILIO_API_KEY', '58da72c239014320afb438d12173bfdf')

    EXCHANGE_INTEGRATIONS = os.getenv('EXCHANGE_INTEGRATIONS', 'integrations')

    # CELERY QUEUE INTEGRATIONS
    QUEUE_SMARTZAP_WORKSPACE = os.getenv('QUEUE_SMARTZAP_COMPANY', 'smartzap-companies-stage')
    QUEUE_SMARTZAP_USER = os.getenv('QUEUE_SMARTZAP_USER', 'smartzap-users-stage')

    QUIZ_REPORT_PAGE_SIZE = int(os.getenv('QUIZ_REPORT_PAGE_SIZE', 100))
    BILLING_HOUR = int(os.getenv('BILLING_HOUR', 0))
    ENROLLMENT_WAITING_HOUR = int(os.getenv('ENROLLMENT_WAITING_HOUR', 1))
    CANCEL_IDLE_ENROLLMENTS_HOUR = int(os.getenv('CANCEL_IDLE_ENROLLMENTS_HOUR', 2))
    RECOMMENDATION_HOUR = int(os.getenv('RECOMMENDATION_HOUR', 9))
    ENROLLMENT_LIMIT_DAYS_WAITING = int(os.getenv('ENROLLMENT_LIMIT_DAYS_WAITING', 1))
    MIN_CERTIFICATE_PERCENTAGE = int(os.getenv('MIN_CERTIFICATE_PERCENTAGE', 50))

    ELASTIC_APM = {
        "SERVICE_NAME": os.getenv("ELASTIC_APM_SERVICE_NAME", "smartzap"),
        "SERVER_URL": os.getenv("ELASTIC_APM_SERVER_URL", "https://keeps.apm.us-east-1.aws.cloud.es.io"),
        "SECRET_TOKEN": os.getenv("ELASTIC_APM_SECRET_TOKEN"),
        "ENVIRONMENT": os.getenv("ELASTIC_APM_ENVIRONMENT", "development"),
        "DEBUG": DEBUG if ENVIRONMENT in ['stage', 'staging'] else None,  # True to activate in debug mode for stage
    }

    DEFAULT_CERTIFICATE_LOGO = os.getenv(
        'DEFAULT_CERTIFICATE_LOGO',
        'https://s3.amazonaws.com/keeps.reports/assets/icones3.png'
    )
    AWS_CERTIFICATE_BUCKET_NAME = os.getenv('AWS_CERTIFICATE_BUCKET_NAME', 'smartzap/certificate_template')

    DEFAULT_ANSWERS = {
        'accept': [
            'sim, quero continuar',
            'si, quiero continuar'
        ],
        'reject': [
            'não',
            'no'
        ],
        'content': [
            'quero o conteúdo já', 'próximo conteúdo', 'receber agora',
            'quiero el contenido', 'próximo contenido', 'recíbelo ahora'
        ],
        'finish': [
            'finalizar matrícula',
            'finalizar el curso',
        ],
        'certificate': [
            'receber certificado',
            'recibir certificado'
        ],
        'drop_out': [
            'quero desistir',
            'quiero desistir']
    }

    WHATSAPP_ANSWERS = os.getenv('WHATSAPP_ANSWERS')
    DISCORD_WEBHOOK = os.getenv(
        "DISCORD_WEBHOOK",
        "https://discord.com/api/webhooks/1078713233151627294"
        "/xzv7KYojWypAz3JkLip87osEYdCl1N3i1hJmdDnKqKtNTxjns6M08WcXVuCmXVIwdSJf"
    )

    REPORT_GENERATOR_SERVER_URL = os.getenv(
        "REPORT_GENERATOR_SERVER_URL", "http://report-generator-svc:8080"
    )
    DEFAULT_CERTIFICATE_TEMPLATES = {
        "pt-br": os.getenv(
            "PT_BR_CERTIFICATE_DEFAULT_TEMPLATE_KEY",
            "certificates/certificate_template_pt_br.jasper",
        ),
        "es": os.getenv(
            "ES_CERTIFICATE_DEFAULT_TEMPLATE_KEY",
            "certificates/certificate_template_es.jasper",
        ),
        "en": os.getenv(
            "EN_CERTIFICATE_DEFAULT_TEMPLATE_KEY",
            "certificates/certificate_template_en.jasper",
        )
    }

    DEFAULT_USER_LANGUAGE_ID = os.environ.get("DEFAULT_USER_LANGUAGE_ID", "ea636f50-fdc4-49b0-b2de-9e5905de456b")
    SMARTZAP_USER_ROLE_ID = os.environ.get("SMARTZAP_USER_ROLE_ID", "08404086-5e4e-48c6-91d7-dbeb360c7205")

    # comma-separated string of uuids
    CAIXA_WORKSPACE_IDS = format_string_to_list(os.getenv("CAIXA_WORKSPACE_IDS", ""))

    # Notification
    NOTIFICATION_API_URL = os.environ.get(
        "NOTIFICATION_API_URL", "https://learning-platform-api-stage.keepsdev.com/notification"
    )
    NOTIFICATION_DEFAULT_LANGUAGE = os.environ.get(
        "NOTIFICATION_DEFAULT_LANGUAGE", "pt-BR"
    )
    NOTIFICATION_CAIXA_SMARTZAP_BACKGROUND_TASK_ERROR = os.environ.get(
        "NOTIFICATION_CAIXA_SMARTZAP_BACKGROUND_TASK_ERROR", "caixa_smartzap_background_task_error"
    )

    KEEPS_SUPPORT_EMAIL = os.environ.get("KEEPS_SUPPORT_EMAIL", "<EMAIL>")
    KEEPS_WORKSPACE_ID = os.environ.get("KEEPS_WORKSPACE_ID", "e76b5082-f4fe-4f41-be79-1977840e16a8")
    DEFAULT_TIMEZONE = os.environ.get("DEFAULT_TIMEZONE", "America/Sao_Paulo")

    COURSES_RECOMMENDATION_DELAY = os.environ.get("COURSES_RECOMMENDATION_DELAY", 5)

    COUNTDOWN_TO_SEND_FIRST_MESSAGE = int(os.environ.get("COUNTDOWN_TO_SEND_FIRST_MESSAGE", 60))

    def load_whatsapp_answers(self):
        if self.WHATSAPP_ANSWERS:
            return json.loads(self.WHATSAPP_ANSWERS)
        return self.DEFAULT_ANSWERS


d = Config()
