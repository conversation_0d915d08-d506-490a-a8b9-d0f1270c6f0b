import os
from typing import Dict

import domain.clients.tasks_client
import injector
from domain import client, database, model, schema, service
from domain.clients.learn_analytics_client import LearnAnalyticsClient
from domain.clients.report_generator_client import ReportGeneratorClient
from domain.clients.token_service import TokenService
from domain.discord_webhook_logger import Discord<PERSON>ebhookLogger
from domain.idle_enrollments.idle_enrollment_repository import IdleEnrollmentRepository
from domain.idle_enrollments.idle_enrollment_service import IdleEnrollmentService
from domain.idle_enrollments.interfaces.idle_enrollments_repository import IIdleEnrollmentsRepository
from domain.idle_enrollments.interfaces.idle_enrollments_service import IIdleEnrollmentsService
from domain.services.certificate_service import CertificateService
from domain.services.content_delivery_service import ContentMessageDataService
from domain.services.course_reminders_service import CourseRemindersService
from domain.services.courses_recommendations_service import CoursesRecommendationsService
from domain.services.interfaces.course_reminders_service import ICourseRemindersService
from domain.services.learn_content_validation_service import (
    BaseMimeTypeValidator,
    LearnContentValidationService,
    VideoMimeTypeValidator,
)
from domain.services.message_chatter_bot_v2 import MessageChatterBotV2
from domain.services.message_dispatcher_v2 import MessageDispatcherV2
from domain.services.nps_service import NPSService
from domain.services.report_service import ReportService
from domain.services.schedule_admin_service_v2 import (
    CourseEndHandler,
    CourseIntroductionHandler,
    CourseNpsSurveyHandler,
    CoursesRecommendationHandler,
    ScheduleAdminServiceV2,
    ScheduleHandlerFactory,
)
from domain.services.schedules.course_reminder_handler import CoursesReminderHandler
from domain.services.user_initiated_enrollment_service import UserInitiatedEnrollmentService
from domain.services.user_service import UserServiceV2
from domain.services.workspace_certificate_template_service import WorkspaceCertificateTemplateService
from domain.templates.templates_sids import TEMPLATES_SIDS
from flows.services.flows_service import FlowsService


class Globals:
    def client_id(self):
        return ''

    def user_id(self):
        return ''

    def enrollment_id(self):
        return ''


class Container:
    def __init__(self, config, cache, g_attr=None):
        self.cache = cache
        self.config = config
        self.g_attr = g_attr or Globals()
        self.myacc_client_v2 = client.MyAccountClientV2(self.config.MYACC_V2_URL)
        self.kontent_client = client.KontentClient(self.config.KONTENT_URL,
                                                   self.config.KEEPS_SECRET_TOKEN_INTEGRATION,
                                                   self.cache)

        self.firebase_client = client.FirebaseClient(self.config.GCM_FIREBASE_URL, self.config.GCM_FIREBASE_KEY)
        language_channels = model.LanguageChannels(self.config.TWILIO_CHANNELS)
        self.whatsapp_client = client.WhatsAppTwilioClient(language_channels, TEMPLATES_SIDS)
        self.tasks_client = domain.clients.tasks_client.TasksClient()
        self.flows_service = FlowsService(self.config.TWILIO_FLOWS_CONFIG_FILE)
        self.token_service = TokenService(
            client_id=self.config.KEYCLOAK_CLIENT_ID,
            client_secret=self.config.KEYCLOAK_CLIENT_SECRET,
            keycloak_realm=self.config.KEYCLOAK_REALM,
            keycloak_url=self.config.KEYCLOAK_SERVER_URL
        )
        self.myacc_client = client.MyAccountClient(
            self.config.MYACC_URL, self.config.KEEPS_SECRET_TOKEN_INTEGRATION, self.cache, self.token_service
        )


    @staticmethod
    def _get_templates_path():
        current_di_path = os.path.dirname(os.path.abspath(__file__))
        templates_path = os.path.join(current_di_path, 'templates')
        return templates_path

    @staticmethod
    def _get_workspace_color_path():
        current_di_path = os.path.dirname(os.path.abspath(__file__))
        color_path = os.path.join(current_di_path, 'color_map/workspace_theme_color.json')
        return color_path

    def myacc(self):
        return self.myacc_client

    def myacc_v2(self):
        return self.myacc_client_v2

    def learn_analytics(self):
        return self.learn_analytics_client

    def whatsapp(self):
        return self.whatsapp_client

    def course_category(self):
        return service.CourseCategoryService(database.shared_session)

    def course_workspace(self):
        return service.CourseWorkspaceService(database.shared_session)

    def workspace(self) -> service.WorkspaceService:
        return service.WorkspaceService(database.shared_session)

    def content_type(self):
        return service.ContentTypeService(database.shared_session)

    def course(self):
        return service.CourseService(database.shared_session, self.g_attr.client_id(), self.g_attr.user_id())

    def lesson(self):
        return service.LessonService(database.shared_session)

    def content(self):
        return service.ContentService(database.shared_session)

    def user(self):
        return service.UserService(database.shared_session, self.g_attr.client_id())

    def user_service_v2(self):
        return UserServiceV2(database.shared_session, self.g_attr.client_id(), self.myacc_client)

    def enrollment(self):
        return service.EnrollmentService(database.shared_session, self.schedule(), self.g_attr.client_id())

    def activity(self):
        return service.ActivityService(database.shared_session)

    def feedback(self):
        return service.FeedbackService(database.shared_session)

    def schedule(self):
        return service.ScheduleService(database.shared_session, self.g_attr.client_id())

    def chat(self):
        return service.ChatService(database.shared_session)

    def dispatcher(self):
        templates_path = self._get_templates_path()
        return service.MessageDispatcher(self.whatsapp_client, templates_path, self.config.DEFAULT_IMAGE_END_COURSE)

    def dispatcher_v2(self) -> MessageDispatcherV2:
        return MessageDispatcherV2(self.flows_service)

    def chatter_bot(self):
        templates_path = self._get_templates_path()
        return service.MessageChatterBot(self.enrollment(), self.course(), self.schedule(), self.chat(),
                                         self.whatsapp(), templates_path, self.dispatcher(), self.tasks_client,
                                         self.config.load_whatsapp_answers())

    def learn_content_validation_service(self) -> LearnContentValidationService:
        validators: Dict[str, BaseMimeTypeValidator] = {
            "video/mp4": VideoMimeTypeValidator(),
            "video/3gp": VideoMimeTypeValidator(),
        }
        return LearnContentValidationService(validators)

    def content_message_data_service(self):
        return ContentMessageDataService(
            self.enrollment(),
            self.schedule(),
            self.schedule_admin(),
            self.kontent_client,
            self.learn_content_validation_service(),
            database.shared_session
        )

    def chatter_bot_v2(self):
        return MessageChatterBotV2(
            self.enrollment(),
            self.schedule(),
            self.tasks_client,
            self.nps_service(),
            self.user_initiated_enrollment_service(),
            self.courses_recommendation_service(),
            self.workspace()
        )

    def course_report(self) -> service.CourseReportService:
        return service.CourseReportService(self.schedule(), self.enrollment(), self.kontent_client, self.i18n(), self.config.MIN_CERTIFICATE_PERCENTAGE)

    def course_processor(self) -> service.CourseProcessor:
        return service.CourseProcessor(database.shared_session, self.kontent_client, self.webhook_logger())

    def private_channel_service(self) -> service.PrivateChannelService:
        return service.PrivateChannelService(model.LanguageChannels(self.config.TWILIO_CHANNELS))

    def report_generator_client(self) -> ReportGeneratorClient:
        return ReportGeneratorClient(self.config.REPORT_GENERATOR_SERVER_URL)

    def workspace_certificate_template_service(self) -> WorkspaceCertificateTemplateService:
        return WorkspaceCertificateTemplateService(database.shared_session)

    def certificate_service(self) -> CertificateService:
        return CertificateService(
            self.report_generator_client(),
            self.s3_client(),
            self.myacc_client,
            self._get_workspace_color_path(),
            self.workspace_certificate_template_service(),
            self.config.DEFAULT_CERTIFICATE_TEMPLATES
        )

    def nps_service(self):
        return NPSService(
            database.shared_session,
            self.course(),
            self.enrollment()
        )

    def schedule_admin(self):
        return service.ScheduleAdminService(
            self.schedule(),
            self.course_report(),
            self.enrollment(),
            self.chat(),
            self.config.SMARTVIEW_URL,
            self.config.SMARTZAP_SECRET,
            self.dispatcher(),
            self.config.SCHEDULE_CALLBACK_URL,
            self.firebase_client,
            self.certificate_service(),
            self.private_channel_service()
        )

    def course_introduction_handler(self):
        return CourseIntroductionHandler(self.dispatcher_v2(), self.schedule())

    def course_end_handler(self):
        return CourseEndHandler(
            self.dispatcher_v2(),
            self.schedule(),
            self.enrollment(),
            self.course_report(),
            self.certificate_service()
        )

    def course_nps_survey_handler(self):
        return CourseNpsSurveyHandler(self.dispatcher_v2())

    def courses_recommendation_handler(self):
        return CoursesRecommendationHandler(self.dispatcher_v2())

    def course_reminder_handler(self) -> CoursesReminderHandler:
        return CoursesReminderHandler(self.dispatcher_v2())

    def schedule_handler_factory(self) -> ScheduleHandlerFactory:
        handlers = {
            model.ScheduleType.course_introduction: self.course_introduction_handler(),
            model.ScheduleType.course_end: self.course_end_handler(),
            model.ScheduleType.course_nps_survey: self.course_nps_survey_handler(),
            model.ScheduleType.courses_recommendation: self.courses_recommendation_handler(),
            model.ScheduleType.course_reminder: self.course_reminder_handler()
        }
        return ScheduleHandlerFactory(handlers)

    def schedule_admin_v2(self):
        return ScheduleAdminServiceV2(
            self.schedule(),
            self.course_report(),
            self.enrollment(),
            self.chat(),
            self.dispatcher_v2(),
            self.certificate_service(),
            self.schedule_handler_factory()
        )

    def user_xls_importer(self):
        return service.UserXlsImporter(self.user())

    def s3_client(self):
        return client.S3Client(self.config.AWS_BUCKET_NAME, self.config.AWS_BASE_S3_URL, self.config.AWS_ACCESS_KEY_ID,
                               self.config.AWS_SECRET_ACCESS_KEY, self.config.AWS_REGION_NAME)

    def i18n(self):
        translations_path = os.path.join(self._get_templates_path(), 'templates/translations')
        return client.I18n(translations_path)

    @staticmethod
    def webhook_logger():
        return DiscordWebhookLogger()

    def stats(self):
        _, kontent_db = database.create_engine_and_session(self.config.KONTENT_DATABASE_URL)
        return service.StatsService(self.user(), self.course(), self.content(), kontent_db,
                                    self.s3_client(), schema.DictGenerator(), self.config.QUIZ_REPORT_PAGE_SIZE)

    def client_id(self):
        return self.g_attr.client_id()

    def user_id(self):
        return self.g_attr.user_id()

    def enrollment_id(self):
        return self.g_attr.enrollment_id()

    def notification(self):
        return service.NotificationService(database.shared_session, self.g_attr.client_id(), self.g_attr.user_id())

    def course_backup(self):
        return service.CourseBackupService(self.course(), self.notification(),
                                           self.kontent_client, self.stats(), self.s3_client())

    def token_service(self) -> TokenService:
        return TokenService(
            client_id=self.config.KEYCLOAK_CLIENT_ID,
            client_secret=self.config.KEYCLOAK_CLIENT_SECRET,
            keycloak_realm=self.config.KEYCLOAK_REALM,
            keycloak_url=self.config.KEYCLOAK_SERVER_URL
        )

    def learn_alytics_client(self) -> LearnAnalyticsClient:
        return LearnAnalyticsClient(
            url=self.config.LEARN_ANALYTICS_URL,
            token_service=self.token_service,
            http_timeout_sec=20
        )

    def report_service(self) -> ReportService:
        return ReportService(self.notification(), self.learn_alytics_client(), self.g_attr.client_id())

    def user_initiated_enrollment_service(self) -> UserInitiatedEnrollmentService:
        return UserInitiatedEnrollmentService(self.enrollment(), self.user(), self.course())

    def courses_recommendation_service(self) -> CoursesRecommendationsService:
        return CoursesRecommendationsService(
            database.shared_session,
            self.user(),
            self.schedule_admin_v2(),
            self.schedule(),
            self.enrollment()
        )

    def course_reminders_service(self) -> ICourseRemindersService:
        return CourseRemindersService(
            database.shared_session,
            self.schedule(),
            self.enrollment()
        )

    def idle_enrollments_service(self) -> IIdleEnrollmentsService:
        return IdleEnrollmentService(self.idle_enrollments_repository(), self.enrollment())

    def idle_enrollments_repository(self) -> IIdleEnrollmentsRepository:
        return IdleEnrollmentRepository(database.shared_session)

    def configure(self, binder):
        binder.bind(client.KontentClient, to=self.kontent_client)
        binder.bind(LearnAnalyticsClient, to=injector.CallableProvider(self.learn_alytics_client)),
        binder.bind(service.CourseCategoryService, to=injector.CallableProvider(self.course_category))
        binder.bind(service.CourseWorkspaceService, to=injector.CallableProvider(self.course_workspace))
        binder.bind(service.WorkspaceService, to=injector.CallableProvider(self.workspace))
        binder.bind(service.ContentTypeService, to=injector.CallableProvider(self.content_type))
        binder.bind(service.CourseService, to=injector.CallableProvider(self.course))
        binder.bind(service.LessonService, to=injector.CallableProvider(self.lesson))
        binder.bind(service.ContentService, to=injector.CallableProvider(self.content))
        binder.bind(service.UserService, to=injector.CallableProvider(self.user))
        binder.bind(UserServiceV2, to=injector.CallableProvider(self.user_service_v2))
        binder.bind(service.EnrollmentService, to=injector.CallableProvider(self.enrollment))
        binder.bind(service.ActivityService, to=injector.CallableProvider(self.activity))
        binder.bind(service.FeedbackService, to=injector.CallableProvider(self.feedback))
        binder.bind(service.ScheduleService, to=injector.CallableProvider(self.schedule))
        binder.bind(service.MessageDispatcher, to=injector.CallableProvider(self.dispatcher))
        binder.bind(service.CourseReportService, to=injector.CallableProvider(self.course_report))
        binder.bind(service.ScheduleAdminService, to=injector.CallableProvider(self.schedule_admin))
        binder.bind(service.UserXlsImporter, to=injector.CallableProvider(self.user_xls_importer))
        binder.bind(service.MessageChatterBot, to=injector.CallableProvider(self.chatter_bot))
        binder.bind(MessageChatterBotV2, to=injector.CallableProvider(self.chatter_bot_v2))
        binder.bind(MessageDispatcherV2, to=injector.CallableProvider(self.dispatcher_v2))
        binder.bind(ScheduleAdminServiceV2, to=injector.CallableProvider(self.schedule_admin_v2))
        binder.bind(client.S3Client, to=injector.CallableProvider(self.s3_client))
        binder.bind(service.StatsService, to=injector.CallableProvider(self.stats))
        binder.bind(service.ClientId, to=injector.CallableProvider(self.client_id))
        binder.bind(service.UserId, to=injector.CallableProvider(self.user_id))
        binder.bind(service.EnrollmentId, to=injector.CallableProvider(self.enrollment_id))
        binder.bind(service.NotificationService, to=injector.CallableProvider(self.notification))
        binder.bind(service.CourseBackupService, to=injector.CallableProvider(self.course_backup))
        binder.bind(ContentMessageDataService, to=injector.CallableProvider(self.content_message_data_service))
        binder.bind(NPSService, to=injector.CallableProvider(self.nps_service))
        binder.bind(
            UserInitiatedEnrollmentService, to=injector.CallableProvider(self.user_initiated_enrollment_service)
        )
        binder.bind(
            CoursesRecommendationsService, to=injector.CallableProvider(self.courses_recommendation_service)
        )
