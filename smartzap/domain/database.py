from contextlib import contextmanager

from sqlalchemy import create_engine
from sqlalchemy.orm import scoped_session, sessionmaker

engine_map = {}
session_map = {}
shared_session = None


def _create_engine(database_url, pool_size):
    if database_url in engine_map:
        return engine_map[database_url]

    kwargs = {}
    if not database_url.upper().startswith('SQLITE'):
        kwargs = {'pool_size': pool_size, 'pool_recycle': 3600, 'max_overflow': 10}

    engine = create_engine(database_url, convert_unicode=True, **kwargs)
    engine_map[database_url] = engine
    return engine


def session_factory(database_url):
    if database_url in session_map:
        return session_map[database_url]

    engine = _create_engine(database_url, pool_size=2)

    session = scoped_session(sessionmaker(autocommit=False, autoflush=True, bind=engine))
    session_map[database_url] = session
    return session


def create_engine_and_session(database_url):
    engine = _create_engine(database_url, pool_size=1)
    session = session_factory(database_url)
    return engine, session


@contextmanager
def transaction(db):
    try:
        yield db
        db.commit()
    except Exception:
        db.rollback()
        raise
