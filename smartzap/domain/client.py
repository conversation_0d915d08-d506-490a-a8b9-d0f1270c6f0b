import json
import urllib
import uuid
from typing import Dict

import boto3
import i18n
import requests
import urljoin
from boto3.s3 import transfer
from domain import model
from domain.clients.make_http_client import make_http_client
from domain.clients.token_service import TokenService
from domain.config import Config
from domain.exceptions.twilio_client_exceptions import TemplateNotFound
from jose import J<PERSON><PERSON>rror, jws
from logger import logger
from twilio.base.exceptions import TwilioRestException
from twilio.rest import Client

HTTP_TIMEOUT_SEC = 20
HTTP_TIMEOUT_LONG_SEC = 60


class OpenIDException(Exception):
    def __init__(self, code, message):
        self.code = code
        self.message = message


class MyAccountClient:
    def __init__(self, url, api_token, cache, token_service: TokenService):
        self.cache = cache
        self.url = url
        self.api_token = api_token
        self.smartzap_user_role_id = Config.SMARTZAP_USER_ROLE_ID
        self._url_users = self.url + "/workspaces/{}/users"
        self._url_users_public = self.url + "/users/public/{}"
        self._keycloak_url = Config.KEYCLOAK_SERVER_URL
        self._keycloak_realm = Config.KEYCLOAK_REALM
        self._keycloak_client_id = Config.KEYCLOAK_CLIENT_ID
        self._keycloak_client_secret = Config.KEYCLOAK_CLIENT_SECRET
        self._token_service = token_service

    def create_user(self, user_data: dict, workspace_id: str):
        user_data = user_data.copy()
        user_data["language"] = user_data.get("language", Config.DEFAULT_USER_LANGUAGE_ID)
        payload = {"permissions": [self.smartzap_user_role_id], "users": [user_data]}

        response = requests.post(
            f"{self._url_users.format(workspace_id)}",
            json=payload,
            headers={"Authorization": f"{self._token_service.get_integration_token()}", "x-client": workspace_id},
        )
        if response.status_code == 201:
            return response.json()[0]
        return response.json()

    def get_user_info(self, token):
        try:
            user_id = json.loads(jws.get_unverified_claims(token).decode("utf-8")).get("sub")
        except JWSError:
            return {}
        key_cache = 'myacc-roles-{}'.format(user_id)
        user_info = self.cache.get(key_cache)
        if not user_info:
            url = urljoin.url_path_join(self.url, '/users/info')

            with make_http_client({'Authorization': token, 'Cache-Control': 'no-cache'}) as http:
                response = http.get(url, timeout=HTTP_TIMEOUT_LONG_SEC)
                if response.status_code != 200:
                    return {}
                user_info = response.json()
                self.cache.set(key_cache, user_info, timeout=1800)

        return user_info

    def get_workspace_theme_color(self, workspace_id):
        url = urljoin.url_path_join(self.url, f'/workspaces/{workspace_id}/theme')

        with make_http_client({"Authorization": self.api_token}) as http:
            response = http.get(url, timeout=HTTP_TIMEOUT_SEC)
            if response.status_code != 200:
                return {}
            return response.json()


class MyAccountClientV2:
    def __init__(self, url):
        self.url = url

    def get_user_info(self, token, workspace_id):
        url = urljoin.url_path_join(self.url, '/api/users/info')

        with make_http_client({'Authorization': f'Bearer {token}', 'x-client': workspace_id}) as http:
            response = http.get(url, timeout=HTTP_TIMEOUT_LONG_SEC)
            if response.status_code != 200:
                return {}
            user_info = response.json()

        return user_info


class WhatsAppTwilioClient:
    def __init__(self, channels, templates: Dict[str, Dict[str, Dict[str, str]]]):
        self.channels = channels
        self.templates = templates

    @staticmethod
    def name():
        return 'twilio'

    @staticmethod
    def parse_channel_url(url):
        if not url:
            return '', '', ''

        schema = url.rfind('/')
        if schema >= 0:
            url = url[schema + 1:]
        colon = url.find(':')
        if colon < 0:
            return '', '', ''
        at_sign = url.find('@')
        if at_sign < 0:
            return '', '', ''
        username = urllib.parse.unquote(url[:colon])
        password = urllib.parse.unquote(url[colon + 1:at_sign])
        phone = urllib.parse.unquote(url[at_sign + 1:])
        if not username or not password or not phone:
            return '', '', ''
        return username, password, phone

    def _load_credentials(self, private_channels, lang):
        channel_url = self.channels.load(lang)
        if private_channels:
            channel_url = private_channels.load(lang)
        return self.parse_channel_url(channel_url)

    def get_template_sid(self, account_sid: str, template_key: str, lang: str) -> str:
        account_templates = self.templates[account_sid]
        if not lang:
            return account_templates['pt-br'][template_key]
        return account_templates[lang.lower()][template_key]

    def send_message(
        self,
        phone_number: str,
        template_key: str,
        content_variables: Dict,
        callback_url: str,
        lang: str = 'pt-br',
        private_channels: model.LanguageChannels = None,
        media_url: str = None
    ):
        account_id, account_key, sender_number = self._load_credentials(private_channels, lang)

        try:
            template_sid = self.get_template_sid(account_id, template_key, lang)
        except KeyError:
            raise TemplateNotFound(account_id, template_key, lang)

        params = {
            'from_': f'whatsapp:{sender_number}',
            'content_sid': template_sid,
            'to': f'whatsapp:{phone_number}',
            'status_callback': callback_url,
            'content_variables': json.dumps(content_variables, default=str)
        }
        if media_url:
            params['media_url'] = media_url
        return self._send_to_twilio(account_id, account_key, params)

    def send_media(self, phone_number, media_url, lang='', private_channels=None):
        account_id, account_key, sender_number = self._load_credentials(private_channels, lang)
        params = {'from_': f'whatsapp:{sender_number}', 'to': f'whatsapp:{phone_number}',
                  'media_url': media_url}
        return self._send_to_twilio(account_id, account_key, params)

    @staticmethod
    def _send_to_twilio(account_id, account_key, message):
        try:
            client = Client(account_id, account_key)
            message = client.messages.create(**message)
            return message.sid, True
        except TwilioRestException as e:
            logger.error(str(e))
            return str(e), False


class KontentClient:
    def __init__(self, url, api_token, cache):
        self.cache = cache
        self.url = url
        self.api_token = api_token

    def load_content(self, learn_id):
        url = urljoin.url_path_join(self.url, '/learn-content')
        full_path = f'{url}/{learn_id}?request_cache=false'

        key_cache = f'kontent-learn-{learn_id}'
        learn_info = self.cache.get(key_cache)

        if not learn_info:
            with make_http_client({'Authorization': self.api_token}) as http:
                response = http.get(full_path, timeout=HTTP_TIMEOUT_SEC)
                if response.ok and response.content:
                    learn_info = response.json()
                    self.cache.set(key_cache, learn_info, timeout=600)

        return learn_info

    def delete_content(self, content_id, client_id):
        url = urljoin.url_path_join(self.url, '/learn-content')
        full_path = f'{url}/{content_id}'

        with make_http_client({'Authorization': self.api_token, 'x-client': client_id}) as http:
            response = http.delete(full_path, timeout=HTTP_TIMEOUT_SEC)
            return response

    def load_exam(self, exam_id, enrollment_id, client_id):
        url = urljoin.url_path_join(self.url, '/assessments/exams')
        full_path = f'{url}/{exam_id}?enrollment_id={enrollment_id}'

        with make_http_client({'Authorization': self.api_token, 'x-client': client_id}) as http:
            response = http.get(full_path, timeout=HTTP_TIMEOUT_SEC)
            return response.json()

    def delete_exam(self, exam_id, client_id):
        url = urljoin.url_path_join(self.url, '/assessments/exams')
        full_path = f'{url}/{exam_id}'

        with make_http_client({'Authorization': self.api_token, 'x-client': client_id}) as http:
            response = http.delete(full_path, timeout=HTTP_TIMEOUT_SEC)
            return response

    def save_answers(self, answers, client_id, enrollment_id):
        url = urljoin.url_path_join(self.url, '/assessments/answers')
        full_path = f'{url}?request_cache=false'
        answers['app_id'] = 'smartzap'
        answers['enrollment_id'] = enrollment_id

        with make_http_client({
            'Authorization': self.api_token,
            'Content-Type': 'application/json',
            'x-client': client_id
        }) as http:
            response = http.post(full_path, data=json.dumps(answers), timeout=HTTP_TIMEOUT_SEC)
            return response


class FirebaseClient:
    def __init__(self, url, api_token):
        self.url = url
        self.api_token = api_token

    def short_link(self, link, meta_title, meta_desc, meta_image_url):
        data = {
            'dynamicLinkInfo': {
                'domainUriPrefix': 'https://keeps.page.link',
                'link': link,
                'socialMetaTagInfo': {
                    'socialTitle': meta_title,
                    'socialDescription': meta_desc,
                    'socialImageLink': meta_image_url
                }
            },
            'suffix': {
                'option': 'SHORT'
            }
        }

        with make_http_client() as http:
            url = f'{self.url}/v1/shortLinks?key={self.api_token}'
            response = http.post(url=url, json=data, timeout=HTTP_TIMEOUT_SEC)
            if response.ok:
                result = response.json()
                return result['shortLink']

            return link


class S3Client:
    def __init__(self, bucket_name, s3_url, aws_access_key_id, aws_secret_access_key, aws_region_name):
        self.aws_region_name = aws_region_name
        self.aws_secret_access_key = aws_secret_access_key
        self.aws_access_key_id = aws_access_key_id
        self.bucket_name = bucket_name
        self.url = s3_url

    @staticmethod
    def file_types():
        return {'image': ['png', 'jpg', 'jpeg']}

    def send_file(self, file_obj=None, file_path=None, content_type=None):
        """
        Upload file to AWS S3.

        Is possible send file object (FileStorage type) or the file
        path saved into application folder

        :param file_obj: binary file
        :param file_path: string file path saved locally
        :param content_type: string file content type ('image/jpg', 'video/mp4', ...)
        :return: {name: string, url: string}
        """
        name = str(uuid.uuid4())
        extension = content_type.split('/')[1]
        filename = f'images/{name}.{extension}'

        s3 = boto3.client('s3',
                          aws_access_key_id=self.aws_access_key_id,
                          aws_secret_access_key=self.aws_secret_access_key,
                          region_name=self.aws_region_name)

        config = transfer.TransferConfig(
            multipart_threshold=64 * 1024 * 1024,
            max_concurrency=10,
            num_download_attempts=10,
            multipart_chunksize=16 * 1024 * 1024,
            max_io_queue=10000
        )

        if file_obj:
            s3.upload_fileobj(file_obj, self.bucket_name, filename, Config=config,
                              ExtraArgs={'ACL': 'public-read', 'ContentType': content_type})

        if file_path:
            s3.upload_file(file_path, self.bucket_name, filename, Config=config,
                           ExtraArgs={'ACL': 'public-read', 'ContentType': content_type})

        return {
            'name': filename,
            'url': '{}/{}/{}'.format(self.url, self.bucket_name, filename)
        }

    def send_csv_file(self, file_path=None):
        name = str(uuid.uuid4())
        filename = f'reports/csv/{name}.csv'

        return self.send_file_from_path(file_path, filename, 'text/csv')

    def send_zip_file(self, file_path=None):
        name = str(uuid.uuid4())
        filename = f'backup/zip/{name}.zip'

        return self.send_file_from_path(file_path, filename, 'application/zip')

    def send_file_from_path(self, file_path, filename, content_type):
        s3 = boto3.client('s3',
                          aws_access_key_id=self.aws_access_key_id,
                          aws_secret_access_key=self.aws_secret_access_key,
                          region_name=self.aws_region_name)

        config = transfer.TransferConfig(
            multipart_threshold=64 * 1024 * 1024,
            max_concurrency=10,
            num_download_attempts=10,
            multipart_chunksize=16 * 1024 * 1024,
            max_io_queue=10000
        )

        s3.upload_file(file_path, self.bucket_name, filename, Config=config,
                       ExtraArgs={'ACL': 'public-read', 'ContentType': content_type})
        return {
            'name': filename,
            'url': '{}/{}/{}'.format(self.url, self.bucket_name, filename)
        }


class I18n:
    def __init__(self, languages_path):
        i18n.load_path.append(languages_path)
        i18n.set('filename_format', '{locale}.{format}')
        i18n.set('file_format', 'json')
        i18n.set('skip_locale_root_data', True)
        self.locale = ''

    def set_locale(self, locale):
        self.locale = locale

    def t(self, identifier):
        if self.locale:
            return i18n.t(identifier, locale=self.locale)
        return i18n.t(identifier)
