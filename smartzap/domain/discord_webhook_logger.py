import sys
import traceback
from typing import List, Optional

import flask
import psutil
from discord_webhook import DiscordEmbed, DiscordWebhook
from domain.config import Config
from requests import Timeout
from werkzeug.exceptions import HTTPException

EMBED_FIELD_MAX_VALUE = 1024
HALF_EMBED_FIELD_MAX_VALUE = int(EMBED_FIELD_MAX_VALUE / 2)
TITLE_MAX_CHARACTERS = 256
DESCRIPTION_MAX_CHARACTERS = 4096
RED_COLOR = "ff0000"
WARNING_COLOR = "ffcc00"


class DiscordWebhookLogger:
    def __init__(
        self,
        url: str = Config.DISCORD_WEBHOOK,
    ):
        self.url = url

    def emit_request_exception(self, error: BaseException):
        if Config.DEBUG or "test" in sys.argv:
            return

        try:
            post_data = str(flask.request.json())
        except (TypeError, HTTPException):
            post_data = 'No Data'

        description = f"{error}\n{traceback.format_exc(limit=4)}"
        main_embed = DiscordEmbed(
            title=f"API Error {flask.request.path}",
            description=description[:DESCRIPTION_MAX_CHARACTERS]
        )
        main_embed.set_color(RED_COLOR)
        main_embed = self._add_embed_fields(main_embed, post_data)
        self.execute(main_embed)

    @staticmethod
    def _add_embed_fields(main_embed, post_data):
        main_embed.add_embed_field("Method", flask.request.method if flask.request else 'No Request', inline=False)
        main_embed.add_embed_field("Path", flask.request.path if flask.request else 'No Request', inline=False)
        main_embed.add_embed_field("User", str(flask.g.get('email')), inline=False)
        main_embed.add_embed_field("Workspace", str(flask.request.headers.environ.get('HTTP_X_CLIENT')), inline=False)
        main_embed.add_embed_field("Headers", str(flask.request.headers.environ)[:EMBED_FIELD_MAX_VALUE], inline=False)
        main_embed.add_embed_field("UA", str(flask.request.headers.environ.get('HTTP_USER_AGENT')), inline=False)
        main_embed.add_embed_field(
            "GET Params", str(flask.request.query_string) if flask.request else 'No Request', inline=False
        )
        main_embed.add_embed_field("POST Data", str(post_data) if flask.request else 'No Request', inline=False)
        return main_embed

    def emit_short_message(self, identifier: str, error: BaseException):
        if Config.DEBUG or "test" in sys.argv:
            return
        description = f"{error}\n{traceback.format_exc()}"
        if len(description) > EMBED_FIELD_MAX_VALUE:
            description = description[:HALF_EMBED_FIELD_MAX_VALUE] + ' | ' + description[-HALF_EMBED_FIELD_MAX_VALUE:]
        main_embed = DiscordEmbed(
            title=identifier[:TITLE_MAX_CHARACTERS],
            description=description
        )
        self.execute(main_embed)

    def execute(
        self,
        embed: DiscordEmbed,
        detail_embeds: Optional[List[DiscordEmbed]] = None,
        footer_text: Optional[str] = None
    ):
        embed.set_footer(text=footer_text)
        embed.set_timestamp()
        embed.add_embed_field(name="RAM memory used", value=f"{psutil.virtual_memory()[2]}%", inline=False)
        webhook = DiscordWebhook(url=self.url)
        webhook.add_embed(embed)
        if detail_embeds:
            detail_embed = detail_embeds[0]
            detail_embed.set_color(embed.color)
            webhook.add_embed(detail_embed)
        try:
            webhook.execute()
        except Timeout as error:
            raise RuntimeError(f"Connection to Discord timed out: {error}")
