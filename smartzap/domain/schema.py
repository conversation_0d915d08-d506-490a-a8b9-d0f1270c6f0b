import re
import typing

import marshmallow
import pytz
from domain import database, model
from marshmallow.fields import Method
from marshmallow_sqlalchemy import SQLAlchemyAutoSchema, auto_field
from marshmallow_sqlalchemy.fields import Nested

default_max_name_length = 100
default_max_description_length = 200


class PowerTextValidator(marshmallow.validate.Validator):
    def __init__(self, max_length, validate_linebreak, validate_url, validate_empty):
        self.max_length = max_length
        self.validate_linebreak = validate_linebreak
        self.validate_url = validate_url
        self.validate_empty = validate_empty

    def _check_linebreak(self, value):
        if not value or not self.validate_linebreak:
            return

        original_len = len(value)
        value = value.replace('\r', '').replace('\n', '')
        new_len = len(value)
        if original_len != new_len:
            raise marshmallow.validate.ValidationError('Line breaks are not accepted.')

    def _check_url(self, value):
        if not value or not self.validate_url:
            return

        regex = r"(?i)\b((?:https?://|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}/)(?:[^\s()<>]+|\(([^\s()<>]+|" \
                r"(\([^\s()<>]+\)))*\))+(?:\(([^\s()<>]+|(\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:'\".,<>?«»“”‘’]))"
        url = re.findall(regex, value)
        if url:
            raise marshmallow.validate.ValidationError('Url inside text is not accepted.')

    def _check_empty(self, value):
        if not value and self.validate_empty:
            raise marshmallow.validate.ValidationError('Empty is not accepted.')

    def __call__(self, value) -> typing.Any:
        value_strip = value.strip() if value else None
        self._check_empty(value_strip)
        self._check_url(value_strip)
        self._check_linebreak(value_strip)
        if value and len(value_strip) > self.max_length:
            raise marshmallow.validate.ValidationError(f'Longer than maximum length {self.max_length}.')
        return value


class CourseCategorySchema(SQLAlchemyAutoSchema):
    class Meta:
        include_fk = True
        transient = True
        load_instance = True
        model = model.CourseCategory


class CourseWorkspaceSchema(SQLAlchemyAutoSchema):
    class Meta:
        include_fk = True
        transient = True
        load_instance = True
        model = model.CourseWorkspace


class ContentTypeSchema(SQLAlchemyAutoSchema):
    class Meta:
        include_fk = True
        transient = True
        load_instance = True
        model = model.ContentType


class WorkspaceSchema(SQLAlchemyAutoSchema):
    class Meta:
        include_fk = True
        transient = True
        load_instance = True
        model = model.Workspace
        exclude = ('enrollments',)

    billing_cycle_day = auto_field(validate=marshmallow.validate.Range(min=1, max=31))


class ContentSchema(SQLAlchemyAutoSchema):
    class Meta:
        include_fk = True
        transient = True
        load_instance = True
        model = model.Content

    name = auto_field(validate=PowerTextValidator(default_max_name_length, True, True, True))
    description = auto_field(validate=PowerTextValidator(default_max_description_length, True, True, True))
    dispatch_in = auto_field(validate=marshmallow.validate.Range(min=1))
    type = Nested(ContentTypeSchema, many=False, dump_only=True)

    def __init__(self, *args, **kwargs):
        """
        :param small_size: Indicate the serialization with low data.
        """
        self.small_size = kwargs.pop('small_size', False)
        super().__init__(*args, **kwargs)

    @marshmallow.post_dump()
    def _prepare_dump(self, data, many, **kwargs):
        if self.small_size:
            for key in ['type', 'lesson_id', 'type_id', 'order', 'dispatch_in', 'dispatch_period', 'learn_content']:
                del data[key]
        return data


class LessonSchema(SQLAlchemyAutoSchema):
    class Meta:
        include_fk = True
        transient = True
        load_instance = True
        model = model.Lesson

    name = auto_field(validate=PowerTextValidator(default_max_name_length, True, True, True))
    description = auto_field(validate=PowerTextValidator(default_max_description_length, True, True, False))
    contents = Nested(ContentSchema, many=True, dump_only=True)

    def __init__(self, *args, **kwargs):
        """
        :param small_size: Indicate the serialization with low data.
        """
        self.small_size = kwargs.pop('small_size', False)
        super().__init__(*args, **kwargs)

    @marshmallow.post_dump()
    def _prepare_dump(self, data, many, **kwargs):
        if self.small_size:
            del data['contents']
            del data['order']
            del data['course_id']
        else:
            contents = data.get('contents', [])
            if contents:
                morning = model.ContentPeriod.morning
                data['contents'] = sorted(contents, key=lambda x: (x['dispatch_in'],
                                                                   x['dispatch_period'] != morning,
                                                                   x['dispatch_period']))
        return data


class UserSchema(SQLAlchemyAutoSchema):
    class Meta:
        include_fk = True
        transient = True
        load_instance = True
        model = model.User

    id = auto_field(dump_only=True)


class CourseShortSchema(SQLAlchemyAutoSchema):
    class Meta:
        include_fk = True
        transient = True
        load_instance = True
        model = model.Course
        fields = ("id", "name",)


class EnrollmentSchema(SQLAlchemyAutoSchema):
    class Meta:
        include_fk = True
        transient = True
        load_instance = True
        model = model.Enrollment
        exclude = ('workspace',)

    workspace_id = auto_field(dump_only=True)
    timezone = auto_field(validate=marshmallow.validate.OneOf(pytz.all_timezones))
    user = Nested(UserSchema, many=False, dump_only=True)
    course = Nested(CourseShortSchema, many=False, dump_only=True)
    current_lesson = Nested(LessonSchema(small_size=True), many=False, dump_only=True)
    current_content = Nested(ContentSchema(small_size=True), many=False, dump_only=True)
    progress = auto_field(dump_only=True)
    messages_pending_count = Method("get_messages_pending_count")
    messages_sent_count = Method("get_messages_sent_count")

    @staticmethod
    def get_messages_pending_count(obj):
        return obj.schedules.filter(model.Schedule.status == model.ScheduleStatus.pending).count()

    @staticmethod
    def get_messages_sent_count(obj):
        return obj.schedules.filter(model.Schedule.status == model.ScheduleStatus.delivered).count()


class CourseSchema(SQLAlchemyAutoSchema):
    class Meta:
        include_fk = True
        transient = True
        load_instance = True
        model = model.Course
        exclude = ("user_creator_id", 'workspace_owner_id')

    name = auto_field(validate=PowerTextValidator(default_max_name_length, True, True, True))
    description = auto_field(validate=PowerTextValidator(default_max_description_length, True, True, True))
    duration = auto_field(dump_only=True)
    points = auto_field(dump_only=True)
    quiz_performance_weight = auto_field(validate=marshmallow.validate.Range(min=0, max=10))
    content_performance_weight = auto_field(validate=marshmallow.validate.Range(min=0, max=10))
    category = Nested(CourseCategorySchema, many=False, dump_only=True)
    workspace_owner = Nested(WorkspaceSchema, many=False, dump_only=True)
    user_creator = Nested(UserSchema, many=False, dump_only=True)

    @marshmallow.pre_load
    def validate_lang(self, data, **kwargs):
        lang = data.get('lang', '')
        if 'lang' in data and lang.lower() not in model.accepted_course_lang:
            raise marshmallow.ValidationError(f'Invalid Course language. Accepts: {model.accepted_course_lang}')
        quiz_weight = data.get('quiz_performance_weight')
        content_weight = data.get('content_performance_weight')
        if quiz_weight and content_weight and content_weight + quiz_weight != 10:
            raise marshmallow.ValidationError('Invalid content and quiz weights. '
                                              'Content weight + Quiz weight must be igual 10.')

        if quiz_weight and not content_weight:
            data['content_performance_weight'] = 10 - quiz_weight
        if content_weight and not quiz_weight:
            data['quiz_performance_weight'] = 10 - content_weight
        return data

    @marshmallow.post_dump()
    def _prepare_totals(self, data, many, **kwargs):
        course_id = data['id']
        query = database.shared_session.query(model.Enrollment).filter(model.Enrollment.course_id == course_id)
        enrollments_count = query.count()
        enrolments_completed_count = query.filter(model.Enrollment.status == model.EnrollmentStatus.completed).count()
        lessons_count = database.shared_session.query(model.Lesson).filter(model.Lesson.course_id == course_id).count()
        data['total_users_enrolled'] = enrollments_count
        data['total_users_completed'] = enrolments_completed_count
        data['total_lessons'] = lessons_count
        return data


class UserEnrollmentSchema(UserSchema):
    class Meta(UserSchema.Meta):
        load_instance = False
    course_id = marshmallow.fields.String()
    start_date = marshmallow.fields.Date()
    timezone = marshmallow.fields.String(validate=marshmallow.validate.OneOf(pytz.all_timezones))


class EnrollmentBatchSchema(EnrollmentSchema):
    class Meta(EnrollmentSchema.Meta):
        load_instance = False
        exclude = ('user_id', 'workspace_id')

    users = marshmallow.fields.List(marshmallow.fields.String)


class ActivitySchema(SQLAlchemyAutoSchema):
    class Meta:
        include_fk = True
        transient = True
        load_instance = True
        model = model.Activity

    enrollment_id = auto_field(dump_only=True)


class FeedbackSchema(SQLAlchemyAutoSchema):
    class Meta:
        include_fk = True
        transient = True
        load_instance = True
        model = model.Feedback


class ScheduleSchema(SQLAlchemyAutoSchema):
    class Meta:
        include_fk = True
        transient = True
        load_instance = True
        model = model.Schedule


class ReScheduleSchema(marshmallow.Schema):
    send_date = marshmallow.fields.Date()
    period = marshmallow.fields.String(validate=marshmallow.validate.OneOf(model.ContentPeriod.all))


class UserCsvSchema(UserSchema):
    class Meta(UserSchema.Meta):
        datetimeformat = '%Y/%m/%d %H:%M:%S'


class EnrollmentCsvSchema(EnrollmentSchema):
    class Meta(UserSchema.Meta):
        dateformat = '%Y/%m/%d'

    @marshmallow.post_dump()
    def _convert(self, data, many, **kwargs):
        new_data = {}
        new_data['id'] = data['user_id']
        new_data['name'] = data['user']['name']
        new_data['phone'] = data['user']['phone']
        new_data['email'] = data['user']['email']
        new_data['tags'] = data['user']['tags'] if data['user'].get('tags') else None
        new_data['started_at'] = data['start_date']
        new_data['finished_at'] = data['end_date']
        new_data['performance'] = data['performance']
        new_data['status'] = data['status']
        new_data['current_lesson'] = data['current_lesson']['name'] if data.get('current_lesson') else None
        new_data['current_content'] = data['current_content']['name'] if data.get('current_content') else None
        return new_data


class ActivityCsvSchema(ActivitySchema):
    class Meta(ActivitySchema.Meta):
        datetimeformat = '%Y/%m/%d %H:%M:%S'
        exclude = ('id', 'user_id', 'content_id')


class CsvResultSchema(marshmallow.Schema):
    url = marshmallow.fields.String()


class ChargeSchema(SQLAlchemyAutoSchema):
    class Meta:
        include_fk = True
        transient = True
        load_instance = True
        model = model.Billing
        exclude = ('workspace_id',)


class NotificationTypeSchema(SQLAlchemyAutoSchema):
    class Meta:
        include_fk = True
        transient = True
        load_instance = True
        model = model.NotificationType


class NotificationSchema(SQLAlchemyAutoSchema):
    class Meta:
        include_fk = True
        transient = True
        load_instance = True
        model = model.Notification

    type = Nested(NotificationTypeSchema, many=False, dump_only=True)


class BillingPeriodSchema(marshmallow.Schema):
    start_at = marshmallow.fields.Date()
    end_at = marshmallow.fields.Date()


class BillingSchema(marshmallow.Schema):
    monthly_plan = marshmallow.fields.Integer()
    available_zaps = marshmallow.fields.Integer()
    zaps_sent = marshmallow.fields.Integer()
    current_billing = marshmallow.fields.Nested(BillingPeriodSchema, dump_only=True)
    charges = marshmallow.fields.List(marshmallow.fields.Nested(ChargeSchema, dump_only=True))


class EnrollmentTrackingSchema(marshmallow.Schema):
    content = marshmallow.fields.Nested(ContentSchema)
    first_access = marshmallow.fields.DateTime()
    last_access = marshmallow.fields.DateTime()
    duration = marshmallow.fields.Integer()
    learn_duration = marshmallow.fields.Integer()
    total_questions = marshmallow.fields.Integer()
    total_correct_answers = marshmallow.fields.Integer()
    schedule_status = marshmallow.fields.String()


class UserWithEmailRequiredSchema(UserSchema):
    class Meta:
        include_fk = True
        transient = True
        load_instance = False
        model = model.User

    id = auto_field(dump_only=True)
    email = marshmallow.fields.Email(required=True)
    name = marshmallow.fields.Str(required=True)


class UserResponseSchema(marshmallow.Schema):
    id = marshmallow.fields.UUID(required=True)
    name = marshmallow.fields.String(required=True)
    email = marshmallow.fields.Email(required=True)
    profiles = marshmallow.fields.List(marshmallow.fields.Dict())
    language = marshmallow.fields.Dict()
    status = marshmallow.fields.Boolean()
    time_zone = marshmallow.fields.String()
    created = marshmallow.fields.Boolean()


class EnrollmentStatusCountSchema(marshmallow.Schema):
    started_count = marshmallow.fields.Integer()
    waiting_count = marshmallow.fields.Integer()


class EnrollmentMessagesPendingCountSchema(marshmallow.Schema):
    message_pending_count = marshmallow.fields.Integer()


class MessagesSentCountSchema(marshmallow.Schema):
    messages_sent_count = marshmallow.fields.Integer()


class UsersCountSchema(marshmallow.Schema):
    users_count = marshmallow.fields.Integer()


class IdsSchema(marshmallow.Schema):
    ids = marshmallow.fields.List(marshmallow.fields.String)


class DictGenerator:
    def __init__(self):
        self.user_csv = UserCsvSchema()
        self.enrollment_csv = EnrollmentCsvSchema()
        self.activity_csv = ActivityCsvSchema()

    def user_to_dict(self, user):
        return self.user_csv.dump(user, many=False)

    def enrollment_to_dict(self, enrollment):
        return self.enrollment_csv.dump(enrollment, many=False)

    def activity_to_dict(self, activity):
        return self.activity_csv.dump(activity, many=False)


def list_schema(schema, name_prefix=''):
    class ListSchema(marshmallow.Schema):
        count = marshmallow.fields.Integer()
        result = marshmallow.fields.Nested(schema, many=True, dump_only=True)

    ListSchema.__name__ = f'List{name_prefix}Schema'
    return ListSchema


def build_list(data):
    return {'count': len(data), 'result': data}


def paginate_schema(schema, name_prefix=''):
    class PaginationSchema(marshmallow.Schema):
        page = marshmallow.fields.Integer()
        per_page = marshmallow.fields.Integer()
        total_pages = marshmallow.fields.Integer()
        count = marshmallow.fields.Integer()
        result = marshmallow.fields.Nested(schema, many=True, dump_only=True)

        @marshmallow.post_dump
        def remove_none_values(self, data, many, **kwargs):
            return {
                key: value for key, value in data.items() if value or key == 'result'
            }

    PaginationSchema.__name__ = f'Pagination{name_prefix}{schema.__name__}'
    return PaginationSchema


def build_pagination(data, pagination):
    return {'page': pagination.page, 'per_page': pagination.per_page, 'total_pages': pagination.total_pages,
            'count': pagination.count, 'result': data}


user_schema = UserSchema()
user_enrollment_schema = UserEnrollmentSchema()
enrollment_schema = EnrollmentSchema()
enrollment_batch_schema = EnrollmentBatchSchema()
