import re
from datetime import datetime, timedelta

import pytz


def round_performance(performance):
    return round(performance, 3)


def clean_for_url_or_path(value: str) -> str:
    # Allow only letters, numbers, dashes, underscores, and dots
    value = value.replace(" ", "_")
    return re.sub(r'[^A-Za-z0-9_-]', '', value)


def format_string_to_list(input_string: str) -> list:
    if not input_string:
        return []

    return [item.strip() for item in input_string.split(",")]


class BusinessHourUtils:
    BUSINESS_HOURS_START = 9
    BUSINESS_HOURS_END = 18
    BUSINESS_DAYS = {0, 1, 2, 3, 4}

    @staticmethod
    def get_next_business_hour(now: datetime, user_timezone: str) -> datetime:
        """
        Determines the next available business hour for message sending based on the user's timezone.

        :param now: Current datetime in UTC.
        :param user_timezone: User's timezone string (e.g., 'America/New_York').
        :return: Datetime for the next available business hour in user's local timezone.
        """
        user_tz = pytz.timezone(user_timezone) if user_timezone else pytz.UTC
        now_local = now.astimezone(user_tz)

        if (
            now_local.hour >= BusinessHourUtils.BUSINESS_HOURS_END
            or now_local.hour < BusinessHourUtils.BUSINESS_HOURS_START
        ):
            next_day = now_local + timedelta(days=1)
            while next_day.weekday() not in BusinessHourUtils.BUSINESS_DAYS:
                next_day += timedelta(days=1)

            return user_tz.localize(
                datetime(next_day.year, next_day.month, next_day.day, BusinessHourUtils.BUSINESS_HOURS_START)
            )

        next_hour = now_local + timedelta(minutes=5)
        return next_hour
