import datetime
import json
import uuid
from typing import Optional

import sqlalchemy
from domain import phone_helper
from sqlalchemy import Boolean, Column, Date, DateTime, Float, ForeignKey, Integer, String, Text, UniqueConstraint
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, validates

Base = declarative_base()


default_lang = 'pt-br'
accepted_course_lang = ['pt-br', 'es']
languages_description = [
    {'name': 'Português - Brasil', 'value': 'pt-br'},
    {'name': 'Español', 'value': 'es'}
]
SET_NULL = 'SET NULL'


class CourseStatus:
    creating = 'CREATING'
    processing = 'PROCESSING'
    reviewing = 'REVIEWING'
    finished = 'FINISHED'
    deleting = 'DELETING'

    _all = [creating, processing, reviewing, finished, deleting]

    @classmethod
    def is_valid(cls, status):
        return status in cls._all


class ScheduleStatus:
    pending = 'PENDING'
    sent = 'SENT'
    delivered = 'DELIVERED'
    error = 'ERROR'
    failed = 'FAILED'

    _all = [pending, sent, delivered, error, failed]

    @classmethod
    def is_valid(cls, status):
        return status in cls._all


class ScheduleType:
    course_introduction = 'COURSE_INTRODUCTION'
    lesson_content = 'LESSON_CONTENT'
    course_end = 'COURSE_END'
    course_nps_survey = 'COURSE_NPS_SURVEY'
    courses_recommendation = 'COURSES_RECOMMENDATION'
    course_reminder = 'COURSE_REMINDER'

    _all = [course_introduction, lesson_content, course_end, course_nps_survey, courses_recommendation, course_reminder]

    @classmethod
    def is_valid(cls, schedule_type):
        return schedule_type in cls._all

    @classmethod
    def is_content(cls, schedule_type):
        return schedule_type == cls.lesson_content


class ChatType:
    course_introduction = ScheduleType.course_introduction
    lesson_content = ScheduleType.lesson_content
    course_end = ScheduleType.course_end
    course_welcome = 'COURSE_WELCOME'
    answer_not_found = 'ANSWER_NOT_FOUND'
    enrollment_rejected = 'ENROLLMENT_REJECTED'
    enrollment_not_found = 'ENROLLMENT_NOT_FOUND'
    enrollment_started = 'ENROLLMENT_STARTED'
    enrollment_wrong_answer = 'ENROLLMENT_WRONG_ANSWER'
    content_anticipated = 'CONTENT_ANTICIPATED'
    content_wrong_answer = 'CONTENT_WRONG_ANSWER'
    content_nothing_pending = 'CONTENT_NOTHING_PENDING'
    content_nothing_to_do = 'CONTENT_NOTHING_TO_DO'
    finish_course_anticipated = 'FINISH_COURSE_ANTICIPATED'
    finish_course_content_pending = 'FINISH_COURSE_CONTENT_PENDING'
    finish_course_nothing_to_do = 'FINISH_COURSE_NOTHING_TO_DO'
    finish_course_wrong_answer = 'FINISH_COURSE_WRONG_ANSWER'
    certificate_not_available = 'CERTIFICATE_NOT_AVAILABLE'
    certificate = 'CERTIFICATE'
    all = [course_introduction, lesson_content, course_end, course_welcome, enrollment_rejected,
           enrollment_wrong_answer, content_anticipated, content_wrong_answer, content_nothing_pending,
           finish_course_anticipated, finish_course_content_pending, finish_course_wrong_answer,
           enrollment_not_found, enrollment_started, answer_not_found, content_nothing_to_do,
           finish_course_nothing_to_do, certificate_not_available, certificate]


class ChatAnswerType:
    accept = 'ACCEPT'
    reject = 'REJECT'
    content = 'CONTENT'
    finish = 'FINISH'
    certificate = 'CERTIFICATE'
    drop_out = 'DROP_OUT'

    all = [accept, reject, content, finish, certificate, drop_out]


class ContentPeriod:
    morning = 'MORNING'
    afternoon = 'AFTERNOON'
    night = 'NIGHT'

    all = [morning, afternoon, night]

    @classmethod
    def is_valid(cls, period):
        return period in cls.all

    @classmethod
    def morning_time(cls):
        return datetime.time(hour=9, minute=0, second=0)

    @classmethod
    def afternoon_time(cls):
        return datetime.time(hour=14, minute=0, second=0)

    @classmethod
    def night_time(cls):
        return datetime.time(hour=19, minute=0, second=0)

    @classmethod
    def content_time(cls, period):
        periods = {
            ContentPeriod.morning: ContentPeriod.morning_time(),
            ContentPeriod.afternoon: ContentPeriod.afternoon_time(),
            ContentPeriod.night: ContentPeriod.night_time(),
        }
        return periods.get(period, datetime.time(hour=9, minute=0, second=0))


class Action:
    watch = 'WATCH'
    read = 'READ'
    view = 'VIEW'
    listen = 'LISTEN'

    _all = [watch, read, view, listen]

    @classmethod
    def is_valid(cls, status):
        return status in cls._all


class EnrollmentStatus:
    waiting = 'WAITING'
    started = 'STARTED'
    completed = 'COMPLETED'
    refused = 'REFUSED'
    canceled = 'CANCELED'
    dropout = 'DROPOUT'

    _all = [waiting, started, completed, refused, canceled, dropout]

    @classmethod
    def is_valid(cls, status):
        return status in cls._all


class Timestamp:
    created = Column(DateTime, default=lambda: datetime.datetime.utcnow(), nullable=True)
    updated = Column(DateTime, default=lambda: datetime.datetime.utcnow(), nullable=True)


@sqlalchemy.event.listens_for(Base, 'attribute_instrument')
def configure_string_listener(class_, key, inst):
    if not hasattr(inst.property, 'columns'):
        return

    @sqlalchemy.event.listens_for(inst, 'set', retval=True)
    def set_(instance, value, oldvalue, initiator):
        column_type = inst.property.columns[0].type.__class__
        if column_type == String and value:
            return value.strip()
        return value


@sqlalchemy.event.listens_for(Timestamp, 'before_update', propagate=True)
def timestamp_before_update(mapper, connection, target):
    target.updated = datetime.datetime.utcnow()


class CourseCategory(Base, Timestamp):
    __tablename__ = 'course_category'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, nullable=False)
    description = Column(String)


class CourseWorkspace(Base, Timestamp):
    __tablename__ = 'course_workspace'
    __table_args__ = (UniqueConstraint('workspace_id', 'course_id', name='workspace_course_uc'),)

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    workspace_id = Column(String(36), ForeignKey('workspace.id'), primary_key=True)
    course_id = Column(String(36), ForeignKey('course.id'), primary_key=True)


class UserWorkspace(Base, Timestamp):
    __tablename__ = 'user_workspace'
    __table_args__ = (UniqueConstraint('workspace_id', 'user_id', name='workspace_user_uc'),)

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    workspace_id = Column(String(36), ForeignKey('workspace.id'), primary_key=True)
    user_id = Column(String(36), ForeignKey('user.id'), primary_key=True)


class Workspace(Base, Timestamp):
    __tablename__ = 'workspace'

    id = Column(String(36), primary_key=True)
    name = Column(String, nullable=False)
    logo_url = Column(String, nullable=True)
    user_token_expiration = Column(Integer, default=5)
    end_course_schedule = Column(Integer, default=6)
    monthly_plan = Column(Integer, default=0)
    billing_cycle_day = Column(Integer, default=1)
    private_channel = Column(String, nullable=True)
    messages_by_twilio_studio = Column(Boolean, default=False)
    messages_content_embed = Column(Boolean, default=False)
    send_courses_recommendation_message = Column(Boolean, default=False)
    courses_portal_url = Column(String, nullable=True)
    send_course_reminder_message = Column(Boolean, default=False)
    enrollment_idle_days_limit = Column(Integer, default=30, nullable=True)

    courses = relationship('Course', secondary='course_workspace', back_populates='companies')

    @validates('billing_cycle_day')
    def validate_billing_cycle_day(self, _, cycle_day):
        assert 1 <= cycle_day <= 31, 'Invalid Billing Cycle Day (1-31)'
        return cycle_day

    @validates('private_channel')
    def validate_private_channel(self, _, private_channel):
        if not private_channel:
            return private_channel

        colon = private_channel.find(':')
        at_sign = private_channel.find('@')
        assert 0 < colon < at_sign, 'Invalid Private Channel: User:Pass@+************'
        return private_channel


class Billing(Base, Timestamp):
    __tablename__ = 'billing'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    workspace_id = Column(String(36), ForeignKey('workspace.id'), nullable=False)
    start_at = Column(Date)
    end_at = Column(Date)
    monthly_plan = Column(Integer)
    billing_cycle_day = Column(Integer)
    used = Column(Integer)
    balance = Column(Integer)
    __table_args__ = (
        UniqueConstraint('start_at', 'end_at', 'workspace_id', name='_start_at__end_at__workspace_id__unique'),
    )


class Course(Base, Timestamp):
    __tablename__ = 'course'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    category_id = Column(String(36), ForeignKey('course_category.id'), nullable=False)
    name = Column(String, nullable=False)
    description = Column(String)
    holder_image = Column(String)
    thumb_image = Column(String)
    duration = Column(Float)
    points = Column(Float)
    is_active = Column(Boolean)
    status = Column(String, default=CourseStatus.creating)
    lang = Column(String)
    quiz_performance_weight = Column(Integer, default=5)
    content_performance_weight = Column(Integer, default=5)

    workspace_owner_id = Column(String(36), ForeignKey('workspace.id'), nullable=False)
    user_creator_id = Column(String(36), ForeignKey('user.id'), nullable=False)

    lessons = relationship('Lesson', backref='course', cascade='all,delete', order_by='Lesson.order')
    enrollments = relationship('Enrollment', backref='course', cascade='all,delete')
    category = relationship('CourseCategory', backref='course')
    workspace_owner = relationship('Workspace', backref='course')
    user_creator = relationship('User', backref='course')
    allow_content_anticipation = Column(Boolean, default=True)
    allow_drop_out = Column(Boolean, default=False)
    disable_send_certificate = Column(Boolean, default=False)
    allow_nps_survey = Column(Boolean, default=True)
    nps_score = Column(Integer)

    companies = relationship('Workspace', secondary='course_workspace', back_populates='courses')

    @validates('status')
    def validate_status(self, _, status):
        assert CourseStatus.is_valid(status), 'Invalid Course status.'
        return status

    @validates('quiz_performance_weight')
    def validate_quiz_performance_weight(self, _, quiz_performance_weight):
        assert 0 <= quiz_performance_weight <= 10, 'Invalid Quiz Performance Weight. Must be between 0-10'
        return quiz_performance_weight

    @validates('content_performance_weight')
    def validate_content_performance_weight(self, _, content_performance_weight):
        assert 0 <= content_performance_weight <= 10, 'Invalid Content Performance Weight. Must be between 0-10'
        return content_performance_weight

    def is_processing(self):
        return self.status == CourseStatus.processing

    def is_immutable(self):
        return self.status == CourseStatus.processing or self.status == CourseStatus.deleting

    def total_performance_weight(self):
        return self.quiz_performance_weight + self.content_performance_weight


class Lesson(Base, Timestamp):
    __tablename__ = 'lesson'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    course_id = Column(String(36), ForeignKey('course.id'), nullable=False)
    name = Column(String, nullable=False)
    description = Column(String)
    order = Column(Integer)

    contents = relationship('Content', backref='lesson', cascade='all,delete', order_by='Content.order')


class ContentType(Base, Timestamp):
    __tablename__ = 'content_type'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, nullable=False)
    description = Column(String)
    image_url = Column(String)


class Content(Base, Timestamp):
    __tablename__ = 'content'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    lesson_id = Column(String(36), ForeignKey('lesson.id'), nullable=False)
    type_id = Column(String(36), ForeignKey('content_type.id'))
    name = Column(String, nullable=False)
    description = Column(String)
    order = Column(Integer)
    dispatch_in = Column(Integer)
    dispatch_period = Column(String, default=ContentPeriod.morning)
    learn_content = Column(String(36))

    type = relationship('ContentType', backref='contents', lazy='joined')

    @validates('dispatch_period')
    def validate_dispatch_period(self, _, period):
        assert ContentPeriod.is_valid(period), 'Invalid Content dispatch period.'
        return period

    def get_dispatch_time(self):
        return ContentPeriod.content_time(self.dispatch_period)


class User(Base, Timestamp):
    __tablename__ = 'user'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, nullable=False)
    phone = Column(String, nullable=False, unique=True)
    email = Column(String)
    cpf = Column(String)
    ein = Column(String)
    birthday = Column(Date)
    director = Column(String)
    manager = Column(String)
    area_of_activity = Column(String)
    job = Column(String)
    tags = Column(String)
    avatar = Column(String)
    sync_check = Column(String)
    my_account_user = Column(Boolean, default=False)

    @validates('phone')
    def validate_phone(self, _, phone):
        phone = phone_helper.normalize_brazilian_9_digits(phone)
        return phone


class Enrollment(Base, Timestamp):
    __tablename__ = 'enrollment'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey('user.id'), nullable=False)
    course_id = Column(String(36), ForeignKey('course.id'), nullable=False)
    workspace_id = Column(String(36), ForeignKey('workspace.id'), nullable=False)
    current_lesson_id = Column(String(36), ForeignKey('lesson.id'), nullable=True)
    current_content_id = Column(String(36), ForeignKey('content.id', ondelete=SET_NULL), nullable=True)
    points = Column(Float)
    performance = Column(Float)
    start_date = Column(Date)
    end_date = Column(Date)
    timezone = Column(String)
    status = Column(String, default=EnrollmentStatus.waiting)
    progress = Column(Integer, default=0)
    nps_score: Optional[int] = Column(Integer)
    certificate_url = Column(String, nullable=True)

    user = relationship('User', backref='enrollment', lazy='joined')
    schedules = relationship('Schedule', lazy='dynamic', backref='enrollment')
    current_lesson = relationship('Lesson', backref='enrollment', lazy='joined')
    current_content = relationship('Content', backref='enrollment', lazy='joined')

    activities = relationship('Activity', backref='enrollment', cascade='all,delete')
    workspace = relationship('Workspace', backref='enrollments')

    @validates('status')
    def validate_status(self, _, status):
        assert EnrollmentStatus.is_valid(status), 'Invalid Enrollment status.'
        return status

    def can_change_status(self, status):
        if self.status == status:
            return True

        routes = {
            EnrollmentStatus.waiting: [EnrollmentStatus.started, EnrollmentStatus.refused, EnrollmentStatus.canceled],
            EnrollmentStatus.started: [EnrollmentStatus.completed, EnrollmentStatus.canceled],
            EnrollmentStatus.refused: [],
            EnrollmentStatus.completed: [],
        }
        possible_new_status = routes.get(self.status, [])
        return status in possible_new_status


class Activity(Base, Timestamp):
    __tablename__ = 'activity'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    enrollment_id = Column(String(36), ForeignKey('enrollment.id', ondelete=SET_NULL), nullable=True)
    content_id = Column(String(36), ForeignKey('content.id', ondelete=SET_NULL), nullable=True)
    user_id = Column(String(36), nullable=False)
    action = Column(String)
    start_at = Column(DateTime)
    stop_at = Column(DateTime)
    duration = Column(Integer)

    @validates('action')
    def validate_action(self, _, action):
        assert Action.is_valid(action), 'Invalid Activity action.'
        return action


class Feedback(Base, Timestamp):
    __tablename__ = 'feedback'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    workspace_id = Column(String(36), ForeignKey('workspace.id'), nullable=False)
    tag = Column(String)
    message = Column(String)
    scope = Column(String)


class Schedule(Base, Timestamp):
    __tablename__ = 'schedule'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    enrollment_id = Column(String(36), ForeignKey('enrollment.id', ondelete=SET_NULL), nullable=True)
    workspace_id = Column(String(36), ForeignKey('workspace.id'), nullable=False)
    message_id = Column(String)
    send_date = Column(DateTime)
    status = Column(String, default=ScheduleStatus.pending)
    reference_id = Column(String(36))
    triggered_flow_id = Column(String(34))
    type = Column(String, nullable=False)
    lang = Column(String, nullable=False)
    timezone = Column(String, nullable=False)
    anticipated = Column(Boolean, default=False)
    external_sender = Column(Boolean, default=False)

    workspace = relationship('Workspace', backref='schedules', lazy='joined')

    @validates('status')
    def validate_status(self, _, status):
        assert ScheduleStatus.is_valid(status), 'Invalid Schedule status.'
        return status

    @validates('type')
    def validate_action(self, _, schedule_type):
        assert ScheduleType.is_valid(schedule_type), 'Invalid Schedule type.'
        return schedule_type

    def is_sent(self):
        return self.status == ScheduleStatus.sent or self.status == ScheduleStatus.delivered


class Chat(Base, Timestamp):
    __tablename__ = 'chat'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    enrollment_id = Column(String(36), ForeignKey('enrollment.id', ondelete='cascade'), nullable=False)
    content_id = Column(String(36), nullable=True)
    type = Column(String, nullable=False)
    replied = Column(Boolean, default=False)
    user_answer = Column(String, nullable=True)

    def can_reply_message(self, reply_type):
        return self.type != reply_type

    def is_in_24_window(self):
        if not self.created:
            return False

        delta = datetime.datetime.utcnow() - self.created
        return delta <= datetime.timedelta(hours=23, minutes=50)


class NotificationType(Base, Timestamp):
    __tablename__ = 'notification_type'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    type = Column(String, nullable=False)
    action = Column(String)
    image_url = Column(String)


class Notification(Base, Timestamp):
    __tablename__ = 'notification'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey('user.id', ondelete='cascade'), nullable=False)
    workspace_id = Column(String(36), ForeignKey('workspace.id'), nullable=False)
    type_id = Column(String(36), ForeignKey('notification_type.id'))
    message = Column(String, nullable=False)
    content = Column(String)
    read = Column(Boolean, default=False)

    type = relationship('NotificationType', backref='notifications', lazy='joined')
    user = relationship('User', backref='notifications')


class WorkspaceCertificateTemplate(Base):
    __tablename__ = 'workspace_certificate_template'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    workspace_id = Column(String(36), ForeignKey('workspace.id'), nullable=False)
    language = Column(String(20), nullable=False)
    certificate_template_key = Column(Text, nullable=False)

    workspace = relationship('Workspace', backref='certificates')

    __table_args__ = (
        UniqueConstraint('workspace_id', 'language', name='workspace_certificate_template_uc'),
    )

    @validates('language')
    def validate_status(self, _, language):
        assert language in accepted_course_lang, 'Invalid Language'

    def __repr__(self):
        return f"<WorkspaceCertificate(id={self.id}, workspace_id={self.workspace_id}, language={self.language})>"


class LanguageChannels:
    def __init__(self, languages_content):
        self.languages = {}
        if not languages_content:
            return
        self._build_languages_from_json_content(languages_content)

    def _build_languages_from_json_content(self, languages_content):
        try:
            languages = json.loads(languages_content)
        except json.JSONDecodeError:
            languages = {}
        for key, token in languages.items():
            self.languages[key.lower()] = token

    def load(self, language):
        if not language:
            return self._load_default()

        lang = language.lower()
        channel = self._load_full_or_short(lang)
        return channel or self._load_default()

    def _load_default(self):
        lang = self.languages.get('default', 'pt-br')
        return self._load_full_or_short(lang)

    def _load_full_or_short(self, lang):
        channel = self.languages.get(lang, '')
        if not channel:
            channel = self.languages.get(self._short_language(lang), '')
        return channel

    @staticmethod
    def _short_language(lang):
        tokens = lang.split('-')
        return tokens[0] if tokens else ''
