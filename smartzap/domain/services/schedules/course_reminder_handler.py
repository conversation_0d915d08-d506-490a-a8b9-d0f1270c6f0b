from domain.model import Enrollment, Schedule, User
from domain.services.message_dispatcher_v2 import MessageDispatcherV2
from domain.services.schedule_admin_service_v2 import ScheduleHandler


class CoursesReminderHandler(ScheduleHandler):
    def __init__(self, dispatcher: MessageDispatcherV2):
        self.dispatcher = dispatcher

    def handle(self, schedule: Schedule, enrollment: Enrollment, user: User) -> str:
        return self.dispatcher.send_message(enrollment.user.phone, "course_reminder", {
            "course_allow_drop_out": enrollment.course.allow_drop_out,
            "course_name": enrollment.course.name,
            "user_name": enrollment.user.name
        }, schedule.workspace.private_channel)
