from typing import Dict, List, Union

from domain import model
from domain.client import MyAccount<PERSON><PERSON>
from domain.service import UserService


class UserServiceV2(UserService):
    def __init__(self, db, client_id, my_account_client: MyAccountClient):
        super().__init__(db, model.User)
        self.client_id = client_id
        self.my_account_client = my_account_client

    def add(self, user: dict) -> Union[List[Dict], Dict]:
        user["profile"] = {
            "director": user.get("director"),
            "manager": user.get("manager"),
            "area_of_activity": user.get("area_of_activity")
        }
        return self.my_account_client.create_user(user, self.client_id)
