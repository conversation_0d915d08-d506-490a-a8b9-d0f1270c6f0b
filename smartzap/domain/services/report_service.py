from domain.clients.learn_analytics_client import LearnAnalyticsClient
from domain.model import EnrollmentStatus
from domain.service import NotificationService

COMPLETED_ENROLLMENTS = 'COMPLETED_ENROLLMENTS'
IN_PROGRESS_ENROLLMENTS = 'IN_PROGRESS_ENROLLMENTS'
USER_ACTIVITIES = 'USER_ACTIVITIES'


class ReportService:
    def __init__(
        self,
        notification_service: NotificationService,
        learn_analytics_client: LearnAnalyticsClient,
        client_id: str,
    ):
        self._notification_service = notification_service
        self._learn_analytics_client = learn_analytics_client
        self._functions = {
            COMPLETED_ENROLLMENTS: self.generate_completed_enrollments_export,
            IN_PROGRESS_ENROLLMENTS: self.generate_in_progress_enrollments_export,
            USER_ACTIVITIES: self.generate_user_activities_export
        }
        self._client_id = client_id

    def generate(self, report_type: str, course_id: str, user_token: str):
        report = self._functions[report_type](course_id, user_token)
        return report["report"]["id"]

    def generate_completed_enrollments_export(self, course_id: str, token: str):
        return self._learn_analytics_client.generate_enrollments_export(
            self._client_id, {"status__in": [EnrollmentStatus.completed], "course_id": str(course_id)}, token
        )

    def generate_in_progress_enrollments_export(self, course_id: str, token: str):
        return self._learn_analytics_client.generate_enrollments_export(
            self._client_id,
            {"status__in": [EnrollmentStatus.started, EnrollmentStatus.waiting], "course_id": str(course_id)},
            token
        )

    def generate_user_activities_export(self, course_id: str, token: str):
        return self._learn_analytics_client.generate_user_activities_export(
            self._client_id, {"enrollment__course_id": str(course_id)}, token
        )
