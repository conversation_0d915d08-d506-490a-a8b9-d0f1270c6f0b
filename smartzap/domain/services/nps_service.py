from typing import List

from domain.exceptions.nps_exceptions import (
    CourseNotAcceptingNPSSurveyException,
    EnrollmentNotCompletedException,
    FeedbackAlreadyRegisteredException,
    NPSScoreOutOfRangeException,
)
from domain.model import Course, Enrollment, EnrollmentStatus
from domain.service import CourseService, EnrollmentService
from domain.signals.events import CREATED_KEY, course_nps_score_event
from sqlalchemy.orm import joinedload


class NPSService:
    def __init__(self, database_session, course_service: CourseService, enrollment_service: EnrollmentService):
        self.database_session = database_session
        self.course_service = course_service
        self.enrollment_service = enrollment_service

    def calc_course_score(self, course_id: str):
        """
        Calculate and update the NPS score for a given course.

        :param course_id: ID of the course
        """
        enrollments = self.enrollment_service.db.query(Enrollment).filter(
            Enrollment.course_id == course_id,
            Enrollment.nps_score.isnot(None),
            Enrollment.status == EnrollmentStatus.completed
        )

        scores = [enrollment.nps_score for enrollment in enrollments]

        nps_score = self.calc(scores)
        self.course_service.update_entity(course_id, {Course.nps_score.key: nps_score})

    def register_enrollment_score(self, enrollment: Enrollment, score: int):
        """
        Register an NPS score for an enrollment.

        :param enrollment: The enrollment object
        :param score: NPS score (0-10)
        """
        if not enrollment.course:
            enrollment = (
                self.enrollment_service.db.query(Enrollment)
                .options(joinedload(Enrollment.course))
                .filter(Enrollment.id == enrollment.id)
                .one()
            )

        if enrollment.status != EnrollmentStatus.completed:
            raise EnrollmentNotCompletedException()
        if enrollment.nps_score is not None:
            raise FeedbackAlreadyRegisteredException()
        if not enrollment.course.allow_nps_survey:
            raise CourseNotAcceptingNPSSurveyException()
        if not (0 <= score <= 10):
            raise NPSScoreOutOfRangeException()

        self.enrollment_service.update_entity(enrollment.id, {Enrollment.nps_score.key: score})
        course_nps_score_event.emit(CREATED_KEY, course=enrollment.course)

    def calc(self, scores: List[int]) -> int:
        """
        Calculate Net Promoter Score (NPS) based on a list of scores (0-10).

        :param scores: List of customer feedback scores (0-10)
        :return: NPS score as an integer
        """
        total_responses = len(scores)
        if total_responses == 0:
            return 0

        promoters = sum(1 for score in scores if score >= 9)
        detractors = sum(1 for score in scores if score <= 6)

        promoters_percent = (promoters / total_responses) * 100
        detractors_percent = (detractors / total_responses) * 100

        nps = promoters_percent - detractors_percent

        return round(nps)
