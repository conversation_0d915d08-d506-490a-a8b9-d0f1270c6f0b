from domain import model
from domain.model import Enrollment, Workspace
from domain.service import PrivateChannelService
from flows.flow_input_data import FlowInputData
from flows.services.flows_service import FlowsService


class MessageDispatcherV2:
    def __init__(self, flows_service: FlowsService):
        self.flows_service = flows_service

    def dispatch_course_introduction(
        self,
        phone,
        course_name,
        user_name,
        workspace_name,
        private_channel
    ):
        data = {'user_name': user_name, 'company_name': workspace_name, 'course_name': course_name}
        message_type = "enrollment_intro"
        return self.send_message(phone, message_type, data, private_channel)

    def dispatch_enrollment_nps_survey(self, enrollment: Enrollment):
        data = {'user_name': enrollment.user.name, 'course_name': enrollment.course.name}
        message_type = "enrolment_nps_survey"
        return self.send_message(enrollment.user.phone, message_type, data, enrollment.workspace.private_channel)

    def dispatch_lesson_content(
        self,
        phone,
        user_name,
        lesson_name,
        content_name,
        content_description,
        anticipated,
        content_link,
        content_days_expiration,
        private_channel,
        allow_anticipation: bool,
        allow_drop_out: bool,
        messages_content_embed: bool,
    ):
        data = {
            "user_name": user_name,
            "lesson_name": lesson_name,
            "content_name": content_name,
            "content_description": content_description or "",
            "content_days_expiration": content_days_expiration,
            "content_link": content_link,
            "allow_anticipation": allow_anticipation,
            "allow_drop_out": allow_drop_out,
            "anticipated": anticipated,
            "is_content_embed": messages_content_embed,
        }
        message_type = "enrollment_content"
        return self.send_message(phone, message_type, data, private_channel)

    def dispatch_end_course(
        self,
        phone,
        course_name,
        user_name,
        percentage,
        duration,
        learn_duration,
        points,
        learn_points,
        performance_type,
        certificate_url,
        private_channel,
        disable_send_certificate=False,
    ):
        data = {
            "user_name": user_name,
            "course_name": course_name,
            "percentage": percentage,
            "duration": duration,
            "learn_duration": learn_duration,
            "points": points,
            "learn_points": learn_points,
            "language": "pt-br",
            "certificate_url": certificate_url,
            "disable_send_certificate": disable_send_certificate,
            "performance_type": performance_type,
        }

        message_type = "enrollment_finish"
        return self.send_message(phone, message_type, data, private_channel)

    def dispatch_courses_recommendations(self, phone, user_name: str, workspace: Workspace):
        message_type = "courses_recommendations"
        return self.send_message(
            phone,
            message_type,
            {
                "workspace_id": workspace.id,
                "user_name": user_name
            },
            workspace.private_channel
        )

    def send_message(
        self,
        phone: str,
        message_type: str,
        data: dict,
        private_channel_data: dict
    ):
        if not private_channel_data:
            return None
            ##raise ValueError("Private Channel Data is required")
        private_channel = (
            model.LanguageChannels(private_channel_data)
            if private_channel_data
            else None
        )
        username, password, sender_phone = PrivateChannelService.parse_channel_url(private_channel.load('pt-br'))

        flow_input_data = FlowInputData(
            sender=sender_phone,
            recipient=phone,
            parameters=data,
            account_sid=username,
            auth_token=password,
        )
        return self.flows_service.trigger_flow(message_type, flow_input_data)
