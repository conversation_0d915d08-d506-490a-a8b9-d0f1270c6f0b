from abc import ABC, abstractmethod
from datetime import datetime

from celery.utils.log import get_task_logger
from dateutil import tz
from domain import model
from domain.config import Config
from domain.exceptions.service_exceptions import NotFoundException
from domain.model import Course, Enrollment, Schedule, ScheduleType, User
from domain.service import ChatService, CourseReportService, EnrollmentService, ScheduleService, schedule_sent_signal
from domain.services.certificate_service import CertificateService
from domain.services.message_dispatcher_v2 import MessageDispatcherV2

logger = get_task_logger(__name__)

class ScheduleHandler(ABC):
    @abstractmethod
    def handle(self, schedule: Schedule, enrollment: Enrollment, user: User) -> str:
        pass

class CourseIntroductionHandler(ScheduleHandler):
    def __init__(self, dispatcher: MessageDispatcherV2, schedule_service: ScheduleService):
        self.dispatcher = dispatcher
        self.schedule_service = schedule_service

    def handle(self, schedule: Schedule, enrollment: Enrollment, user: User) -> str:
        course: Course = self.schedule_service.load_model(model.Course, schedule.reference_id)
        return self.dispatcher.dispatch_course_introduction(
            user.phone,
            course.name,
            user.name,
            enrollment.workspace.name,
            enrollment.workspace.private_channel,
        )

class CourseEndHandler(ScheduleHandler):
    def __init__(
        self,
        dispatcher: MessageDispatcherV2,
        schedule_service: ScheduleService,
        enrollment_service: EnrollmentService,
        course_report_service: CourseReportService,
        certificate_service: CertificateService
    ):
        self.dispatcher = dispatcher
        self.schedule_service = schedule_service
        self.enrollment_service = enrollment_service
        self.course_report_service = course_report_service
        self.certificate_service = certificate_service

    def handle(self, schedule: Schedule, enrollment: Enrollment, user: User) -> str:
        course = self.schedule_service.load_model(model.Course, schedule.reference_id)
        workspace = enrollment.workspace
        percentage = 1
        performance_type = 100

        local_date = self._local_date(schedule.send_date, schedule.timezone)
        self.enrollment_service.complete_course(
            schedule.enrollment_id,
            local_date,
            percentage,
            course.points,
        )

        percentage = self.course_report_service.format_percentage(percentage)
        duration = self.course_report_service.format_duration(course.duration)
        learn_duration = f"{self.course_report_service.format_duration(course.duration)}"
        points = f'{course.points:.1f}'

        certificate_url = self.certificate_service.generate_certificate(enrollment, course)
        self.enrollment_service.update_certificate_url(schedule.enrollment_id, certificate_url)

        d_id = self.dispatcher.dispatch_end_course(
            user.phone,
            course.name,
            user.name,
            percentage,
            duration,
            learn_duration,
            points,
            points,
            performance_type,
            certificate_url,
            workspace.private_channel,
            course.disable_send_certificate
        )

        if enrollment.course.allow_nps_survey:
            self.schedule_service.create_post_schedule(enrollment, ScheduleType.course_nps_survey)
        if enrollment.workspace.send_courses_recommendation_message:
            self.schedule_service.create_post_schedule(
                enrollment, ScheduleType.courses_recommendation, Config.COURSES_RECOMMENDATION_DELAY
            )

        return d_id

    @staticmethod
    def _local_date(date: datetime, tmz):
        local_timezone = tz.gettz(tmz)
        utc_timezone = tz.gettz("UTC")
        local_date = date.replace(tzinfo=utc_timezone)
        return local_date.astimezone(local_timezone).date()

class CourseNpsSurveyHandler(ScheduleHandler):
    def __init__(self, dispatcher: MessageDispatcherV2):
        self.dispatcher = dispatcher

    def handle(self, schedule: Schedule, enrollment: Enrollment, user: User) -> str:
        return self.dispatcher.dispatch_enrollment_nps_survey(enrollment)

class CoursesRecommendationHandler(ScheduleHandler):
    def __init__(self, dispatcher: MessageDispatcherV2):
        self.dispatcher = dispatcher

    def handle(self, schedule: Schedule, enrollment: Enrollment, user: User) -> str:
        return self.dispatcher.dispatch_courses_recommendations(
            enrollment.user.phone,
            enrollment.user.name,
            schedule.workspace
        )

class ScheduleHandlerFactory:
    def __init__(self, handlers: dict[ScheduleType, ScheduleHandler]):
        self.handlers = handlers

    def create_handler(self, schedule_type: ScheduleType) -> ScheduleHandler:
        return self.handlers.get(schedule_type, None)

class ScheduleAdminServiceV2:
    def __init__(
        self,
        schedule_service: ScheduleService,
        course_report_service: CourseReportService,
        enrollment_service: EnrollmentService,
        chat_service: ChatService,
        dispatcher: MessageDispatcherV2,
        certificate_service: CertificateService,
        handler_factory: ScheduleHandlerFactory,
    ):
        self.enrollment_service = enrollment_service
        self.course_report_service = course_report_service
        self.schedule_service = schedule_service
        self.chat_service = chat_service
        self.dispatcher = dispatcher
        self.certificate_service = certificate_service
        self.handler_factory = handler_factory

    def dispatch(self, schedule_id):
        if not self.dispatcher:
            return
        schedule = None

        try:
            schedule: Schedule = self.schedule_service.load(schedule_id)
            if schedule.status != model.ScheduleStatus.pending:
                logger.info(f"Schedule not pending: {schedule.id}")
                return

            handler = self.handler_factory.create_handler(schedule.type)
            if handler:
                enrollment, user = self.schedule_service.load_enrollment_and_user(schedule.enrollment_id)
                dispatch_id = handler.handle(schedule, enrollment, user)
                self._post_dispatch(schedule, dispatch_id)
        except NotFoundException as e:
            logger.error(f"Schedule not found: {e}")
            if schedule:
                self._post_dispatch(schedule, e.message, False)

    def _post_dispatch(self, schedule: Schedule, dispatch_id, success=True):
        self.schedule_service.update_schedule_status(schedule, dispatch_id, success)

        if success and dispatch_id:
            self.chat_service.registry_schedule(schedule)
            schedule_sent_signal.emit("sent", schedule=schedule)
