from domain.exceptions.service_exceptions import CourseNotFound
from domain.model import Course, CourseWorkspace, Enrollment, EnrollmentStatus, User
from domain.service import CourseService, EnrollmentService, UserService


class UserInitiatedEnrollmentService:
    """
   Service for handling cases where a user initiates their own enrollment in a course.
   """
    def __init__(self, enrollment_service: EnrollmentService, user_service: UserService, course_service: CourseService):
        self.enrollment_service = enrollment_service
        self.user_service = user_service
        self.course_service = course_service

    def enroll(self, user_phone: str, course_id: str) -> Enrollment:
        user = self._get_user_by_phone(user_phone)
        workspace_id = self._get_course_workspace_id(course_id, user.id)

        enrollment = self._create_enrollment(user.id, course_id, workspace_id)

        return self.enrollment_service.add(enrollment)

    def _get_user_by_phone(self, phone: str) -> User:
        return self.user_service.get_by_phone(phone=phone, raise_exception=True)

    def _get_course_workspace_id(self, course_id: str, user_id: str) -> str:
        user_workspace_ids = self.user_service.get_user_workspace_ids(user_id)

        workspace_id = (
            self.course_service.db.query(CourseWorkspace.workspace_id)
            .join(Course)
            .filter(
                Course.id == course_id,
                CourseWorkspace.workspace_id.in_(user_workspace_ids),
            )
            .scalar()
        )

        if not workspace_id:
            raise CourseNotFound()
        return workspace_id

    def _create_enrollment(self, user_id: str, course_id: str, workspace_id: str) -> Enrollment:
        return Enrollment(
            user_id=user_id,
            course_id=course_id,
            workspace_id=workspace_id,
            status=EnrollmentStatus.started
        )
