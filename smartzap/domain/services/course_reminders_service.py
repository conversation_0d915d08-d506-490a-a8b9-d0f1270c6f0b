import logging
from datetime import datetime, timedelta, timezone

import pytz
from domain.model import Enrollment, EnrollmentStatus, Schedule, ScheduleStatus, ScheduleType
from domain.service import EnrollmentService, ScheduleService
from domain.services.interfaces.course_reminders_service import ICourseRemindersService
from domain.utils import BusinessHourUtils
from sqlalchemy import and_, func

logger = logging.getLogger(__name__)


class CourseRemindersService(ICourseRemindersService):
    def __init__(
        self,
        db,
        schedule_service: ScheduleService,
        enrollment_service: EnrollmentService
    ):
        self.db = db
        self.schedule_service = schedule_service
        self.enrollment_service = enrollment_service

    def schedule_reminders_for_idle_enrollments(self, workspace_id: str):
        enrollments_without_recent_activity = self._get_enrollment_without_recent_activity(workspace_id)

        if not enrollments_without_recent_activity:
            logger.info("No idle enrollments found.")
            return

        for enrollment in enrollments_without_recent_activity:
            self._schedule_reminder(enrollment[0])

    def _get_enrollment_without_recent_activity(self, workspace_id):
        some_hours_ago = datetime.now(tz=timezone.utc) - timedelta(hours=23)
        enrollments_already_notified = (
            self.db.query(Enrollment.id)
            .join(Schedule, Schedule.enrollment_id == Enrollment.id)
            .filter(
                and_(
                    Schedule.type == ScheduleType.course_reminder
                )
            )
            .subquery()
        )
        enrollments_without_recent_activity = (
            self.db.query(
                Enrollment.id
            )
            .join(Schedule, Schedule.enrollment_id == Enrollment.id)
            .filter(
                Schedule.status.in_([ScheduleStatus.sent, ScheduleStatus.delivered, ScheduleStatus.error]),
                Schedule.type.in_([ScheduleType.lesson_content, ScheduleType.course_introduction]),
                Schedule.workspace_id == workspace_id,
                Schedule.enrollment_id.notin_(enrollments_already_notified),
                Enrollment.status == EnrollmentStatus.started
            )
            .group_by(Enrollment.id)
            .having(
                func.max(Schedule.send_date) < some_hours_ago
            )
        )
        return enrollments_without_recent_activity

    def _schedule_reminder(self, enrollment_id: str):
        enrollment = self.db.query(Enrollment).get(enrollment_id)
        if not enrollment.user.phone:
            logger.warning(f"Skipping enrollment {enrollment.id} - No phone number found.")
            return

        send_date = BusinessHourUtils.get_next_business_hour(datetime.now(timezone.utc), enrollment.timezone)

        schedule = Schedule(
            workspace_id=enrollment.workspace_id,
            enrollment_id=enrollment.id,
            type=ScheduleType.course_reminder,
            send_date=send_date,
            lang=enrollment.course.lang,
            timezone=enrollment.timezone or pytz.UTC.__str__(),
            external_sender=True,
        )

        logger.info(f"Scheduling reminder for user {enrollment.user.phone}, enrollment {enrollment.id}")
        schedule = self.schedule_service.add(schedule)
