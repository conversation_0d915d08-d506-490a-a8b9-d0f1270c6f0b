from typing import Optional, <PERSON><PERSON>

from domain import model
from domain.exceptions.nps_exceptions import CourseNotAcceptingNPSSurveyException, FeedbackAlreadyRegisteredException
from domain.model import Enrollment
from domain.service import EnrollmentService, ScheduleService, WorkspaceService
from domain.services.courses_recommendations_service import CoursesRecommendationsService
from domain.services.nps_service import (
    NPSService,
)
from domain.services.user_initiated_enrollment_service import UserInitiatedEnrollmentService

SUCCESS = "success"
WITHOUT_COURSES_TO_RECOMMEND = "without_courses_to_recommend"
NO_ENROLLMENT = "no_enrollment"


class MessageChatterBotV2:
    def __init__(
        self,
        enrollment_service: EnrollmentService,
        schedule_service: ScheduleService,
        tasks_client,
        nps_service: NPSService,
        user_initiated_enrollment_service: UserInitiatedEnrollmentService,
        courses_recommendations_service: CoursesRecommendationsService,
        workspace_service: WorkspaceService
    ):
        self.enrollment_service = enrollment_service
        self.schedule_service = schedule_service
        self.tasks_client = tasks_client
        self.nps_service = nps_service
        self.user_initiated_enrollment_service = user_initiated_enrollment_service
        self.courses_recommendations_service = courses_recommendations_service
        self.workspace_service = workspace_service

    def _accept_message(self, enrollment) -> Enrollment:
        enrollment = self.enrollment_service.accept_disclaimer(enrollment)
        return enrollment

    def _finish_course_message(self, enrollment) -> str:
        status, schedule_id = self.schedule_service.anticipate_course_end(enrollment.id)
        if schedule_id:
            self.tasks_client.dispatch_message_by_external(schedule_id)
        return status

    def enrollment_confirmation(self, phone: str, accept: bool) -> Tuple[Optional[Enrollment], str]:
        enrollment = self.enrollment_service.load_first_active_from_phone(phone)
        if not enrollment:
            return None, NO_ENROLLMENT
        if enrollment.status != model.EnrollmentStatus.waiting:
            return None, "enrollment_already_started"

        if accept:
            return self.enrollment_service.accept_disclaimer(enrollment), "enrollment_confirmed"

        return self.enrollment_service.reject_disclaimer(enrollment), "enrollment_rejected"

    def finish_enrollment(self, phone: str) -> Optional[str]:
        enrollment = self.enrollment_service.load_first_active_from_phone(phone)
        if not enrollment:
            return NO_ENROLLMENT

        return self._finish_course_message(enrollment)

    def give_up(self, phone: str) -> Tuple[Optional[str], str]:
        enrollment = self.enrollment_service.load_first_active_from_phone(phone)
        if not enrollment:
            return None, NO_ENROLLMENT

        self.schedule_service.give_up_enrollment(enrollment.id)
        return enrollment.course.name, SUCCESS

    def register_nps_score(self, phone: str, score: int) -> str:
        enrollment = self.enrollment_service.load_last_completed_from_phone(phone)
        if not enrollment:
            return "no_completed_enrollment"

        try:
            self.nps_service.register_enrollment_score(enrollment, score)
        except FeedbackAlreadyRegisteredException:
            return "feedback_already_registered"
        except CourseNotAcceptingNPSSurveyException:
            return "course_not_accepting_nps_survey"

        return SUCCESS

    def enroll(self, phone: str, course_id: str) -> str:
        self.user_initiated_enrollment_service.enroll(phone, course_id)
        return SUCCESS

    def get_courses_portal_url(self, workspace_id: str) -> Tuple[str, str]:
        portal_url = self.courses_recommendations_service.get_portal_link(workspace_id)
        return portal_url, SUCCESS

    def get_courses_recommendations_data(self, phone: str, workspace_id: str) -> Tuple[dict, str]:
        courses = self.courses_recommendations_service.get_courses(phone, workspace_id, 3)
        if len(courses) < 3:
            return {}, WITHOUT_COURSES_TO_RECOMMEND

        recommendations_data = {}
        for i, course in enumerate(courses):
            recommendations_data.update({
                f"course_{i + 1}_id": course.id,
                f"course_{i + 1}_name": course.name,
                f"course_{i + 1}_description": course.description
            })
        return recommendations_data, SUCCESS
