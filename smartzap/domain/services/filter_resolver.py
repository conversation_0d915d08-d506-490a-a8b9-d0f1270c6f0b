import collections
import importlib
import math
from typing import Sequence

import sqlalchemy_filters
from flask_extensions.webfilters import WebFilterValidationException
from sqlalchemy import and_, or_
from sqlalchemy.orm import Query
from sqlalchemy.sql.functions import ReturnTypeFromArgs


class Unaccent(ReturnTypeFromArgs):
    pass


def _paginate_filters(query, spec):
    page = spec.get("page", 1)
    per_page = spec.get("per_page", None)
    return _pagination(query, page, per_page)


SPECIAL_SQL_WORDS = ("user",)


def _pagination(query, page, per_page):
    query, pagination = sqlalchemy_filters.apply_pagination(
        query, page_number=page, page_size=per_page
    )
    total_pages = (
        int(math.ceil(float(pagination.total_results) / float(per_page)))
        if per_page
        else None
    )
    return query, create_pagination(
        page, per_page, total_pages, pagination.total_results
    )


def create_pagination(page, per_page, total_pages, count):
    SmartPage = collections.namedtuple(
        "SmartPage", ["page", "per_page", "total_pages", "count"]
    )
    return SmartPage(page, per_page, total_pages, count)


class FilterResolver:
    def __init__(self, model_type):
        self.model_type = model_type

    @staticmethod
    def _get_model_by_name(name: str):
        model_class = f"domain.model.{name}"
        module_name, class_name = model_class.rsplit(".", 1)
        model_module = importlib.import_module(module_name)
        return getattr(model_module, class_name)

    def _get_attrib_model(self, field):
        attributes = [a for a in dir(self.model_type) if a == field]
        if attributes:
            attrib = getattr(self.model_type, attributes[-1], None)
            if not attrib:
                return None
            prop = getattr(attrib, "property", None)
            if not prop:
                return None
            back_pop = getattr(prop, "back_populates", "")
            use_list = getattr(prop, "uselist", True)
            if back_pop and not use_list:
                return prop.entity.class_
        return None

    def _add_models(self, filters):
        join_models = {}
        for filter_name in ["filter_spec", "sort_spec", "search_spec"]:
            for f in filters.get(filter_name, []):
                f["model"] = self.model_type.__name__
                field_name = f.get("field", "")
                v = field_name.split("__")
                if len(v) >= 2:
                    attrib_name = v[0]
                    attrib_model = self._get_attrib_model(attrib_name)
                    if attrib_model:
                        field_name = "__".join(v[1:])
                        f["model"] = attrib_model.__name__
                        f["field"] = field_name
                        join_models[attrib_model.__name__] = attrib_model
        return join_models

    def exec_query_filters(self, query, filters):
        self._add_models(filters)

        filter_spec = filters.get("filter_spec", [])
        if filter_spec:
            query = sqlalchemy_filters.apply_filters(query, filter_spec)

        search_spec = filters.get("search_spec", [])
        query = self._add_search_filter(query, search_spec)

        sort_spec = filters.get("sort_spec", [])
        if sort_spec:
            query = sqlalchemy_filters.apply_sort(query, sort_spec)

        query, pagination = _paginate_filters(query, filters.get("pag_spec", {}))
        return query.all(), pagination

    def apply_filters_in_query(self, query, filters):
        self._add_models(filters)
        filter_spec = filters.get("filter_spec", [])

        if filter_spec:
            query = sqlalchemy_filters.apply_filters(query, filter_spec)

        search_spec = filters.get("search_spec", [])
        query = self._add_search_filter(query, search_spec)

        sort_spec = filters.get("sort_spec", [])
        if sort_spec:
            query = sqlalchemy_filters.apply_sort(query, sort_spec)

        return query

    @staticmethod
    def _extract_ilike_spec(filter_spec, ilike_spec):
        for position, spec in enumerate(filter_spec):
            if spec["op"] == "ilike":
                filter_spec.pop(position)
                ilike_spec.append(spec)

    def _add_search_filter(self, query: Query, specs: Sequence[dict]) -> Query:
        search_filters = []
        for spec in specs:
            ilike_filter, query = self._generate_ilike_filter(query, spec)
            search_filters.append(ilike_filter)

        return query.filter(and_(or_(*tuple(search_filters))))

    def _generate_ilike_filter(self, query, spec):
        # todo: remove this in favor to new sqlalchemy filter lib
        table_name = spec["model"].lower()
        model_name = (
            f'"{table_name}"' if table_name in SPECIAL_SQL_WORDS else table_name
        )
        table_joined = f"JOIN {model_name} ON" in str(query)
        model_base = self._get_model_by_name(spec["model"])
        if not table_joined:
            query = query.join(model_base)
        field_name = spec["field"]
        try:
            field = getattr(model_base, field_name)
        except KeyError:
            raise WebFilterValidationException(
                f"{model_name} doesn't have the field {field_name}"
            )
        ilike_filter = Unaccent(field).ilike(Unaccent(spec["value"]))
        return ilike_filter, query
