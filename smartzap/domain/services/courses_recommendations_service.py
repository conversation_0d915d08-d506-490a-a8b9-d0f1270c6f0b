import logging
from datetime import datetime, timedelta, timezone
from typing import List, Set

import pytz
from domain.model import Course, CourseStatus, Enrollment, EnrollmentStatus, Schedule, ScheduleType, User, Workspace
from domain.service import EnrollmentService, ScheduleService, UserService
from domain.services.schedule_admin_service_v2 import ScheduleAdminServiceV2
from sqlalchemy import and_, func, not_

logger = logging.getLogger(__name__)


class CoursesRecommendationsService:
    def __init__(
        self,
        db,
        user_service: UserService,
        schedule_admin_service: ScheduleAdminServiceV2,
        schedule_service: ScheduleService,
        enrollment_service: EnrollmentService,
        days_without_new_enrollment: List[int] = None
    ):
        self.db_session = db
        if not days_without_new_enrollment:
            days_without_new_enrollment = [3, 5]
        self.days_without_new_enrollment = days_without_new_enrollment
        self.user_service = user_service
        self.schedule_cutoff_end = datetime.now(timezone.utc) - timedelta(hours=24)
        self.schedule_admin_service = schedule_admin_service
        self.schedule_service = schedule_service
        self.enrollment_service = enrollment_service

    def get_courses(self, user_phone: str, workspace_id: str, courses_count: int) -> List[Course]:
        user = self.user_service.get_by_phone(user_phone, True)
        subquery = (
            self.db_session.query(Enrollment.course_id)
            .filter(
                and_(
                    Enrollment.user_id == user.id,
                    Enrollment.workspace_id == workspace_id,
                    Enrollment.status == EnrollmentStatus.completed
                )
            )
        )

        query = (
            self.db_session.query(Course)
            .filter(
                and_(
                    Course.workspace_owner_id == workspace_id,
                    Course.status == CourseStatus.finished,
                    not_(Course.id.in_(subquery))
                )
            )
            .order_by(func.random())
            .limit(courses_count)
        )

        return query.all()

    def _get_recently_completed_enrollment_user_phones(
        self,
        days_without_enrollment: int,
        workspace_id: str
    ) -> List[str]:
        now = datetime.now(timezone.utc)
        cutoff_start = now - timedelta(days=days_without_enrollment + 1)
        cutoff_end = now - timedelta(days=days_without_enrollment)

        users_with_enrollment_in_progress = (
            self.db_session.query(Enrollment.user_id)
            .filter(
                and_(
                    Enrollment.status.in_([EnrollmentStatus.started, EnrollmentStatus.waiting])
                )
            )
            .subquery()
        )

        users_that_already_receive_the_recommendation = (
            self.db_session.query(Enrollment.user_id)
            .join(Schedule, Schedule.enrollment_id == Enrollment.id)
            .filter(
                and_(
                    Schedule.type == ScheduleType.courses_recommendation,
                    Schedule.send_date >= self.schedule_cutoff_end
                )
            )
            .subquery()
        )

        query = (
            self.db_session.query(
                User.phone
            )
            .join(Enrollment, Enrollment.user_id == User.id)
            .filter(
                and_(
                    Enrollment.status == EnrollmentStatus.completed,
                    Enrollment.workspace_id == workspace_id,
                    not_(Enrollment.user_id.in_(users_with_enrollment_in_progress)),
                    not_(Enrollment.user_id.in_(users_that_already_receive_the_recommendation))
                )
            )
            .group_by(User.id, User.phone)
            .having(func.max(Enrollment.end_date) < cutoff_end.date())
            .having(func.max(Enrollment.end_date) >= cutoff_start.date())
        )

        return [row[0] for row in query.all()]

    def list_workspace_users_to_notify(self, workspace_id: str) -> Set[str]:
        user_phones = set()
        for days in self.days_without_new_enrollment:
            user_phones.update(self._get_recently_completed_enrollment_user_phones(days, workspace_id))

        logger.info(f'Found users {user_phones}')

        return user_phones

    def schedule_message(self, phone: str, workspace_id: str) -> None:
        enrollment = self.enrollment_service.load_last_completed_from_phone(phone)
        if not enrollment:
            logger.info(f'Any completed enrollment for {phone} was found')
            return

        courses = self.get_courses(phone, workspace_id, 3)
        if not courses or len(courses) < 3:
            logger.info(f'Any new courses available to {phone}')
            return

        schedule = Schedule(
            workspace_id=workspace_id,
            enrollment_id=enrollment.id,
            type=ScheduleType.courses_recommendation,
            send_date=datetime.now(timezone.utc),
            lang=enrollment.course.lang,
            timezone=enrollment.timezone or pytz.UTC.__str__(),
            external_sender=True
        )

        logger.info(f'Scheduling message for user with phone {phone}')
        schedule = self.schedule_service.add(schedule)
        self.schedule_admin_service.dispatch(schedule.id)

    def get_portal_link(self, workspace_id: str) -> str:
        return self.db_session.query(
            Workspace.courses_portal_url
        ).filter(Workspace.id == workspace_id).scalar()
