from abc import ABC, abstractmethod
from typing import Dict


class BaseMimeTypeValidator(ABC):
    @abstractmethod
    def is_valid(self, learn_content: dict) -> bool:
        pass


class VideoMimeTypeValidator(BaseMimeTypeValidator):
    WHATSAPP_SIZE_LIMIT_MB = 16

    def is_valid(self, learn_content: dict) -> bool:
        file_size = learn_content.get("file_size")
        return (
            file_size is not None and
            isinstance(file_size, (int, float)) and
            file_size <= self.WHATSAPP_SIZE_LIMIT_MB
        )


class UnsupportedMimeTypeValidator(BaseMimeTypeValidator):
    def is_valid(self, learn_content: dict) -> bool:
        return True


class LearnContentValidationService:
    def __init__(self, validators: Dict[str, BaseMimeTypeValidator]):
        self.validators = validators
        self.default_validator = UnsupportedMimeTypeValidator()

    def is_valid_whatsapp_content(self, learn_content: dict) -> bool:
        mime_type = learn_content.get("file_mime_type")
        validator = self.validators.get(mime_type, self.default_validator)
        return validator.is_valid(learn_content)
