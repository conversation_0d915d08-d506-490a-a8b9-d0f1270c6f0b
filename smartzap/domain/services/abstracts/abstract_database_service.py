from domain import database
from domain.exceptions.service_exceptions import NotFoundException
from domain.services.filter_resolver import FilterResolver


class AbstractDatabaseService:
    def __init__(self, db, model_type):
        self.db = db
        self.model_type = model_type

    def add(self, obj):
        with database.transaction(self.db):
            self.db.add(obj)
            return obj

    def update(self, obj_id, obj):
        _ = self.load(obj_id)
        with database.transaction(self.db):
            obj.id = obj_id
            return self.db.merge(obj)

    def load(self, obj_id):
        obj = self.db.query(self.model_type).filter_by(id=obj_id).first()
        if not obj:
            raise NotFoundException(f"{self.model_type.__name__} not found")
        return obj

    def load_all(self):
        query = self.db.query(self.model_type)
        return query.all()

    def delete(self, obj_id):
        obj = self.load(obj_id)
        with database.transaction(self.db):
            self.db.delete(obj)

    def load_filters(self, filters):
        query = self.db.query(self.model_type)
        return FilterResolver(self.model_type).exec_query_filters(query, filters)

    def apply_filters(self, filters):
        query = self.db.query(self.model_type)
        return FilterResolver(self.model_type).apply_filters_in_query(query, filters)

    def delete_in_batch(self, ids):
        with database.transaction(self.db):
            self.db.query(self.model_type).filter(self.model_type.id.in_(ids)).delete(
                synchronize_session=False
            )

    def update_entity(self, obj_id: str, obj_update: dict):
        instance = self.db.query(self.model_type).get(obj_id)
        if not instance:
            raise NotFoundException(f"{self.model_type.__name__} not found")

        for key, value in obj_update.items():
            setattr(instance, key, value)

        with database.transaction(self.db):
            self.db.add(instance)
            return instance
