from typing import Optional

from domain.model import WorkspaceCertificateTemplate
from domain.services.abstracts.abstract_database_service import AbstractDatabaseService


class WorkspaceCertificateTemplateService(AbstractDatabaseService):
    def __init__(self, db):
        super().__init__(db, WorkspaceCertificateTemplate)

    def find_by_workspace(self, workspace_id: str, language: str) -> Optional[WorkspaceCertificateTemplate]:
        template = self.db.query(
            WorkspaceCertificateTemplate
        ).filter(
            WorkspaceCertificateTemplate.workspace_id == workspace_id,
            WorkspaceCertificateTemplate.language == language
        ).first()
        return template
