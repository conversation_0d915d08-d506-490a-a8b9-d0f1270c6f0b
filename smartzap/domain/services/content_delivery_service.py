import datetime
from typing import Dict, Optional, <PERSON><PERSON>

import pytz
from domain import database, model
from domain.client import KontentClient
from domain.model import Content, Enrollment, Schedule
from domain.service import EnrollmentService, ScheduleAdminService, ScheduleService
from domain.services.learn_content_validation_service import LearnContentValidationService


class ContentMessageDataService:
    def __init__(
        self,
        enrollment_service: EnrollmentService,
        schedule_service: ScheduleService,
        schedule_admin_service: ScheduleAdminService,
        kontent_client: KontentClient,
        learn_content_validation_service: LearnContentValidationService,
        database_session,
    ):
        self.enrollment_service = enrollment_service
        self.schedule_service = schedule_service
        self.schedule_admin_service = schedule_admin_service
        self.kontent_client = kontent_client
        self.database_session = database_session
        self.learn_content_validation_service = learn_content_validation_service

    def get_next_content_data(self, phone: str, execution_sid: Optional[str] = None) -> Tuple[Dict, str]:
        enrollment = self._load_active_enrollment(phone)
        if not enrollment:
            return {}, "no_enrollment"

        with database.transaction(self.database_session):
            schedule = self.schedule_service.get_next_schedule(enrollment.id)
            if not self._is_schedule_valid(schedule):
                return {}, self._schedule_invalid_reason(schedule)

            self._mark_schedule_as_sent(schedule, execution_sid)
            return self._build_content_response(schedule, enrollment), "anticipated"

    def _load_active_enrollment(self, phone: str) -> Optional[Enrollment]:
        return self.enrollment_service.load_first_active_from_phone(phone)

    def _is_schedule_valid(self, schedule: Optional[Schedule]) -> bool:
        return (
            schedule is not None
            and schedule.type == model.ScheduleType.lesson_content
        )

    def _schedule_invalid_reason(self, schedule: Optional[Schedule]) -> str:
        if schedule is None:
            return "any_message_scheduled"
        if schedule.type == model.ScheduleType.course_end:
            return "waiting_end"
        return "any_message_scheduled"

    def _mark_schedule_as_sent(self, schedule: Schedule, execution_sid: Optional[str]) -> None:
        schedule.anticipated = True
        schedule.triggered_flow_id = execution_sid
        schedule.status = model.ScheduleStatus.sent
        schedule.send_date = datetime.datetime.now(pytz.utc)

    def _build_content_response(self, schedule: Schedule, enrollment: Enrollment) -> Dict:
        content = self.schedule_service.load_model(model.Content, schedule.reference_id)
        content_link, is_content_embed = self._resolve_content_link(schedule, enrollment, content)

        self.enrollment_service.register_current_content(
            schedule.enrollment_id, content.id, content.lesson_id
        )

        return {
            "language": schedule.lang,
            "user_phone": enrollment.user.phone,
            "user_name": enrollment.user.name,
            "content_title": content.name,
            "content_description": content.description or content.name,
            "anticipated": schedule.anticipated,
            "content_link": content_link,
            "image_url": getattr(content.type, "image_url", ""),
            "is_content_embed": is_content_embed,
            "token_expiration": enrollment.workspace.user_token_expiration,
            "course_allow_content_anticipation": enrollment.course.allow_content_anticipation,
            "course_allow_drop_out": enrollment.course.allow_drop_out,
        }

    def _resolve_content_link(
        self, schedule: Schedule, enrollment: Enrollment, content: Content
    ) -> Tuple[str, bool]:
        learn_content = self.kontent_client.load_content(content.learn_content)
        if not learn_content:
            return self._generate_fallback_link(schedule, enrollment, content), False

        is_valid_whatsapp_content = self.learn_content_validation_service.is_valid_whatsapp_content(learn_content)

        if enrollment.workspace.messages_content_embed and is_valid_whatsapp_content:
            return learn_content.get("url"), True

        return self._generate_fallback_link(schedule, enrollment, content), False

    def _generate_fallback_link(
        self, schedule: Schedule, enrollment: Enrollment, content: Content
    ) -> str:
        return self.schedule_admin_service.generate_content_url(
            content,
            enrollment.user_id,
            enrollment.id,
            enrollment.workspace.user_token_expiration,
            schedule.lang,
        )
