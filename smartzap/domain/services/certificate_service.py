import base64
import json
import tempfile
import uuid
from datetime import date
from typing import Dict

from babel.dates import format_date as babel_format_date
from domain.client import MyA<PERSON>unt<PERSON><PERSON>, S3Client
from domain.clients.report_generator_client import Report<PERSON>enerator<PERSON>lient
from domain.config import Config
from domain.exceptions.service_exceptions import IntegrityException
from domain.model import Course, Enrollment, EnrollmentStatus, Workspace
from domain.services.workspace_certificate_template_service import WorkspaceCertificateTemplateService
from domain.utils import clean_for_url_or_path

DEFAULT_LOG = Config.DEFAULT_CERTIFICATE_LOGO
COURSE_NAME = "course_name"
USER_NAME = "user_name"


class CertificateService:
    def __init__(
        self,
        report_generator_client: ReportGeneratorClient,
        aws_s3_client: S3Client,
        account_client: MyAccountClient,
        workspace_color_path: str,
        workspace_certificate_template_service: WorkspaceCertificateTemplateService,
        default_templates: Dict[str, str],
    ):
        self._report_generator_client = report_generator_client
        self._aws_s3_client = aws_s3_client
        self._account_client = account_client
        self._default_templates = default_templates
        self._date_format = "%d/%m/%Y"
        self._long_date_format = "%d/%m/%Y"
        self._workspace_color_path = workspace_color_path
        self._workspace_certificate_template_service = workspace_certificate_template_service

    def generate_certificate(self, enrollment: Enrollment, course: Course) -> str:
        """
        Generates a certificate for a given enrollment, uploading it to S3 and returning the URL.
        """
        if enrollment.status != EnrollmentStatus.completed:
            raise IntegrityException("user_enrollment_not_finish")

        workspace_logo = enrollment.workspace.logo_url
        date_finished = self.format_time_date(enrollment.end_date)
        date_started = self.format_time_date(enrollment.start_date)

        data = {
            "principal": {
                COURSE_NAME: course.name.upper(),
                USER_NAME: enrollment.user.name.upper(),
                "performance": f"{int(enrollment.performance * 100)}%",
                "duration": self.format_time_duration(int(course.duration or 0)),
                "date_finished": date_finished,
                "date_started": date_started,
                "date_started_long_date_format": self.format_long_date(enrollment.start_date, course.lang),
                "date_finished_long_date_format": self.format_long_date(enrollment.end_date, course.lang),
                "company_icon": workspace_logo or DEFAULT_LOG,
                "template_color": self._get_workspace_theme_color(enrollment.workspace_id),
            },
            "summary": {},
        }

        certificate_url = self._compile_file(enrollment.workspace, data, course.lang)

        return certificate_url

    def _compile_file(self, workspace: Workspace, data: dict, language: str) -> str:
        """
        Compiles the certificate file from the template and uploads it, returning the file URL.
        """
        certificate_template = self._get_certificate_template(workspace, language)
        cover_data = data["principal"]
        file_buffer = self._report_generator_client.generate_report(
            certificate_template, cover_data, params={}
        )
        file_name = self._create_file_name(cover_data)
        with tempfile.NamedTemporaryFile(mode='wb') as certificate_file:
            certificate_file.write(base64.b64decode(file_buffer))
            certificate_file.seek(0)
            return self._upload(certificate_file.name, file_name)

    def _create_file_name(self, cover_data: dict) -> str:
        course_name = cover_data.get(COURSE_NAME, "")
        user_name = cover_data.get(USER_NAME, "")
        random_id = str(uuid.uuid4())[:4]
        file_name = clean_for_url_or_path(f"certificado_{course_name}_{user_name}_{random_id}")
        return f"certificates/smartzap-course/{file_name}.pdf"

    def _upload(self, file_path: str, file_name: str) -> str:
        """
        Uploads the certificate file to S3 and returns the URL.
        """
        response = self._aws_s3_client.send_file_from_path(
            file_path,
            file_name,
            "application/pdf"
        )
        return response["url"]

    def _get_certificate_template(self, workspace: Workspace, language: str) -> str:
        """
        Retrieves the certificate template for the specified workspace and language.
        """
        template = self._workspace_certificate_template_service.find_by_workspace(
            workspace_id=workspace.id, language=language
        )
        if template:
            return template.certificate_template_key
        return self._default_templates[language]

    @staticmethod
    def format_time_duration(seconds: int, string_format="hm") -> str:
        """
        Formats seconds into a human-readable duration format.
        """
        minutes, seconds = divmod(seconds, 60)
        hours, minutes = divmod(minutes, 60)

        if string_format == "hm":
            return f"{hours}h{minutes:02d}m"
        elif string_format == "h":
            return f"{hours}h"
        elif string_format == "m":
            return f"{minutes}m"

        raise ValueError("Invalid format specified")

    def format_time_date(self, input_date: date) -> str:
        return input_date.strftime(self._date_format)

    def format_long_date(self, input_date: date, language: str) -> str:
        """
        Formats a date in long format for the given language (pt-br, en, es).

        :param input_date: The date to format.
        :param language: Language code ('pt-br' for Portuguese, 'en' for English, 'es' for Spanish).
        :return: The formatted date as a string in long format.
        """
        language_codes = {
            'pt-br': 'pt_BR',
            'en': 'en_US',
            'es': 'es_ES'
        }
        try:
            locale_code = language_codes[language]
        except KeyError:
            raise ValueError("Unsupported language. Use 'pt-br', 'en', or 'es'.")

        return babel_format_date(input_date, format='long', locale=locale_code)

    def _get_workspace_theme_color(self, workspace_id):
        response = self._account_client.get_workspace_theme_color(workspace_id=workspace_id)

        with open(self._workspace_color_path, encoding="utf-8") as open_color_map:
            color_map = json.load(open_color_map)
        hex_color = color_map.get(response.get("theme_id"), "#B171FB")

        return hex_color
