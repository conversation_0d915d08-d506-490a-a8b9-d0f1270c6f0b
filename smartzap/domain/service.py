import calendar
import csv
import datetime
import glob
import math
import os
import re
import shutil
import string
import tempfile
import textwrap
import urllib
import uuid
from typing import Dict, List, Optional
from urllib.parse import urlparse

import dateutil
import jwt
import pymitter
import pytz
import requests
import sqlalchemy
import urljoin
import xlrd
from dateutil import tz
from dateutil.relativedelta import relativedelta
from domain import database, model
from domain.client import WhatsAppTwilioClient
from domain.config import Config
from domain.constants.task_names import DISPATCH_MESSAGE, DISPATCH_MESSAGE_BY_EXTERNAL
from domain.discord_webhook_logger import DiscordWebhookLogger
from domain.exceptions.service_exceptions import IntegrityException, NotFoundException, UserNotFound
from domain.message_names import (
    LESSON_CONTENT_ANTICIPATED_WITH_GIVE_UP_OPTION,
    LESSON_CONTENT_WITHOUT_CONTENT_ANTICIPATION,
    LESSON_CONTENT_WITHOUT_CONTENT_ANTICIPATION_WITH_GIVE_UP_OPTION,
)
from domain.model import Enrollment, EnrollmentStatus
from domain.services.abstracts.abstract_database_service import AbstractDatabaseService
from domain.services.certificate_service import CertificateService
from domain.services.filter_resolver import FilterResolver
from domain.utils import round_performance
from logger import logger
from sqlalchemy.orm import Query
from tasks.celery_app import app

schedule_sent_signal = pymitter.EventEmitter()
course_delete_signal = pymitter.EventEmitter()

COURSE_WELCOME_WITHOUT_CONTENT_ANTICIPATION = "course_welcome__without_content_anticipation"
COURSE_WELCOME = "course_welcome"


class ClientId(str):
    pass


class UserId(str):
    pass


class EnrollmentId(str):
    pass


class PrivateChannelService:
    def __init__(self, channels: model.LanguageChannels):
        if not isinstance(channels, model.LanguageChannels):
            raise ValueError(
                "Invalid channels param, expected LanguageChannels instance"
            )
        self.channels = channels

    @staticmethod
    def parse_channel_url(url):
        if not url:
            return "", "", ""

        schema = url.rfind("/")
        if schema >= 0:
            url = url[schema + 1:]
        colon = url.find(":")
        if colon < 0:
            return "", "", ""
        at_sign = url.find("@")
        if at_sign < 0:
            return "", "", ""
        username = urllib.parse.unquote(url[:colon])
        password = urllib.parse.unquote(url[colon + 1:at_sign])
        phone = urllib.parse.unquote(url[at_sign + 1:])
        if not username or not password or not phone:
            return "", "", ""
        return username, password, phone

    def load_credentials(self, private_channels: model.LanguageChannels, lang):
        channel_url = self.channels.load(lang)
        if private_channels:
            channel_url = private_channels.load(lang)
        return self.parse_channel_url(channel_url)


class FilterBuilder:
    def __init__(self):
        self.filter_spec = []
        self.sort_spec = []
        self.pagination_spec = {}

    def _add_filter(self, field, operation, value):
        self.filter_spec.append({"field": field, "op": operation, "value": value})

    def eq(self, field, value):
        self._add_filter(field, "==", value)
        return self

    def lte(self, field, value):
        self._add_filter(field, "<=", value)
        return self

    def order_by(self, field):
        if field.startswith("-"):
            self.sort_spec.append(
                {"field": field[1:], "direction": "desc", "nullslast": True}
            )
        else:
            self.sort_spec.append(
                {"field": field, "direction": "asc", "nullsfirst": True}
            )
        return self

    def page(self, page, per_page):
        self.pagination_spec = {"page": int(page), "per_page": int(per_page)}
        return self

    def build(self):
        return {
            "filter_spec": self.filter_spec,
            "sort_spec": self.sort_spec,
            "pag_spec": self.pagination_spec,
        }


class CourseCategoryService(AbstractDatabaseService):
    def __init__(self, db):
        super().__init__(db, model.CourseCategory)


class CourseWorkspaceService(AbstractDatabaseService):
    def __init__(self, db):
        super().__init__(db, model.CourseWorkspace)


class WorkspaceService(AbstractDatabaseService):
    def __init__(self, db):
        super().__init__(db, model.Workspace)

    @staticmethod
    def _processing_cycle_days(date):
        """
        Define the day of cycle by the date specified.
        If the date is march 1th, 2019... the cycle day for processing is: 29, 30, 31, 1
        Because the routine ran yesterday only processed the day 02/28/2109
        :param date: reference date
        :return: [] with the cycle days
        """
        days = []
        if date.day == 1:
            yesterday = date - relativedelta(days=1)
            for day in range(yesterday.day + 1, 32):
                days.append(day)
        days.append(date.day)
        return days

    @staticmethod
    def _processing_cycle_period(date, cycle_day):
        """
        Define the start and end date of the cycle.
        The cycle is defined before the reference date
        Its the closed period
        :param date: Reference date
        :param cycle_day:
        :return:
        """
        start_at = date + relativedelta(months=-1)
        end_at = date + relativedelta(days=-1)
        if date.day == 1 and date.day != cycle_day:
            start_at = date + relativedelta(months=-2, day=cycle_day)
            if start_at.day != cycle_day:
                start_at += relativedelta(days=1)
        start_at += relativedelta(hour=0, minute=0, second=0)
        end_at += relativedelta(hour=23, minute=59, second=59)
        return start_at, end_at

    def _can_process_billing_period(self, workspace_id, start_at, end_at):
        billing = (
            self.db.query(model.Billing)
            .filter(model.Billing.start_at == start_at)
            .filter(model.Billing.end_at == end_at)
            .filter(model.Billing.workspace_id == workspace_id)
            .all()
        )
        return not billing

    def process_billing_charges(self, date):
        for day in self._processing_cycle_days(date):
            companies = (
                self.db.query(model.Workspace)
                .filter(model.Workspace.billing_cycle_day == day)
                .all()
            )
            for workspace in companies:
                start_at, end_at = self._processing_cycle_period(
                    date, workspace.billing_cycle_day
                )
                if self._can_process_billing_period(workspace.id, start_at, end_at):
                    self._create_charges(workspace, start_at, end_at)

    def _create_charges(self, workspace, start_at, end_at):
        with database.transaction(self.db):
            messages_sent_count = self._load_messages_sent_count(
                workspace.id, start_at, end_at
            )
            charge = model.Billing()
            charge.workspace_id = workspace.id
            charge.start_at = start_at
            charge.end_at = end_at
            charge.monthly_plan = workspace.monthly_plan
            charge.billing_cycle_day = workspace.billing_cycle_day
            charge.balance = workspace.monthly_plan - messages_sent_count
            charge.used = messages_sent_count
            self.db.add(charge)

    def _load_messages_sent_count(self, workspace_id, start_at, end_at):
        query = (
            self.db.query(model.Schedule)
            .filter(model.Schedule.workspace_id == workspace_id)
            .filter(model.Schedule.status == model.ScheduleStatus.delivered)
            .filter(model.Schedule.send_date >= start_at)
            .filter(model.Schedule.send_date <= end_at)
        )
        return query.count()

    @staticmethod
    def _open_cycle_period(date, cycle_day):
        """
        Define the start date of the current opened cycle.
        :param date: Reference date
        :param cycle_day:
        :return:
        """
        if not date or not cycle_day:
            return None, None

        _, month_days = calendar.monthrange(date.year, date.month)
        start_at = date + relativedelta(day=cycle_day)
        if cycle_day > date.day:
            start_at = date + relativedelta(months=-1, day=cycle_day)
        end_at = start_at + relativedelta(days=month_days)
        end_at += relativedelta(day=cycle_day - 1)

        if start_at.day != cycle_day:
            start_at += relativedelta(days=1)
        if end_at.day == cycle_day:
            end_at += relativedelta(days=-1)

        start_at += relativedelta(hour=0, minute=0, second=0)
        end_at += relativedelta(hour=23, minute=59, second=59)
        return start_at, end_at

    def billing_report(self, workspace):
        today = datetime.date.today()
        current_start_date, current_end_date = self._open_cycle_period(
            today, workspace.billing_cycle_day
        )
        sent_count = self._load_messages_sent_count(
            workspace.id, current_start_date, current_end_date
        )
        previous_charges = (
            self.db.query(model.Billing)
            .filter(model.Billing.workspace_id == workspace.id)
            .order_by(model.Billing.start_at.desc())
            .limit(2)
            .all()
        )
        balance = sum([c.balance for c in previous_charges])
        current_balance = (workspace.monthly_plan + balance) - sent_count
        current_billing = {"start_at": current_start_date, "end_at": current_end_date}
        return {
            "monthly_plan": workspace.monthly_plan,
            "zaps_sent": sent_count,
            "available_zaps": current_balance,
            "current_billing": current_billing,
            "charges": previous_charges,
        }

    def load_billing_charges(self, workspace_id):
        return (
            self.db.query(model.Billing)
            .filter(model.Billing.workspace_id == workspace_id)
            .order_by(model.Billing.start_at.desc())
            .all()
        )

    def check_workspace_exist(self, workspace_id):
        return bool(self.db.query(model.Workspace).filter(model.Workspace.id == workspace_id).first())


class ContentTypeService(AbstractDatabaseService):
    def __init__(self, db):
        super().__init__(db, model.ContentType)


class CourseService(AbstractDatabaseService):
    def __init__(self, db, client_id, user_logged_id):
        super().__init__(db, model.Course)
        self.client_id = client_id
        self.user_logged_id = user_logged_id

    def add(self, course):
        with database.transaction(self.db):
            workspace = (
                self.db.query(model.Workspace)
                .filter(model.Workspace.id == self.client_id)
                .first()
            )
            user = (
                self.db.query(model.User)
                .filter(model.User.id == self.user_logged_id)
                .first()
            )
            if not workspace:
                raise NotFoundException("Workspace not found for course association")
            if not user:
                raise NotFoundException(
                    "User not found for course association. "
                    "Check if user logged in exist in Smartzap database"
                )
            course.companies.append(workspace)
            course.workspace_owner_id = workspace.id
            course.user_creator_id = user.id
            self.db.add(course)
            return course

    def load_filters(self, filters):
        query = self.db.query(model.Course).join(model.CourseWorkspace)
        query = query.filter(model.CourseWorkspace.workspace_id == self.client_id)
        return FilterResolver(model.Course).exec_query_filters(query, filters)

    def load(self, course_id):
        query = self.db.query(model.Course).join(model.CourseWorkspace)
        query = query.filter(
            model.Course.id == course_id,
            model.CourseWorkspace.workspace_id == self.client_id,
        )
        course = query.first()
        if not course:
            raise NotFoundException(f"{self.model_type.__name__} not found")
        return course

    def load_content(self, content_id):
        content = (
            self.db.query(model.Content).filter(model.Content.id == content_id).first()
        )
        if not content:
            raise NotFoundException(f"{model.Content.__name__} not found")
        course_id = content.lesson.course.id
        try:
            _ = self.load(course_id)
        except NotFoundException:
            raise NotFoundException(f"{model.Content.__name__} not found")
        return content

    def load_all_enrollments(self, course_id, filters):
        course = self.load(course_id)
        query = self.db.query(model.Enrollment).filter(
            model.Enrollment.course_id == course.id
        )
        return FilterResolver(model.Enrollment).exec_query_filters(query, filters)

    def load_all_lessons(self, course_id, filters):
        course = self.load(course_id)
        if not filters.get("sort_spec", None):
            filters["sort_spec"] = [{"field": "order", "direction": "asc"}]
        query = self.db.query(model.Lesson).filter(model.Lesson.course_id == course.id)
        return FilterResolver(model.Lesson).exec_query_filters(query, filters)

    def update(self, course_id, course_update):
        course = self.load(course_id)
        self.validate_changes_allowed(course, "update")
        with database.transaction(self.db):
            course_update.id = course_id
            return self.db.merge(course_update)

    def transfer_ownership(self, course_id, user_id):
        course = self.load(course_id)
        self.validate_changes_allowed(course, "update")
        with database.transaction(self.db):
            course.user_creator_id = user_id
            return self.db.merge(course)

    def schedule_delete(self, course_id):
        course = self.load(course_id)
        self.validate_changes_allowed(course, "delete")
        with database.transaction(self.db):
            course.status = model.CourseStatus.deleting
        course_delete_signal.emit(
            "delete",
            course=course,
            client_id=self.client_id,
            user_id=self.user_logged_id,
        )

    def validate_changes_allowed(self, course, action):
        if course.workspace_owner_id != self.client_id:
            raise IntegrityException(
                f"Unable to {action} the course. Only workspace owner can {action}."
            )
        if course.is_immutable():
            raise IntegrityException(
                f"Unable to {action} the course. The course is {course.status}."
            )

    def delete(self, course_id):
        course = self.load(course_id)
        if course.workspace_owner_id != self.client_id:
            raise IntegrityException(
                "Unable to delete the course. Only workspace owner can delete."
            )
        with database.transaction(self.db):
            self.delete_schedule_by_course_id(course_id)
            self.db.delete(course)

    def delete_schedule_by_course_id(self, course_id):
        with database.transaction(self.db):
            enrollment_by_course = self.db.query(model.Enrollment).filter(
                model.Enrollment.course_id == course_id
            ).all()
            self.db.query(model.Schedule).filter(

                    model.Schedule.status == model.ScheduleStatus.pending,
                    model.Schedule.enrollment_id.in_([e.id for e in enrollment_by_course])

            ).delete(synchronize_session='fetch')

    def load_course_and_lang(self, course_id):
        course = (
            self.db.query(model.Course).filter(model.Course.id == course_id).first()
        )
        if not course:
            return None, ""
        lang = course.lang or model.default_lang
        return course, lang

    def publish(self, course_id):
        course = self.load(course_id)
        with database.transaction(self.db):
            course.status = model.CourseStatus.processing


class LessonService(AbstractDatabaseService):
    def __init__(self, db):
        super().__init__(db, model.Lesson)

    def load_all_contents(self, lesson_id, filters):
        query = self.db.query(model.Content).filter(
            model.Content.lesson_id == lesson_id
        )
        contents = FilterResolver(model.Content).exec_query_filters(query, filters)

        if contents:
            _sorted = sorted(
                contents[0],
                key=lambda x: (
                    x.dispatch_in,
                    x.dispatch_period != model.ContentPeriod.morning,
                    x.dispatch_period,
                ),
            )
            return _sorted, contents[1]

        return contents


class ContentService(AbstractDatabaseService):
    def __init__(self, db):
        super().__init__(db, model.Content)

    def delete_with_kontent(self, obj_id, kontent_service):
        obj = self.load(obj_id)
        workspace_owner_id = obj.lesson.course.workspace_owner_id

        with database.transaction(self.db):
            self.db.delete(obj)

        if obj.type.name == "Question":
            result = kontent_service.delete_exam(
                obj.learn_content, workspace_owner_id
            )
            logger.info("Learn Content deleted on Kontent: {}".format(str(result)))
        else:
            result = kontent_service.delete_content(
                obj.learn_content, workspace_owner_id
            )
            logger.info("Learn Content deleted on Kontent: {}".format(str(result)))

    def load_all_activities(self, content_id, filters):
        query = self.db.query(model.Activity).filter(
            model.Activity.content_id == content_id
        )
        return FilterResolver(model.Activity).exec_query_filters(query, filters)


class UserService(AbstractDatabaseService):
    def __init__(self, db, client_id):
        super().__init__(db, model.User)
        self.client_id = client_id

    def add(self, user):
        db_user = (
            self.db.query(model.User).filter(model.User.phone == user.phone).first()
        )
        with database.transaction(self.db):
            if not db_user:
                self.db.add(user)
                self.db.flush()
            else:
                user.id = db_user.id
                user = self.db.merge(user)

            query = self.db.query(model.UserWorkspace)
            query = query.filter(
                model.UserWorkspace.user_id == user.id,
                model.UserWorkspace.workspace_id == self.client_id,
            )
            user_workspace = query.first()
            if user_workspace:
                return user

            user_workspace = model.UserWorkspace()
            user_workspace.user_id = user.id
            user_workspace.workspace_id = self.client_id
            self.db.add(user_workspace)
            return user

    def load(self, user_id):
        query = self.db.query(model.User).join(model.UserWorkspace)
        query = query.filter(
            model.User.id == user_id, model.UserWorkspace.workspace_id == self.client_id
        )
        user = query.first()
        if not user:
            raise NotFoundException(f"{self.model_type.__name__} not found")
        return user

    def load_filters(self, filters):
        query = self.db.query(model.User).join(model.UserWorkspace)
        query = query.filter(model.UserWorkspace.workspace_id == self.client_id)
        return FilterResolver(model.User).exec_query_filters(query, filters)

    def count_users(self):
        query = self.db.query(model.User).join(model.UserWorkspace)
        return query.filter(model.UserWorkspace.workspace_id == self.client_id).count()

    def delete(self, user_id):
        query = self.db.query(model.UserWorkspace)
        query = query.filter(
            model.UserWorkspace.user_id == user_id,
            model.UserWorkspace.workspace_id == self.client_id,
        )
        user_workspace = query.first()

        if not user_workspace:
            raise NotFoundException("UserWorkspace not found")

        with database.transaction(self.db):
            self.db.delete(user_workspace)
            self.db.query(model.Enrollment).filter(
                model.Enrollment.user_id == user_id,
                model.Enrollment.workspace_id == self.client_id,
            ).delete()

    def update(self, user_id, obj):
        query = self.db.query(model.User)
        query = query.filter(model.User.id == user_id)
        user = query.first()

        if not user:
            raise NotFoundException(f"{self.model_type.__name__} not found")

        with database.transaction(self.db):
            obj.id = user_id
            return self.db.merge(obj)

    def load_unprotected_in(self, users_id):
        """
        Do not validate the client_id access. It's unprotected for helping others services. Do not use directly to api
        :param users_id:
        :return: list
        """
        return self.db.query(model.User).filter(model.User.id.in_(users_id)).all()

    def check_phone_exist(self, phone):
        db_user = bool(
            self.db.query(model.User).filter(model.User.phone == phone).first()
        )
        return db_user

    def get_by_phone(self, phone: str, raise_exception: bool = False):
        db_user = self.db.query(model.User).filter(model.User.phone == phone).first()
        if not db_user and raise_exception:
            raise UserNotFound('User not found for the given phone number {}'.format(phone))
        return db_user

    def link_workspace(self, user_id, workspace_id):
        with database.transaction(self.db):
            query = self.db.query(model.UserWorkspace)
            query = query.filter(
                model.UserWorkspace.user_id == user_id,
                model.UserWorkspace.workspace_id == workspace_id,
            )
            user_workspace = query.first()

            if user_workspace:
                return user_workspace

            user_workspace = model.UserWorkspace()
            user_workspace.user_id = user_id
            user_workspace.workspace_id = workspace_id
            self.db.add(user_workspace)
            return user_workspace

    def get_user_workspace_ids(self, user_id: str) -> List[str]:
        return self.db.query(model.UserWorkspace.workspace_id).filter(model.UserWorkspace.user_id == user_id).all()


class EnrollmentService(AbstractDatabaseService):
    def __init__(self, db, schedule_service, client_id=None):
        super().__init__(db, model.Enrollment)
        self.client_id = client_id
        self.schedule_service = schedule_service

    def load_enrollment_stats(self, filters: dict) -> dict:
        enrollments = self.apply_filters(filters).filter(
            model.Enrollment.workspace_id == self.client_id
        )
        started_count = enrollments.filter(
            model.Enrollment.status == model.EnrollmentStatus.started
        ).count()
        waiting_count = enrollments.filter(
            model.Enrollment.status == model.EnrollmentStatus.waiting
        ).count()

        return {"started_count": started_count, "waiting_count": waiting_count}

    def load_messages_pending_count(self, filters: dict) -> dict:
        enrollments = (
            self.apply_filters(filters)
            .filter(model.Enrollment.workspace_id == self.client_id)
            .subquery()
        )
        return (
            self.db.query(model.Schedule)
            .filter(model.Schedule.status == model.ScheduleStatus.pending)
            .join(enrollments, model.Schedule.enrollment_id == enrollments.c.id)
            .count()
        )

    @staticmethod
    def _is_id_equal(lhs, rhs):
        if not rhs:
            return True
        return lhs == rhs

    def add(self, enrollment):
        self.check_enrollment_available(enrollment.course_id, enrollment.user_id)
        with database.transaction(self.db):
            if not enrollment.start_date:
                enrollment.start_date = datetime.date.today()
            self.db.add(enrollment)
        self._create_schedules_by_status(enrollment)
        return enrollment

    def _create_schedules_by_status(self, enrollment):
        if enrollment.status == EnrollmentStatus.waiting:
            return self.schedule_service.create_welcome_schedules(enrollment)
        if enrollment.status == EnrollmentStatus.started:
            return self.schedule_service.create_schedules(enrollment)

    def check_enrollment_available(self, course_id, user_id):
        """
        Checks if the user can be enrolled in the course.

        Roles:
         - Course exist
         - Course must have completed its analysis and be published (status=FINISHED)
         - User can not have 2 enrollment in progress (started ou waiting).
        """
        course = (
            self.db.query(model.Course).filter(model.Course.id == course_id).first()
        )
        if not course:
            raise NotFoundException("Course not found")
        if course.status != model.CourseStatus.finished:
            raise IntegrityException(
                "Unable to enroll in the course. The course is not FINISHED."
            )

        enrollment = (
            self.db.query(model.Enrollment)
            .filter(
                model.Enrollment.user_id == user_id,
                model.Enrollment.status.in_(
                    (model.EnrollmentStatus.waiting, model.EnrollmentStatus.started)
                ),
            )
            .all()
        )
        if len(enrollment) > 0:
            raise IntegrityException(
                "The user has an enrollment in progress. "
                "Users can not be enrolled in the same or another course when has an enrollment "
                "completed or drop outed."
            )

    @staticmethod
    def _can_update_ref_ids(enrollment_update_data, current_enrollment):
        if (enrollment_update_data.course_id and enrollment_update_data.course_id != current_enrollment.course_id):
            return False
        if (enrollment_update_data.user_id and enrollment_update_data.user_id != current_enrollment.user_id):
            return False
        return True

    def update(self, enrollment_id, enrollment):
        current_enrollment = self.load(enrollment_id)
        if not self._can_update_ref_ids(enrollment, current_enrollment):
            raise IntegrityException(
                "It is not possible to change the course or user enrolled. "
                "You must delete this enrollment and create another."
            )
        start_date_changed = (
            current_enrollment.start_date != enrollment.start_date
            if enrollment.start_date
            else False
        )
        timezone_changed = (
            current_enrollment.timezone != enrollment.timezone
            if enrollment.timezone
            else False
        )
        status_changed = (
            current_enrollment.status != enrollment.status
            if enrollment.status
            else False
        )
        if (start_date_changed and current_enrollment.status != model.EnrollmentStatus.waiting):
            raise IntegrityException(
                "The Enrollment start_date only can be changed when status is WAITING"
            )

        if (timezone_changed and current_enrollment.status != model.EnrollmentStatus.waiting):
            raise IntegrityException("The Enrollment timezone only can be changed when status is WAITING")

        if status_changed and not current_enrollment.can_change_status(
            enrollment.status
        ):
            raise IntegrityException(
                "The Enrollment status can not be changed from "
                f"{current_enrollment.status} to {enrollment.status}"
            )

        with database.transaction(self.db):
            enrollment.id = enrollment_id
            enrollment = self.db.merge(enrollment)
            if not status_changed:
                return enrollment
            if enrollment.status == model.EnrollmentStatus.started:
                self.schedule_service.create_schedules(enrollment)
            if enrollment.status == model.EnrollmentStatus.canceled:
                self.schedule_service.delete_schedules_from_enrollment_id(enrollment.id)
        return enrollment

    def load_first_active_from_phone(self, phone: str):
        user = self.db.query(model.User).filter(model.User.phone == phone).first()
        if not user:
            return None
        return self._load_first_active(user)

    def load_first_active_from_user(self, user_id: str) -> Optional[model.Enrollment]:
        user = self.db.query(model.User).filter(model.User.id == user_id).first()
        if not user:
            return None
        return self._load_first_active(user)

    def _load_first_active(self, user: model.User) -> Optional[model.Enrollment]:
        return (
            self.db.query(model.Enrollment)
            .filter(
                model.Enrollment.user_id == user.id,
                model.Enrollment.status.in_(
                    (model.EnrollmentStatus.waiting, model.EnrollmentStatus.started)
                ),
            )
            .order_by(model.Enrollment.created.asc())
            .first()
        )

    def load_last_completed_from_phone(self, phone):
        user = self.db.query(model.User).filter(model.User.phone == phone).first()
        if not user:
            return None
        return (
            self.db.query(model.Enrollment)
            .filter(
                model.Enrollment.user_id == user.id,
                model.Enrollment.status == model.EnrollmentStatus.completed
            )
            .order_by(model.Enrollment.end_date.desc(), model.Enrollment.created.desc())
            .first()
        )

    def accept_disclaimer(self, enrollment):
        update_enrollment = model.Enrollment()
        update_enrollment.status = model.EnrollmentStatus.started
        self.update(enrollment.id, update_enrollment)
        return enrollment

    def reject_disclaimer(self, enrollment):
        update_enrollment = model.Enrollment()
        update_enrollment.status = model.EnrollmentStatus.refused
        self.update(enrollment.id, update_enrollment)
        return enrollment

    def complete_course(self, enrollment_id, end_date, performance, points):
        enrollment = self.load(enrollment_id)
        with database.transaction(self.db):
            enrollment.status = model.EnrollmentStatus.completed
            enrollment.end_date = end_date
            enrollment.performance = round_performance(performance)
            enrollment.points = round(points)

    def cancel(self, enrollment: Enrollment):
        with database.transaction(self.db):
            enrollment.status = model.EnrollmentStatus.canceled
            self.schedule_service.delete_schedules_from_enrollment_id(enrollment.id)
        return enrollment

    def register_current_lesson(self, enrollment_id, lesson_id):
        enrollment = self.load(enrollment_id)
        with database.transaction(self.db):
            enrollment.current_lesson_id = lesson_id

    def register_current_content(self, enrollment_id, content_id, lesson_id):
        enrollment = self.load(enrollment_id)
        with database.transaction(self.db):
            enrollment.current_content_id = content_id
            enrollment.current_lesson_id = lesson_id

    def user_can_access_contents(self, subject):
        """
        Check if the sub is an active enrollment, if do not, try to find an user if the sub.
        If the user exists, the access is allowed. This is a compatibility for old tokens
        Only access contents the active enrollments
        :param subject:
        """
        enrollment = (
            self.db.query(model.Enrollment)
            .filter(model.Enrollment.id == subject)
            .first()
        )
        if enrollment and enrollment.status == model.EnrollmentStatus.started:
            return True
        if not enrollment:
            user_enrollments = (
                self.db.query(model.Enrollment)
                .filter(model.Enrollment.user_id == subject)
                .filter(model.Enrollment.status == model.EnrollmentStatus.started)
                .all()
            )
            if user_enrollments:
                return True
        return False

    def load_course(self, enrollment):
        return (
            self.db.query(model.Course)
            .filter(model.Course.id == enrollment.course_id)
            .first()
        )

    def update_progress(self, enrollment_id):
        enrollment = self.load(enrollment_id)
        content_schedules = [
            s
            for s in enrollment.schedules
            if s.type == model.ScheduleType.lesson_content
        ]
        if not content_schedules:
            return 100
        total_contents = len(content_schedules)
        total_delivered = sum([1 for s in content_schedules if s.is_sent()])
        enrollment.progress = min(100, round((100 * total_delivered) / total_contents))
        with database.transaction(self.db):
            return self.db.merge(enrollment)

    def cancel_in_batch(self, enrollments: Query):
        with database.transaction(self.db):
            for enrollment in enrollments:
                enrollment.status = model.EnrollmentStatus.canceled
                self.schedule_service.delete_schedules_from_enrollment_id(enrollment.id)

    def update_certificate_url(self, enrollment_id, certificate_url):
        enrollment = self.load(enrollment_id)
        with database.transaction(self.db):
            enrollment.certificate_url = certificate_url

    def load_last_enrollment_completed_from_phone(self, phone):
        user = self.db.query(model.User).filter(model.User.phone == phone).first()
        if not user:
            return None
        return (
            self.db.query(model.Enrollment)
            .filter(
                model.Enrollment.user_id == user.id,
                model.Enrollment.status == model.EnrollmentStatus.completed,
            )
            .order_by(model.Enrollment.created.desc())
            .first()
        )

    def delete(self, enrollment_id):
        enrollment = self.load(enrollment_id)
        with database.transaction(self.db):
            self.db.delete(enrollment)


class ActivityService(AbstractDatabaseService):
    def __init__(self, db):
        super().__init__(db, model.Activity)

    @staticmethod
    def _get_duration(start_at, stop_at):
        if start_at and stop_at:
            return int(
                (
                    stop_at.replace(tzinfo=tz.UTC) - start_at.replace(tzinfo=tz.UTC)
                ).total_seconds()
            )
        return None

    def add(self, activity):
        enrollment = (
            self.db.query(model.Enrollment)
            .filter(model.Enrollment.id == activity.enrollment_id)
            .first()
        )
        activity.user_id = enrollment.user_id if enrollment else None
        activity.duration = self._get_duration(activity.start_at, activity.stop_at)
        with database.transaction(self.db):
            self.db.add(activity)
            return activity

    def update(self, activity_id, activity):
        current_activity = self.load(activity_id)
        start_at = activity.start_at or current_activity.start_at
        stop_at = activity.stop_at or current_activity.stop_at
        activity.duration = self._get_duration(start_at, stop_at)
        with database.transaction(self.db):
            activity.id = activity_id
            return self.db.merge(activity)


class FeedbackService(AbstractDatabaseService):
    def __init__(self, db):
        super().__init__(db, model.Feedback)


class ScheduleService(AbstractDatabaseService):
    def __init__(self, db, client_id):
        super().__init__(db, model.Schedule)
        self.client_id = client_id

    def send_know(self, schedule: model.Schedule):
        task = DISPATCH_MESSAGE
        if schedule.external_sender:
            task = DISPATCH_MESSAGE_BY_EXTERNAL
        app.send_task(task, args=(schedule.id,), countdown=Config.COUNTDOWN_TO_SEND_FIRST_MESSAGE)

    def _create_schedule(
        self, enrollment, schedule_type, reference_id, send_date, lang, tmz
    ) -> model.Schedule:
        schedule = model.Schedule()
        schedule.id = str(uuid.uuid4())
        schedule.enrollment_id = enrollment.id
        schedule.reference_id = reference_id
        schedule.type = schedule_type
        schedule.send_date = send_date
        schedule.lang = lang
        schedule.timezone = tmz
        schedule.workspace_id = enrollment.workspace_id
        schedule.external_sender = enrollment.workspace.messages_by_twilio_studio
        self.db.add(schedule)
        return schedule

    def _create_new_enrollment_schedule(self, enrollment, lang, tmz):
        utc_now = datetime.datetime.utcnow()
        schedule = self._create_schedule(
            enrollment,
            model.ScheduleType.course_introduction,
            enrollment.course_id,
            utc_now + datetime.timedelta(minutes=1),
            lang,
            tmz,
        )
        self.send_know(schedule)

    def create_welcome_schedules(self, enrollment):
        course = (
            self.db.query(model.Course)
            .filter(model.Course.id == enrollment.course_id)
            .first()
        )
        if not course:
            raise NotFoundException("Course not found")

        with database.transaction(self.db):
            lang = course.lang
            timezone = enrollment.timezone or "UTC"
            self._create_new_enrollment_schedule(enrollment, lang, timezone)

    def create_post_schedule(self, enrollment: Enrollment, schedule_type: model.ScheduleType, delay_minutes=1):
        course_lang = (
            self.db.query(model.Course.lang)
            .filter(model.Course.id == enrollment.course_id)
            .scalar()
        )

        with database.transaction(self.db):
            timezone = enrollment.timezone or pytz.UTC.__str__()
            utc_now = datetime.datetime.now(pytz.UTC)
            send_date = utc_now + datetime.timedelta(minutes=delay_minutes)

            self._create_schedule(
                enrollment,
                schedule_type,
                enrollment.course_id,
                send_date,
                course_lang,
                timezone,
            )

    def create_nps_survey_schedule(self, enrollment: Enrollment):
        course_lang = (
            self.db.query(model.Course.lang)
            .filter(model.Course.id == enrollment.course_id)
            .scalar()
        )
        with database.transaction(self.db):
            timezone = enrollment.timezone or pytz.UTC.__str__()
            utc_now = datetime.datetime.now(pytz.UTC)
            self._create_schedule(
                enrollment,
                model.ScheduleType.course_nps_survey,
                enrollment.course_id,
                utc_now + datetime.timedelta(minutes=1),
                course_lang,
                timezone,
            )

    def create_schedules(self, enrollment: model.Enrollment):
        course = (
            self.db.query(model.Course)
            .filter(model.Course.id == enrollment.course_id)
            .first()
        )
        if not course:
            raise NotFoundException("Course not found")

        with database.transaction(self.db):
            lang = course.lang
            timezone = enrollment.timezone or "UTC"
            lessons = (
                self.db.query(model.Lesson)
                .filter(model.Lesson.course_id == enrollment.course_id)
                .order_by(model.Lesson.order)
                .all()
            )

            max_dispatch_days = 0
            for lesson in lessons:
                contents = (
                    self.db.query(model.Content)
                    .filter(model.Content.lesson_id == lesson.id)
                    .order_by(model.Content.dispatch_in.asc())
                    .all()
                )

                contents = sorted(
                    contents,
                    key=lambda c: (
                        c.dispatch_in,
                        c.dispatch_period != model.ContentPeriod.morning,
                        c.dispatch_period,
                    ),
                )

                for content in contents:
                    max_dispatch_days = max(max_dispatch_days, content.dispatch_in)
                    send_date = self._adjust_send_datetime(
                        enrollment.start_date,
                        content.dispatch_in,
                        content.get_dispatch_time(),
                        timezone,
                    )
                    self._create_schedule(
                        enrollment,
                        model.ScheduleType.lesson_content,
                        content.id,
                        send_date,
                        lang,
                        timezone,
                    )

            if max_dispatch_days:
                end_course_days = enrollment.workspace.end_course_schedule
                send_date = None
                if not enrollment.workspace.messages_by_twilio_studio:
                    send_date = self._adjust_send_datetime(
                        enrollment.start_date,
                        max_dispatch_days + end_course_days,
                        model.ContentPeriod.morning_time(),
                        timezone,
                    )
                self._create_schedule(
                    enrollment,
                    model.ScheduleType.course_end,
                    enrollment.course_id,
                    send_date,
                    lang,
                    timezone,
                )

    def delete_schedules_from_enrollment_id(self, enrollment_id):
        with database.transaction(self.db):
            self.db.query(model.Schedule).filter(
                model.Schedule.enrollment_id == enrollment_id,
                model.Schedule.status == model.ScheduleStatus.pending,
            ).delete()

    def _adjust_send_datetime(self, enrollment_date, days, time, tmz):
        send_date = enrollment_date + datetime.timedelta(days=days)
        timezone = tz.gettz(tmz)
        send_datetime = datetime.datetime.combine(send_date, time).replace(
            tzinfo=timezone
        )
        return self._datetime_as_utc(send_datetime)

    @staticmethod
    def _datetime_as_utc(dt):
        utc_timezone = tz.gettz("UTC")
        return dt.astimezone(utc_timezone)

    def load_pending_schedules(self, page, per_page, external_sender: bool = False):
        offset = (page - 1) * per_page
        limit = per_page
        pending_schedules = (
            self.db.query(model.Schedule)
            .filter(model.Schedule.send_date <= datetime.datetime.utcnow())
            .filter(model.Schedule.status == model.ScheduleStatus.pending)
            .filter(model.Schedule.external_sender == external_sender)
            .order_by(model.Schedule.send_date.asc())
            .limit(limit)
            .offset(offset)
            .all()
        )
        return pending_schedules or []

    def has_content_pending(self, enrollment_id):
        return (
            self.db.query(model.Schedule)
            .filter(model.Schedule.enrollment_id == enrollment_id)
            .filter(model.Schedule.type == model.ScheduleType.lesson_content)
            .filter(model.Schedule.status == model.ScheduleStatus.pending)
            .count() > 0
        )

    def anticipate_next_content(self, enrollment_id):
        course = self.db.query(model.Enrollment).get(enrollment_id).course
        if not course.allow_content_anticipation:
            return "nothing", ""
        with database.transaction(self.db):
            schedule_id = ""
            next_schedules = (
                self.db.query(model.Schedule)
                .filter(model.Schedule.enrollment_id == enrollment_id)
                .filter(model.Schedule.status == model.ScheduleStatus.pending)
                .order_by(model.Schedule.send_date.asc())
                .limit(2)
                .all()
            )
            if not next_schedules:
                return "nothing", schedule_id
            schedule_1 = next_schedules[0]
            if schedule_1.type == model.ScheduleType.course_end:
                return "waiting_end", schedule_id

            status = "nothing"
            if schedule_1.type == model.ScheduleType.lesson_content and not schedule_1.anticipated:
                schedule_id = schedule_1.id
                schedule_1.anticipated = True
                if schedule_1.send_date < datetime.datetime.utcnow():
                    return "anticipated", schedule_id

                schedule_1.send_date = datetime.datetime.utcnow()
                status = "anticipated"
            if len(next_schedules) == 2:
                schedule_2 = next_schedules[1]
                if schedule_2.type == model.ScheduleType.course_end and not schedule_2.anticipated:
                    end_course_days = (
                        schedule_2.enrollment.workspace.end_course_schedule
                    )
                    schedule_2.send_date = self._adjust_send_datetime(
                        datetime.datetime.today(),
                        end_course_days,
                        model.ContentPeriod.morning_time(),
                        schedule_2.timezone,
                    )
            return status, schedule_id

    def anticipate_course_end(self, enrollment_id):
        with database.transaction(self.db):
            schedule_id = ""
            next_schedules = (
                self.db.query(model.Schedule)
                .filter(model.Schedule.enrollment_id == enrollment_id)
                .filter(model.Schedule.status == model.ScheduleStatus.pending)
                .order_by(model.Schedule.send_date.asc())
                .limit(1)
                .all()
            )
            if not next_schedules:
                return "nothing", schedule_id
            schedule = next_schedules[0]
            if schedule.type != model.ScheduleType.course_end:
                return "content_pending", schedule_id

            status = "nothing"
            send_date_able_to_anticipation = (not schedule.send_date or schedule.send_date >= datetime.datetime.utcnow())
            if schedule.type == model.ScheduleType.course_end and not schedule.anticipated and send_date_able_to_anticipation:
                schedule.send_date = datetime.datetime.utcnow()
                schedule.anticipated = True
                status = "anticipated"
                schedule_id = schedule.id
            return status, schedule_id

    def give_up_enrollment(self, enrollment_id):
        enrollment = self.load_model(model.Enrollment, enrollment_id)
        with database.transaction(self.db):
            enrollment.status = model.EnrollmentStatus.dropout
            self.delete_schedules_from_enrollment_id(enrollment.id)

    def load_enrollment_and_user(self, enrollment_id):
        enrollment = self.load_model(model.Enrollment, enrollment_id)
        user = self.load_model(model.User, enrollment.user_id)
        return enrollment, user

    def load_model(self, model_type, model_id):
        obj = self.db.query(model_type).filter(model_type.id == model_id).first()
        if not obj:
            raise NotFoundException(f"{model_type.__name__} not found: {model_id}")
        return obj

    def set_delivered(self, schedule_id):
        schedule = self.load(schedule_id)
        with database.transaction(self.db):
            schedule.status = model.ScheduleStatus.delivered

    def set_failed(self, schedule_id):
        schedule = self.load(schedule_id)
        with database.transaction(self.db):
            schedule.status = model.ScheduleStatus.failed

    def load_content_activities_by_user(self, enrollment_id, content_id):
        query = self.db.query(model.Activity)
        query = query.filter(model.Activity.enrollment_id == enrollment_id)
        query = query.filter(model.Activity.content_id == content_id)
        return query.all()

    def re_schedule_content(self, content, send_date, period):
        if not self.client_id:
            raise IntegrityException("Client ID not defined")

        offset = 0
        limit = 20
        processing = True
        while processing:
            with database.transaction(self.db):
                pending_schedules = (
                    self.db.query(model.Schedule)
                    .filter(model.Schedule.status == model.ScheduleStatus.pending)
                    .filter(model.Schedule.type == model.ScheduleType.lesson_content)
                    .filter(model.Schedule.workspace_id == self.client_id)
                    .filter(model.Schedule.reference_id == content.id)
                    .limit(limit)
                    .offset(offset)
                    .all()
                )
                for schedule in pending_schedules:
                    send_date = self._adjust_send_datetime(
                        send_date,
                        0,
                        model.ContentPeriod.content_time(period),
                        schedule.timezone,
                    )
                    schedule.send_date = send_date
                    end_schedule = (
                        self.db.query(model.Schedule)
                        .filter(model.Schedule.status == model.ScheduleStatus.pending)
                        .filter(model.Schedule.type == model.ScheduleType.course_end)
                        .filter(model.Schedule.enrollment_id == schedule.enrollment_id)
                        .first()
                    )
                    if not end_schedule:
                        continue
                    delta = self._datetime_as_utc(end_schedule.send_date) - schedule.send_date
                    end_course_days = (
                        end_schedule.enrollment.workspace.end_course_schedule
                    )
                    if delta < datetime.timedelta(days=end_course_days):
                        end_schedule.send_date = self._adjust_send_datetime(
                            send_date,
                            end_course_days,
                            model.ContentPeriod.morning_time(),
                            end_schedule.timezone,
                        )
                offset += limit
                processing = len(pending_schedules) > 0

    def load_reference_status(self, enrollment_id, reference_id):
        query = self.db.query(model.Schedule)
        query = query.filter(model.Schedule.enrollment_id == enrollment_id)
        query = query.filter(model.Schedule.reference_id == reference_id)
        schedule = query.first()
        if schedule:
            return schedule.status
        return ""

    def resend_content(self, enrollment_id, content_id):
        with database.transaction(self.db):
            query = self.db.query(model.Schedule)
            query = query.filter(model.Schedule.enrollment_id == enrollment_id)
            query = query.filter(model.Schedule.reference_id == content_id)
            query = query.filter(model.Schedule.workspace_id == self.client_id)
            schedule = query.first()
            if not schedule:
                raise NotFoundException("Schedule not found")

            self.db.expunge(schedule)
            sqlalchemy.orm.make_transient(schedule)
            schedule.id = None
            schedule.anticipated = False
            schedule.status = model.ScheduleStatus.pending
            schedule.send_date = datetime.datetime.utcnow()
            self.db.add(schedule)
            return schedule

    def load_messages_sent_count(self, filters):
        query = self.apply_filters(filters).filter(
            model.Schedule.workspace_id == self.client_id,
            model.Schedule.status == model.ScheduleStatus.delivered,
        )
        return query.count()

    def get_next_schedule(self, enrollment_id: str) -> model.Schedule:
        return (
            self.db.query(model.Schedule)
            .filter(
                model.Schedule.enrollment_id == enrollment_id,
                model.Schedule.status == model.ScheduleStatus.pending,
            )
            .order_by(model.Schedule.send_date.asc())
            .first()
        )

    def update_schedule_status(self, schedule: model.Schedule, dispatch_id: str, success: bool = True) -> None:
        if not dispatch_id:
            return

        schedule.message_id = dispatch_id
        schedule.status = model.ScheduleStatus.sent if success else model.ScheduleStatus.error
        self.db.commit()


class CourseReportService:
    def __init__(
        self,
        schedule_service,
        enrollment_service,
        kontent,
        i18n,
        min_certificate_performance,
    ):
        self.i18n = i18n
        self.kontent = kontent
        self.schedule_service = schedule_service
        self.enrollment_service = enrollment_service
        self.min_certificate_performance = min_certificate_performance

    @staticmethod
    def format_duration(duration):
        hours, remainder = divmod(duration, 3600)
        minutes, seconds = divmod(remainder, 60)
        if duration >= 3600:
            return f"{int(hours):02}h{int(minutes):02}m"
        elif duration >= 60:
            return f"{int(minutes):02}m"
        return f"{int(seconds):02}s"

    def _format_reached_duration(self, reached, reachable):
        f_reached = self.format_duration(reached)
        f_reachable = self.format_duration(reachable)
        return f"{f_reached}/{f_reachable}"

    @staticmethod
    def _format_reached_points(reached, reachable):
        return f"{reached:.1f}/{reachable:.1f}"

    @staticmethod
    def convert_percentage_int(percentage):
        percentage = min(percentage * 100.0, 100.0)
        return math.ceil(percentage)

    @staticmethod
    def format_percentage(percentage):
        percentage_int = CourseReportService.convert_percentage_int(percentage)
        return f"{percentage_int}%"

    @staticmethod
    def performance_type(percentage):
        percentage = math.trunc(percentage * 100.0 * 100000)
        if percentage >= 7000000:
            return 100
        if 5000000 <= percentage < 7000000:
            return 70
        if 0 < percentage < 5000000:
            return 50
        return 0

    def _load_enrollment_activities(self, course, enrollment):
        user_score = {}
        course_lessons = sorted(course.lessons, key=lambda _lesson: _lesson.order)
        for lesson in course_lessons:
            contents = []
            lesson_contents = list(
                filter(
                    lambda content: content.created < enrollment.created,
                    lesson.contents,
                )
            )
            lesson_contents = sorted(lesson_contents, key=lambda lc: lc.order)
            for c in lesson_contents:
                if c.type.name == "Question":
                    exam = self.kontent.load_exam(
                        c.learn_content, enrollment.id, course.workspace_owner_id
                    )
                    total_points = sum([a["points"] for a in exam["questions"]])
                    total_points_won = sum(
                        [5 for a in exam["user_answers"] if bool(a["is_ok"]) is True]
                    )
                    percentage = (
                        min(total_points_won / total_points, 1.0) if total_points else 1
                    )

                    content_data = {
                        "name": c.name,
                        "duration": 0,
                        "points": total_points_won,
                        "percentage": percentage,
                        "learn_duration": 0,
                        "learn_points": total_points,
                        "type": "QUIZ",
                    }
                    contents.append(content_data)
                else:
                    activities = self.schedule_service.load_content_activities_by_user(
                        enrollment.id, c.id
                    )
                    total_duration = sum([a.duration or 0 for a in activities])
                    learn_content = self.kontent.load_content(c.learn_content) or {}
                    learn_content_duration = learn_content.get(
                        "duration", total_duration
                    )
                    percentage = (
                        min(total_duration / learn_content_duration, 1.0)
                        if learn_content_duration > 0
                        else 1.0
                    )
                    points = learn_content.get("points", 0) * percentage
                    content_data = {
                        "name": c.name,
                        "duration": total_duration,
                        "points": points,
                        "percentage": percentage,
                        "learn_duration": learn_content.get("duration", 0),
                        "learn_points": learn_content.get("points", 0),
                        "type": "CONTENT",
                    }
                    contents.append(content_data)
            user_score[lesson.id] = {"name": lesson.name, "contents": contents}
        return user_score

    @staticmethod
    def _calculate_points(content_points, quiz_points, content_weight, quiz_weight):
        total_weight = content_weight + quiz_weight
        return (
            (content_points * content_weight) + (quiz_points * quiz_weight)
        ) / total_weight

    def build_performance(self, course, enrollment):
        """
        Build a dict with the  performance by course, lesson, content
        :param course:
        :param enrollment:
        :return dict:
        """
        user_score = self._load_enrollment_activities(course, enrollment)

        lessons = []
        quiz_points = 0
        content_points = 0
        learn_quiz_points = 0
        learn_content_points = 0
        course_duration = 0
        course_learn_duration = 0
        has_content = False
        has_quiz = False

        for lesson_id, lesson in user_score.items():
            for content in lesson["contents"]:
                course_duration += content["duration"]
                course_learn_duration += content["learn_duration"]
                if content["type"] == "QUIZ":
                    has_quiz = True
                    quiz_points += content["points"]
                    learn_quiz_points += content["learn_points"]
                else:
                    has_content = True
                    content_points += content["points"]
                    learn_content_points += content["learn_points"]

            lessons.append(
                {
                    "id": lesson_id,
                    "name": lesson["name"],
                    "contents": lesson["contents"],
                }
            )

        if has_content and has_quiz:
            quiz_weight = course.quiz_performance_weight
            content_weight = course.content_performance_weight
        elif has_content:
            content_weight = (
                course.content_performance_weight + course.quiz_performance_weight
            )
            quiz_weight = 0
        else:
            content_weight = 0
            quiz_weight = (
                course.quiz_performance_weight + course.content_performance_weight
            )

        quiz_performance = (
            (quiz_points / learn_quiz_points) * quiz_weight
            if learn_quiz_points
            else 0.0
        )
        content_performance = (
            (content_points / learn_content_points) * content_weight
            if learn_content_points
            else 0.0
        )

        course_percentage = (
            quiz_performance + content_performance
        ) / course.total_performance_weight()
        course_percentage = round_performance(course_percentage)
        course_points = self._calculate_points(
            content_points, quiz_points, content_weight, quiz_weight
        )
        course_learn_points = self._calculate_points(
            learn_content_points, learn_quiz_points, content_weight, quiz_weight
        )

        return {
            "id": course.id,
            "name": course.name,
            "points": course_points,
            "percentage": course_percentage,
            "duration": course_duration,
            "learn_points": course_learn_points,
            "learn_duration": course_learn_duration,
            "lessons": lessons,
        }

    def build_text_performance_report(self, course, enrollment):
        """
        Build a textual performance report
        :param course:
        :param enrollment:
        :return (str, int, int): Report, Performance, Points
        """
        self.i18n.set_locale(course.lang)
        course_p = self.build_performance(course, enrollment)

        report_lines = []
        for lesson in course_p["lessons"]:
            report_lines.append(
                textwrap.shorten(lesson["name"], width=20, placeholder="...")
            )
            for content in lesson["contents"]:
                content_name = textwrap.shorten(
                    content["name"], width=15, placeholder="..."
                )
                duration_formatted = self._format_reached_duration(
                    content["duration"], content["learn_duration"]
                )
                points_formatted = self._format_reached_points(
                    content["points"], content["learn_points"]
                )
                report_lines.append(f" + {content_name}")
                report_lines.append(
                    "   - {}: {}".format(self.i18n.t("Duration"), duration_formatted)
                )
                report_lines.append(
                    "   - {}: {}".format(self.i18n.t("Points"), points_formatted)
                )

            report_lines.append("")

        course_percentage = self.format_percentage(course_p["percentage"])
        course_duration_formatted = self._format_reached_duration(
            course_p["duration"], course_p["learn_duration"]
        )
        course_points_formatted = self._format_reached_points(
            course_p["points"], course_p["learn_points"]
        )
        report_lines.append(
            "{}: {}".format(self.i18n.t("Total Duration"), course_duration_formatted)
        )
        report_lines.append(
            "{}: {}".format(self.i18n.t("Total Points"), course_points_formatted)
        )
        report_lines.append(
            "{}: {}".format(self.i18n.t("Course Use"), course_percentage)
        )

        full_report = ""
        for line in report_lines:
            full_report = full_report + line + "\n"
        return full_report, course_p

    def enrollment_tracking_data(self, enrollment_id):
        enrollment = self.enrollment_service.load(enrollment_id)
        course = self.enrollment_service.load_course(enrollment)
        all_activities = enrollment.activities

        contents = []
        course_lessons = sorted(course.lessons, key=lambda _lesson: _lesson.order)
        for lesson in course_lessons:
            lesson_contents = sorted(lesson.contents, key=lambda lc: lc.order)
            for c in lesson_contents:
                schedule_status = self.schedule_service.load_reference_status(
                    enrollment.id, c.id
                )
                if c.type.name == "Question":
                    exam = self.kontent.load_exam(
                        c.learn_content, enrollment.id, course.workspace_owner_id
                    )
                    total_questions = len(exam["questions"])
                    total_answers = len(
                        [a for a in exam["user_answers"] if bool(a["is_ok"]) is True]
                    )
                    answers = sorted(
                        exam["user_answers"], key=lambda a: a.get("updated_date")
                    )
                    first_access = (
                        dateutil.parser.parse(answers[0].get("updated_date"))
                        if answers
                        else None
                    )
                    last_access = (
                        dateutil.parser.parse(answers[-1].get("updated_date"))
                        if answers
                        else None
                    )
                    content_data = {
                        "content": c,
                        "first_access": first_access,
                        "last_access": last_access,
                        "duration": 0,
                        "learn_duration": 0,
                        "total_questions": total_questions,
                        "total_correct_answers": total_answers,
                        "schedule_status": schedule_status,
                    }
                    contents.append(content_data)
                else:
                    activities = [a for a in all_activities if a.content_id == c.id]
                    activities = sorted(activities, key=lambda a: a.start_at)
                    first_access = activities[0].start_at if activities else None
                    last_access = activities[-1].start_at if activities else None
                    total_duration = sum([a.duration or 0 for a in activities])
                    learn_content = self.kontent.load_content(c.learn_content)
                    learn_content_duration = learn_content.get(
                        "duration", total_duration
                    )
                    content_data = {
                        "content": c,
                        "first_access": first_access,
                        "last_access": last_access,
                        "duration": total_duration,
                        "learn_duration": learn_content_duration,
                        "total_questions": 0,
                        "total_correct_answers": 0,
                        "schedule_status": schedule_status,
                    }
                    contents.append(content_data)
        return contents


class ScheduleAdminService:
    def __init__(
        self,
        schedule_service,
        course_report_service,
        enrollment_service,
        chat_service,
        content_url,
        jwt_secret,
        dispatcher,
        callback_url,
        link_shortener,
        certificate_service: CertificateService,
        private_channel_service: PrivateChannelService,
    ):
        self.enrollment_service = enrollment_service
        self.course_report_service = course_report_service
        self.schedule_service = schedule_service
        self.chat_service = chat_service
        self.callback_url = callback_url
        self.dispatcher = dispatcher
        self.content_url = content_url
        self.jwt_secret = jwt_secret
        self.link_shortener = link_shortener
        self.private_channel_service = private_channel_service
        self.certificate_service = certificate_service

    def create_token(self, sub, lang, client_id, token_expiration, enrollment_id):
        payload = {
            "aud": "smartzap",
            "sub": str(sub),
            "enrollment_id": str(enrollment_id),
            "exp": datetime.datetime.utcnow() + datetime.timedelta(days=token_expiration),
            "lang": lang,
            "x-client": str(client_id),
        }
        return jwt.encode(payload, self.jwt_secret, algorithm="HS256")

    def _build_callback_url(
        self, schedule, enrollment, lang, client_id, token_expiration
    ):
        if self.callback_url:
            token = self.create_token(
                enrollment.user_id, lang, client_id, token_expiration, enrollment.id
            )
            url_path = "/api/v1/schedule/{schedule_id}/callback/{broker}?token={token}"
            full_url = urljoin.url_path_join(self.callback_url, url_path)
            broker_name = self.dispatcher.broker_name()
            return full_url.format(
                schedule_id=schedule.id, token=token, broker=broker_name
            )
        return ""

    def dispatch(self, schedule_id):
        if not self.dispatcher:
            return

        dispatch_types = {
            model.ScheduleType.course_introduction: self._dispatch_course_intro,
            model.ScheduleType.lesson_content: self._dispatch_lesson_content,
            model.ScheduleType.course_end: self._dispatch_course_end,
        }
        schedule = None
        try:
            schedule = self.schedule_service.load(schedule_id)
            if schedule.status != model.ScheduleStatus.pending:
                logger.info(f"Schedule not pending: {schedule.id}")
                return
            dispatch_func = dispatch_types.get(schedule.type, None)
            if dispatch_func:
                enrollment, user = self.schedule_service.load_enrollment_and_user(
                    schedule.enrollment_id
                )
                dispatch_func(schedule, enrollment, user)
        except NotFoundException as e:
            logger.error(e.message)
            if schedule:
                self._post_dispatch(schedule, e.message, False)
            return

    def _post_dispatch(self, schedule, dispatch_id, success):
        if not dispatch_id and not success:
            return
        schedule.message_id = dispatch_id
        schedule.status = (
            model.ScheduleStatus.sent if success else model.ScheduleStatus.error
        )
        self.schedule_service.db.commit()
        self.chat_service.registry_schedule(schedule)
        if success:
            schedule_sent_signal.emit("sent", schedule=schedule)

    def _dispatch_course_intro(self, schedule, enrollment, user):
        course = self.schedule_service.load_model(model.Course, schedule.reference_id)
        workspace = enrollment.workspace
        schedule_callback = self._build_callback_url(
            schedule,
            enrollment,
            course.lang,
            course.workspace_owner_id,
            workspace.user_token_expiration,
        )
        d_id, ok = self.dispatcher.dispatch_course_introduction(
            schedule.lang,
            user.phone,
            course.name,
            user.name,
            workspace.name,
            course.holder_image,
            schedule_callback,
            workspace.private_channel,
        )
        self._post_dispatch(schedule, d_id, ok)

    def _dispatch_lesson_content(self, schedule, enrollment, user):
        content = self.schedule_service.load_model(model.Content, schedule.reference_id)
        workspace = enrollment.workspace
        workspace_id = content.lesson.course.workspace_owner_id
        token_expiration = enrollment.workspace.user_token_expiration
        schedule_callback = self._build_callback_url(
            schedule, enrollment, schedule.lang, workspace_id, token_expiration
        )
        image_url = content.type.image_url if content.type else ""
        content_link = self.generate_content_url(
            content, enrollment.user_id, enrollment.id, token_expiration, schedule.lang
        )
        self.enrollment_service.register_current_content(
            schedule.enrollment_id, content.id, content.lesson_id
        )
        description = content.description or content.name
        d_id, ok = self.dispatcher.dispatch_lesson_content(
            schedule.lang,
            user.phone,
            user.name,
            content.lesson.name,
            content.name,
            description,
            schedule.anticipated,
            content_link,
            image_url,
            schedule_callback,
            token_expiration,
            workspace.private_channel,
            enrollment.course.allow_content_anticipation,
            enrollment.course.allow_drop_out,
        )
        self._post_dispatch(schedule, d_id, ok)

    def _dispatch_course_end(self, schedule, enrollment, user):
        course = self.schedule_service.load_model(model.Course, schedule.reference_id)
        workspace = enrollment.workspace
        schedule_callback = self._build_callback_url(
            schedule,
            enrollment,
            course.lang,
            course.workspace_owner_id,
            enrollment.workspace.user_token_expiration,
        )
        course_p = self.course_report_service.build_performance(course, enrollment)

        local_date = self._local_date(schedule.send_date, schedule.timezone)
        self.enrollment_service.complete_course(
            schedule.enrollment_id,
            local_date,
            course_p["percentage"],
            course_p["points"],
        )

        percentage = self.course_report_service.format_percentage(
            course_p["percentage"]
        )
        duration = self.course_report_service.format_duration(course_p["duration"])
        learn_duration = self.course_report_service.format_duration(
            course_p["learn_duration"]
        )
        learn_duration = f"{learn_duration}"
        points = f'{course_p["points"]:.1f}'
        learn_points = f'{course_p["learn_points"]:.1f}'
        performance_type = self.course_report_service.performance_type(
            course_p["percentage"]
        )
        certificate_url = None
        if performance_type > 70:
            certificate_url = self.certificate_service.generate_certificate(enrollment, course)
        self.enrollment_service.update_certificate_url(
            schedule.enrollment_id, certificate_url
        )

        certificate_url = self._prepare_course_end_certificate_media(
            schedule.enrollment_id, certificate_url
        )
        d_id, ok = self.dispatcher.dispatch_end_course(
            schedule.lang,
            user.phone,
            course.name,
            user.name,
            percentage,
            duration,
            learn_duration,
            points,
            learn_points,
            performance_type,
            schedule_callback,
            certificate_url,
            workspace.private_channel,
            course.disable_send_certificate
        )
        self._post_dispatch(schedule, d_id, ok)

    def _prepare_course_end_certificate_media(self, enrollment_id, certificate_url):
        chat = self.chat_service.load_last(enrollment_id)
        if chat and chat.is_in_24_window():
            return certificate_url
        return ""

    @staticmethod
    def _local_date(date, tmz):
        local_timezone = tz.gettz(tmz)
        utc_timezone = tz.gettz("UTC")
        local_date = date.replace(tzinfo=utc_timezone)
        return local_date.astimezone(local_timezone).date()

    def generate_content_url(
        self, content, user_id, enrollment_id, token_expiration, schedule_lang=""
    ):
        workspace_id = content.lesson.course.workspace_owner_id
        lang = schedule_lang or content.lesson.course.lang
        phone = self._load_workspace_phone(content, lang)
        user_token = self.create_token(
            user_id, lang, workspace_id, token_expiration, enrollment_id
        )
        params = {
            "token": user_token,
            "lang": lang,
            "client_id": workspace_id,
            "phone": phone,
        }
        parameters = urllib.parse.urlencode(params)
        long_link = f"{self.content_url}/{content.id}?{parameters}"
        image_url = content.type.image_url if content.type else ""
        return self.link_shortener.short_link(
            long_link, content.lesson.name, content.name, image_url
        )

    def _load_workspace_phone(self, content: model.Content, lang: str) -> Optional[str]:
        phone = None
        private_channel = content.lesson.course.workspace_owner.private_channel
        if private_channel:
            _, _, phone = self.private_channel_service.load_credentials(
                model.LanguageChannels(private_channel), lang
            )
        return phone


class MessageDispatcher:
    def __init__(self, broker: WhatsAppTwilioClient, templates_folder, end_course_image_url):
        self.end_course_image_url = end_course_image_url
        self.templates_folder = templates_folder
        self.broker: WhatsAppTwilioClient = broker

    @staticmethod
    def _short_language(lang):
        tokens = lang.split("-")
        return tokens[0] if tokens else ""

    def _load_lang_path(self, lang):
        if not lang:
            return None
        folder = self.templates_folder.rstrip("/")
        folders = glob.glob(f"{folder}/*/")
        lang_lower = lang.lower()
        short_lang_lower = self._short_language(lang_lower)
        for f in folders:
            base_folder = os.path.basename(os.path.dirname(f))
            f_lower = base_folder.lower()
            if lang_lower in f_lower:
                return f
            if short_lang_lower in f_lower:
                return f
        return None

    def _load_message(self, message_type, lang):
        logger.info(f"{message_type} -- {lang}")
        lang_folder = self._load_lang_path(lang)
        logger.info(lang_folder)
        if not lang_folder:
            return None

        folder = lang_folder.rstrip("/")
        templates = glob.glob(f"{folder}/*.txt")
        for t in templates:
            filename, _ = os.path.splitext(os.path.basename(t))
            if message_type == filename.lower():
                with open(t) as filein:
                    return filein.read()
        return None

    def build_message(self, message_type, lang, data):
        message = self._load_message(message_type, lang)
        if not message:
            return None
        message = string.Template(message)
        return message.substitute(data)

    def broker_name(self):
        return self.broker.name()

    def dispatch_course_introduction(self, lang, phone, course_name, user_name, workspace_name, _,
                                     callback_url, private_channel):
        data = {'1': user_name, '2': workspace_name, '3': course_name}
        template_name = 'course_introduction'
        return self._send_message(phone, template_name, data, callback_url, lang, private_channel)

    def dispatch_lesson_content(
        self,
        lang,
        phone,
        user_name,
        lesson_name,
        content_name,
        content_description,
        anticipated,
        content_link,
        _,
        callback_url,
        content_days_expiration,
        private_channel,
        allow_anticipation: bool,
        allow_drop_out: bool,
    ):
        data = {
            "1": user_name,
            "2": content_name,
            "3": content_description or "",
            "4": content_link,
            "5": str(content_days_expiration),
        }
        message_name = self._get_lesson_content_message_name(
            allow_anticipation, anticipated, allow_drop_out
        )

        return self._send_message(phone, message_name, data, callback_url, lang, private_channel)

    @staticmethod
    def _get_lesson_content_message_name(
        allow_anticipation: bool, anticipated: bool, allow_drop_out: bool
    ) -> str:
        if not allow_anticipation:
            if allow_drop_out:
                return LESSON_CONTENT_WITHOUT_CONTENT_ANTICIPATION_WITH_GIVE_UP_OPTION
            return LESSON_CONTENT_WITHOUT_CONTENT_ANTICIPATION
        if allow_drop_out:
            return LESSON_CONTENT_ANTICIPATED_WITH_GIVE_UP_OPTION

        return "lesson_content_anticipated" if anticipated else "lesson_content"

    def dispatch_end_course(
        self,
        lang,
        phone,
        course_name,
        user_name,
        percentage,
        duration,
        learn_duration,
        points,
        learn_points,
        performance_type,
        callback_url,
        certificate_url,
        private_channel,
        disable_send_certificate=False,
    ):
        data_mappers_by_performance_type = {
            100: {
                "1": user_name,
                "2": course_name,
                "3": str(duration),
                "4": str(learn_duration),
                "5": str(points),
                "6": str(learn_points),
                "7": str(percentage),
            },
            50: {
                "1": user_name,
                "2": course_name,
                "3": str(percentage),
            },
            0: {
                "1": user_name,
                "2": course_name
            }
        }

        if performance_type not in [0, 50, 70, 100]:
            performance_type = 100

        if certificate_url and not disable_send_certificate:
            # Twilio is not supporting PDF + Description. Sending two messages
            m_id, ok = self._send_media(phone, certificate_url, lang, private_channel)
            if not ok:
                return m_id, ok

        message_type = f"end_course_{performance_type}"
        if not certificate_url:
            # Template for certificate available. Necessary because we can only send files between the 24h window.
            message_type = f"end_course_certificate_{performance_type}"

        data = data_mappers_by_performance_type.get(performance_type, data_mappers_by_performance_type[100])

        return self._send_message(phone, message_type, data, callback_url, lang, private_channel)

    def _send_message(
        self,
        phone,
        template_key: str,
        template_variables: Dict,
        callback_url,
        lang,
        private_channel_data,
        media_url=None
    ):
        if not template_key:
            return None
        private_channel: model.LanguageChannels = (
            model.LanguageChannels(private_channel_data)
            if private_channel_data
            else None
        )
        return self.broker.send_message(
            phone,
            template_key,
            template_variables,
            callback_url,
            lang,
            private_channel,
            media_url
        )

    def _send_media(self, phone, media_url, lang, private_channel_data):
        private_channel = (
            model.LanguageChannels(private_channel_data)
            if private_channel_data
            else None
        )
        return self.broker.send_media(phone, media_url, lang, private_channel)


class MessageChatterBot:
    def __init__(
        self,
        enrollment_service,
        course_service,
        schedule_service,
        chat_service,
        broker,
        templates_folder,
        message_dispatcher,
        tasks_client,
        answers,
    ):
        self.course_service = course_service
        self.message_dispatcher = message_dispatcher
        self.enrollment_service = enrollment_service
        self.schedule_service = schedule_service
        self.chat_service = chat_service
        self.templates_folder = templates_folder
        self.broker = broker
        self.tasks_client = tasks_client
        self.answers = answers

    @staticmethod
    def _define_chat_type(message_type):
        chats = {
            "course_welcome": model.ChatType.course_welcome,
            "answer_not_found": model.ChatType.answer_not_found,
            "enrollment_rejected": model.ChatType.enrollment_rejected,
            "enrollment_not_found": model.ChatType.enrollment_not_found,
            "enrollment_started": model.ChatType.enrollment_started,
            "invalid_enrollment_answer": model.ChatType.enrollment_wrong_answer,
            "lesson_content_anticipation_done": model.ChatType.content_anticipated,
            "lesson_content_nothing_pending": model.ChatType.content_nothing_pending,
            "lesson_content_nothing_to_do": model.ChatType.content_nothing_to_do,
            "invalid_next_content_answer": model.ChatType.content_wrong_answer,
            "finish_course_anticipation_done": model.ChatType.finish_course_anticipated,
            "finish_course_content_pending": model.ChatType.finish_course_content_pending,
            "finish_course_nothing_to_do": model.ChatType.finish_course_nothing_to_do,
            "invalid_finish_course_answer": model.ChatType.finish_course_wrong_answer,
            "certificate_not_available": model.ChatType.certificate_not_available,
            "certificate": model.ChatType.certificate,
        }
        return chats.get(message_type, "")

    def _build_response_message(
        self, enrollment, message_type, validate_duplicity=False
    ):
        if not message_type:
            return ""

        if validate_duplicity:
            chat = self.chat_service.load_last(enrollment.id)
            if chat and not chat.can_reply_message(
                self._define_chat_type(message_type)
            ):
                return ""

        course, lang = self.course_service.load_course_and_lang(enrollment.course_id)
        if course and lang:
            data = {
                "course_name": course.name,
                "course_description": course.description,
                "content_days_expiration": enrollment.workspace.user_token_expiration,
            }
            return self.message_dispatcher.build_message(message_type, lang, data)
        return ""

    def _enrollment_not_found_message(self, lang):
        lang = lang or "pt-BR"
        message_type = "enrollment_not_found"
        return self.message_dispatcher.build_message(message_type, lang, {})

    def _enrollment_started(self, enrollment, user_answer):
        message_type = "enrollment_started"

        _, lang = self.course_service.load_course_and_lang(enrollment.course_id)
        workspace = enrollment.workspace
        user = enrollment.user
        self.tasks_client.send_message(
            user.phone, message_type, {}, None, lang, workspace.private_channel
        )

        self._save_chat(enrollment, message_type, None, user_answer)
        return ""

    def _answer_not_recognized(self, enrollment, user_answer):
        message_type = "answer_not_recognized"
        message = self._build_response_message(enrollment, message_type)
        self._save_chat(enrollment, message_type, message, user_answer)
        return message

    def _save_chat(self, enrollment, message_type, message, user_answer):
        replied = bool(message)
        chat_type = self._define_chat_type(message_type)
        self.chat_service.registry_chat(enrollment.id, chat_type, replied, user_answer)

    def _accept_message(self, enrollment, user_answer):
        message_type = self._get_welcome_message_type(enrollment)
        enrollment = self.enrollment_service.accept_disclaimer(enrollment)
        message_data = {
            "1": enrollment.course.description,
            "2": str(enrollment.workspace.user_token_expiration),
        }

        if message_type:
            _, lang = self.course_service.load_course_and_lang(enrollment.course_id)
            workspace = enrollment.workspace
            user = enrollment.user
            self.tasks_client.send_message(
                user.phone, message_type, message_data, None, lang, workspace.private_channel
            )

        self._save_chat(enrollment, message_type, message_data, user_answer)
        return ""

    @staticmethod
    def _get_welcome_message_type(enrollment: model.Enrollment) -> str:
        if not enrollment.course.allow_content_anticipation:
            return COURSE_WELCOME_WITHOUT_CONTENT_ANTICIPATION
        return COURSE_WELCOME

    def _reject_message(self, enrollment, user_answer):
        message_type = "enrollment_rejected"
        enrollment = self.enrollment_service.reject_disclaimer(enrollment)
        message = self._build_response_message(enrollment, message_type)
        self._save_chat(enrollment, message_type, message, user_answer)
        return message

    def _next_content_message(self, enrollment, user_answer):
        status, schedule_id = self.schedule_service.anticipate_next_content(
            enrollment.id
        )
        message_type = ""
        if status == "anticipated":
            message_type = "lesson_content_anticipation_done"
        elif status == "nothing":
            message_type = "lesson_content_nothing_to_do"

        if status == "waiting_end":
            message_type = "lesson_content_nothing_pending"
            _, lang = self.course_service.load_course_and_lang(enrollment.course_id)
            workspace = enrollment.workspace
            self.tasks_client.send_message(
                enrollment.user.phone, message_type, {}, None, lang, workspace.private_channel
            )
            self._save_chat(enrollment, message_type, None, user_answer)
            return ""

        message = self._build_response_message(enrollment, message_type)
        self._save_chat(enrollment, message_type, message, user_answer)
        if schedule_id:
            self.tasks_client.dispatch_schedule(schedule_id)
        return message

    def _finish_course_message(self, enrollment, user_answer):
        status, schedule_id = self.schedule_service.anticipate_course_end(enrollment.id)
        message_type = ""
        if status == "anticipated":
            message_type = "finish_course_anticipation_done"
        elif status == "content_pending":
            message_type = "finish_course_content_pending"
        elif status == "nothing":
            message_type = "finish_course_nothing_to_do"

        message = self._build_response_message(enrollment, message_type)
        self._save_chat(enrollment, message_type, message, user_answer)
        if schedule_id:
            self.tasks_client.dispatch_schedule(schedule_id)
        return message

    def _course_certificate(self, enrollment, user_answer):
        message_type = "certificate"
        certificate_url = enrollment.certificate_url
        message = self._build_response_message(enrollment, message_type, True)
        self._save_chat(enrollment, message_type, message, user_answer)
        return message, certificate_url

    def answer_type(self, answer):
        answer = answer.lower().strip()
        accept = answer in self.answers.get("accept", [])
        reject = answer in self.answers.get("reject", [])
        next_content = answer in self.answers.get("content", [])
        finish_course = answer in self.answers.get("finish", [])
        certificate = answer in self.answers.get("certificate", [])
        drop_out = answer in self.answers.get("drop_out", [])
        if accept and not reject:
            return model.ChatAnswerType.accept
        elif reject and not accept:
            return model.ChatAnswerType.reject
        elif next_content:
            return model.ChatAnswerType.content
        elif finish_course:
            return model.ChatAnswerType.finish
        elif certificate:
            return model.ChatAnswerType.certificate
        elif drop_out:
            return model.ChatAnswerType.drop_out
        else:
            return None

    def process_message(self, phone, answer_type, user_answer, bot_lang=""):
        if answer_type == model.ChatAnswerType.certificate:
            enrollment = (
                self.enrollment_service.load_last_enrollment_completed_from_phone(phone)
            )
            return self.course_certificate(enrollment, user_answer)

        enrollment = self.enrollment_service.load_first_active_from_phone(phone)
        if not enrollment:
            return "", ""

        if answer_type == model.ChatAnswerType.accept:
            return self.accept_disclaimer(enrollment, user_answer)

        if answer_type == model.ChatAnswerType.reject:
            return self.reject_disclaimer(enrollment, user_answer)

        if answer_type == model.ChatAnswerType.content:
            return self.next_content(enrollment, user_answer)

        if answer_type == model.ChatAnswerType.finish:
            return self.finish_course(enrollment, user_answer)

        if answer_type == model.ChatAnswerType.drop_out:
            return self.drop_out(enrollment, user_answer)

        return "", ""

    def accept_disclaimer(self, enrollment, user_answer):
        if enrollment.status == model.EnrollmentStatus.waiting:
            return self._accept_message(enrollment, user_answer), ""
        else:
            return self._enrollment_started(enrollment, user_answer), ""

    def reject_disclaimer(self, enrollment, user_answer):
        if enrollment.status == model.EnrollmentStatus.waiting:
            return self._reject_message(enrollment, user_answer), ""
        else:
            return self._enrollment_started(enrollment, user_answer), ""

    def next_content(self, enrollment, user_answer):
        return self._next_content_message(enrollment, user_answer), ""

    def finish_course(self, enrollment, user_answer):
        return self._finish_course_message(enrollment, user_answer), ""

    def drop_out(self, enrollment, user_answer):
        self.schedule_service.give_up_enrollment(enrollment.id)
        message_type = "give_up_sucess"
        message = self._build_response_message(enrollment, message_type)
        return message, ""

    def course_certificate(self, enrollment, user_answer):
        if enrollment and enrollment.status == model.EnrollmentStatus.completed and enrollment.certificate_url:
            return self._course_certificate(enrollment, user_answer)
        return '', ''


class UserXlsImporter:
    def __init__(self, user_service):
        self.user_service = user_service

    @staticmethod
    def _load_xls(filename):
        users = []
        users_error = []
        xls = xlrd.open_workbook(filename)
        plan = xls.sheets()[0]
        for i in range(1, plan.nrows):
            columns = plan.row_values(i)
            try:
                if len(columns) >= 4:
                    user = {}
                    if isinstance(columns[1], float):
                        user["phone"] = re.sub(r"\D", "", str(int(columns[1])))
                    else:
                        user["phone"] = re.sub(r"\D", "", str((columns[1])))

                    user["name"] = columns[0]
                    user["email"] = columns[2].lower()
                    user["tags"] = columns[3]

                    if len(columns) >= 5:
                        user["myacc"] = bool(columns[4])
                    users.append(user)

            except Exception as e:
                logger.error(e)
                user["error"] = [
                    "XLS parse error. Check all fields",
                ]
                users_error.append(user)

        return users, users_error

    @staticmethod
    def _validate_user_data(data):
        if data.get("name") and data.get("phone"):
            return True, ""
        errors = []
        if not data.get("name"):
            errors.append("Name is required")
        if not data.get("phone"):
            errors.append("Phone is required")
        return False, errors

    @staticmethod
    def _import_user_data(data):
        user = model.User()
        user.name = data.get("name", "")
        user.email = data.get("email", "")
        user.phone = data.get("phone", "")
        user.tags = data.get("tags", "")
        return user

    def import_file(self, filename):
        users_data, _ = self._load_xls(filename)
        users_ok = []
        users_not_ok = []
        for data in users_data:
            ok, error = self._validate_user_data(data)
            if ok:
                user = self._import_user_data(data)
                try:
                    user = self.user_service.add(user)
                    users_ok.append(user)
                except Exception as e:
                    data["error"] = [str(e)]
                    users_not_ok.append(data)
            else:
                data["error"] = error
                users_not_ok.append(data)
        return users_ok, users_not_ok


class CourseProcessor:
    def __init__(self, db, kontent, webhook_logger: DiscordWebhookLogger):
        self.db = db
        self.kontent = kontent
        self.slack = webhook_logger

    def execute(self):
        courses_processed = []
        courses = (
            self.db.query(model.Course)
            .filter(model.Course.status == model.CourseStatus.processing)
            .all()
        )
        with database.transaction(self.db):
            for course in courses:
                logger.info(f"Analyzing Course: {course.id}")
                points = 0
                duration = 0
                all_analyzed = False
                all_contents = [
                    content for lesson in course.lessons for content in lesson.contents
                ]

                if len(all_contents) == 0:
                    logger.info(f"Course if no content: {course.id}")
                    course.status = model.CourseStatus.creating
                    self.db.flush()
                    continue

                for content in all_contents:
                    all_analyzed, duration, points = self._analyze_content(
                        content, course, duration, points
                    )
                    if not all_analyzed:
                        break

                if all_analyzed:
                    logger.info(f"Course analyzed: {course.id}")
                    course.points = points
                    course.duration = duration
                    course.status = model.CourseStatus.reviewing
                    self.db.flush()
                    courses_processed.append(course)

            return courses_processed

    def _analyze_content(self, content, course, duration, points):
        all_analyzed = True
        try:
            if content.type.name == "Question":
                exam = self.kontent.load_exam(
                    content.learn_content, None, course.workspace_owner_id
                )
                points += exam.get("points") if exam.get("points") else 0
                duration += exam.get("duration") if exam.get("duration") else 0
            else:
                learn_content = self.kontent.load_content(content.learn_content)

                if learn_content.get("analyzed"):
                    points += learn_content.get("points", 0)
                    duration += learn_content.get("duration", 0)
                else:
                    all_analyzed = False

        except Exception as error:
            self.slack.emit_short_message(
                "Smartzap Course Analyzing (service.py)", error
            )
        return all_analyzed, duration, points


class StatsService:
    def __init__(
        self,
        user_service,
        course_service,
        content_service,
        kontent_db,
        s3,
        dict_gen,
        quiz_page=100,
    ):
        self.user_service = user_service
        self.course_service = course_service
        self.content_service = content_service
        self.kontent_db = kontent_db
        self.s3 = s3
        self.dict_gen = dict_gen
        self.per_page = quiz_page

    def _csv_generator(self, fields, dict_loader_func):
        with tempfile.NamedTemporaryFile(mode="w+") as csv_file:
            writer = csv.DictWriter(
                csv_file, fields, extrasaction="ignore", dialect="excel"
            )
            writer.writeheader()

            page = 1
            processing = True
            while processing:
                data_list = dict_loader_func(page)
                if data_list:
                    writer.writerows(data_list)
                processing = bool(data_list)
                page += 1
            csv_file.seek(0)
            s3_data = self.s3.send_csv_file(csv_file.name)
            return s3_data["url"]

    def generate_users_csv(self, filters):
        def load(page):
            filters["pag_spec"] = {"page": page, "per_page": 20}
            users, _ = self.user_service.load_filters(filters)
            data_list = []
            for u in users:
                user_data = self.dict_gen.user_to_dict(u)
                data_list.append(user_data)
            return data_list

        fields = [
            "id",
            "name",
            "phone",
            "email",
            "tags",
            "my_account_user",
            "created",
            "updated",
        ]
        url = self._csv_generator(fields, load)
        return url

    def generate_completed_enrollments_csv(self, course_id, filters):
        filter_spec = [f for f in filters["filter_spec"] if f["field"] != "status"]
        filter_spec.append(
            {"field": "status", "op": "==", "value": model.EnrollmentStatus.completed}
        )
        filters["filter_spec"] = filter_spec

        def load(page):
            filters["pag_spec"] = {"page": page, "per_page": self.per_page}
            enrollments, _ = self.course_service.load_all_enrollments(
                course_id, filters
            )
            data_list = []
            for e in enrollments:
                enrollment_data = self.dict_gen.enrollment_to_dict(e)
                data_list.append(enrollment_data)
            return data_list

        fields = [
            "id",
            "name",
            "phone",
            "email",
            "tags",
            "started_at",
            "finished_at",
            "performance",
        ]
        url = self._csv_generator(fields, load)
        return url

    def generate_in_progress_enrollments_csv(self, course_id, filters):
        filter_spec = [f for f in filters["filter_spec"] if f["field"] != "status"]
        filter_spec.append(
            {"field": "status", "op": "!=", "value": model.EnrollmentStatus.completed}
        )
        filters["filter_spec"] = filter_spec

        def load(page):
            filters["pag_spec"] = {"page": page, "per_page": self.per_page}
            enrollments, _ = self.course_service.load_all_enrollments(
                course_id, filters
            )
            data_list = []
            for e in enrollments:
                enrollment_data = self.dict_gen.enrollment_to_dict(e)
                data_list.append(enrollment_data)
            return data_list

        fields = [
            "id",
            "name",
            "phone",
            "email",
            "tags",
            "current_lesson",
            "current_content",
            "status",
        ]
        url = self._csv_generator(fields, load)
        return url

    def generate_activities_csv(self, course_id, filters):
        fields = [
            "id",
            "name",
            "phone",
            "email",
            "tags",
            "lesson",
            "content",
            "action",
            "start_at",
            "stop_at",
            "duration",
        ]
        users_cache = {}
        course = self.course_service.load(course_id)

        with tempfile.NamedTemporaryFile(mode="w+") as csv_file:
            writer = csv.DictWriter(
                csv_file, fields, extrasaction="ignore", dialect="excel"
            )
            writer.writeheader()

            for lesson in course.lessons:
                for content in lesson.contents:
                    page = 1
                    processing = True
                    while processing:
                        filters["pag_spec"] = {"page": page, "per_page": self.per_page}
                        data_list = self._load_content_activities(
                            content.id, filters, users_cache, lesson.name, content.name
                        )
                        if data_list:
                            writer.writerows(data_list)
                        processing = bool(data_list)
                        page += 1
            csv_file.seek(0)
            s3_data = self.s3.send_csv_file(csv_file.name)
            return s3_data["url"]

    def _load_content_activities(
        self, content_id, filters, users_cache, lesson_name, content_name
    ):
        activities, _ = self.content_service.load_all_activities(content_id, filters)
        activities_data = []
        for activity in activities:
            user = users_cache.get(activity.user_id, None)
            if not user:
                try:
                    user = self.user_service.load(activity.user_id)
                # when user is removed from workspace but have activities
                except Exception as e:
                    logger.error(e)
                users_cache[activity.user_id] = user
            user_data = self.dict_gen.user_to_dict(user)
            activity_data = self.dict_gen.activity_to_dict(activity)
            activities_data.append(
                {
                    "lesson": lesson_name,
                    "content": content_name,
                    **activity_data,
                    **user_data,
                }
            )
        return activities_data

    def _load_all_options(self, exam_id):
        query = (
            "SELECT exam.id AS e_id, exam.title AS e_title, question.id AS q_id, question.title AS q_title, "
            "question_option.id AS o_id, question_option.option AS o_title FROM exam "
            "JOIN exam_question on exam_question.exam_id = exam.id "
            "JOIN question on exam_question.question_id = question.id "
            "JOIN question_option on question_option.question_id=question.id "
            "WHERE exam.id = '{exam_id}'"
        )
        exam = self.kontent_db.execute(query.format(exam_id=exam_id)).fetchall()
        if not exam:
            return {}

        options = {}
        for o in exam:
            option_id = str(o["o_id"])
            data = {
                "exam_id": str(o["e_id"]),
                "question_id": str(o["q_id"]),
                "option_id": str(o["o_id"]),
                "exam_title": o["e_title"],
                "question_title": o["q_title"],
                "option_title": o["o_title"],
            }
            options[option_id] = data
        return options

    def _load_answers(self, exam_id, offset):
        query = (
            "SELECT exam.id AS e_id, exam.title AS e_title, question.title AS q_title, "
            "question_option.id AS co_id, answer.options AS uo_id, answer.is_ok AS is_ok, "
            "answer.user_id AS user_id, answer.enrollment_id AS enrollment_id FROM exam "
            "JOIN exam_question on exam_question.exam_id=exam.id "
            "JOIN question on exam_question.question_id=question.id "
            "JOIN question_option on question_option.question_id=question.id "
            "JOIN answer on answer.question_id=question.id "
            "WHERE question_option.correct_answer is True "
            "AND exam.id = '{exam_id}' "
            "ORDER BY user_id "
            "LIMIT {limit} OFFSET {offset} "
        )
        result = self.kontent_db.execute(
            query.format(exam_id=exam_id, limit=self.per_page, offset=offset)
        ).fetchall()
        answers = []
        for a in result:
            data = {
                "exam_id": str(a["e_id"]),
                "correct_answer_id": str(a["co_id"]),
                "user_answer_id": str(a["uo_id"]),
                "user_id": str(a["user_id"]),
                "exam_title": a["e_title"],
                "question_title": a["q_title"],
                "hit": a["is_ok"],
                "enrollment_id": str(a["enrollment_id"]),
            }
            answers.append(data)
        return answers

    def _build_user_answers(self, answers, all_options):
        if not answers:
            return []

        users_id = set()
        for answer in answers:
            users_id.add(answer["user_id"])

        users = self.user_service.load_unprotected_in(users_id)
        users_cache = {}
        for u in users:
            users_cache[u.id] = u

        answers_list = []
        for answer in answers:
            user = users_cache.get(answer["user_id"], None)
            if not user:
                continue

            correct_answer = all_options.get(answer["correct_answer_id"]).get(
                "option_title", ""
            )
            user_answer = all_options.get(answer["user_answer_id"]).get(
                "option_title", ""
            )

            user_data = self.dict_gen.user_to_dict(user)
            ua = {
                **user_data,
                "enrollment_id": answer["enrollment_id"],
                "quiz_name": answer["exam_title"],
                "question_title": answer["question_title"],
                "correct_answer": correct_answer,
                "user_answer": user_answer,
                "hit": answer["hit"],
            }
            answers_list.append(ua)
        return answers_list

    def generate_quizzes_csv(self, course_id):
        course = self.course_service.load(course_id)
        with tempfile.NamedTemporaryFile(mode="w+") as csv_file:
            self.generate_quizzes_csv_to_file(course, csv_file)
            csv_file.seek(0)
            s3_data = self.s3.send_csv_file(csv_file.name)
            return s3_data["url"]

    def generate_quizzes_csv_to_file(self, course, csv_file):
        fields = [
            "id",
            "enrollment_id",
            "name",
            "phone",
            "email",
            "tags",
            "quiz_name",
            "question_title",
            "correct_answer",
            "user_answer",
            "hit",
        ]
        content_questions = [
            c
            for lesson in course.lessons
            for c in lesson.contents
            if c.type.name == "Question"
        ]

        writer = csv.DictWriter(
            csv_file, fields, extrasaction="ignore", dialect="excel"
        )
        writer.writeheader()

        for question in content_questions:
            exam_id = question.learn_content
            all_options = self._load_all_options(exam_id)
            if all_options:
                offset = 0
                processing = True
                while processing:
                    answers = self._load_answers(exam_id, offset)
                    data_list = self._build_user_answers(answers, all_options)
                    if data_list:
                        writer.writerows(data_list)
                    processing = bool(answers)
                    offset += self.per_page


class ChatService(AbstractDatabaseService):
    def __init__(self, db):
        super().__init__(db, model.Chat)

    def registry_schedule(self, schedule):
        chat = model.Chat()
        chat.enrollment_id = schedule.enrollment_id
        chat.type = schedule.type
        if schedule.type == model.ScheduleType.lesson_content:
            chat.content_id = schedule.reference_id
        self.add(chat)

    def registry_chat(self, enrollment_id, chat_type, replied=False, user_answer=None):
        chat = model.Chat()
        chat.enrollment_id = enrollment_id
        chat.type = chat_type
        chat.replied = replied
        chat.user_answer = user_answer
        self.add(chat)

    def load_last(self, enrollment_id):
        chat = (
            self.db.query(model.Chat)
            .filter(model.Chat.enrollment_id == enrollment_id)
            .order_by(model.Chat.created.desc())
            .first()
        )
        return chat


class NotificationService(AbstractDatabaseService):
    def __init__(self, db, client_id, user_id):
        super().__init__(db, model.Notification)
        self.client_id = client_id
        self.user_id = user_id

    def override_user(self, client_id, user_id):
        self.client_id = client_id
        self.user_id = user_id

    def load(self, notification_id):
        notification = (
            self.db.query(model.Notification)
            .filter(model.Notification.id == notification_id)
            .filter(model.Notification.workspace_id == self.client_id)
            .filter(model.Notification.user_id == self.user_id)
            .first()
        )
        if not notification:
            raise NotFoundException(f"{model.Notification.__name__} not found")
        return notification

    def load_filters(self, filters):
        query = (
            self.db.query(model.Notification)
            .filter(model.Notification.workspace_id == self.client_id)
            .filter(model.Notification.user_id == self.user_id)
            .order_by(model.Notification.created.desc())
        )
        filter_spec = [
            f
            for f in filters["filter_spec"]
            if f["field"] != "user_id" and f["field"] != "workspace_id"
        ]
        filters["filter_spec"] = filter_spec
        return FilterResolver(model.Notification).exec_query_filters(query, filters)

    def _load_notification_type(self, n_type, n_action):
        query = self.db.query(model.NotificationType)
        query = query.filter(model.NotificationType.type.ilike(f"%{n_type}%"))
        if n_action:
            query = query.filter(model.NotificationType.action.ilike(f"%{n_action}%"))
        return query.first()

    def _build_notification(self, type_name, action_name):
        notification_type = self._load_notification_type(type_name, action_name)
        if not notification_type:
            return None
        notification = model.Notification()
        notification.user_id = self.user_id
        notification.workspace_id = self.client_id
        notification.type_id = notification_type.id
        return notification

    def notify_report(self, message, url):
        self._notify("Report", None, message, url)

    def notify_error(self, message, details):
        self._notify("Error", None, message, details)

    def notify_course(self, message, course_id):
        self._notify("Course", "Redirect", message, course_id)

    def notify_course_download(self, message, url):
        self._notify("Course", "Download", message, url)

    def _notify(self, type_name, action_name, message, content):
        notification = self._build_notification(type_name, action_name)
        if not notification:
            return
        notification.message = message
        notification.content = content
        self.add(notification)

    def read_all_notifications(self):
        read = False
        self.db.query(model.Notification).filter(
            model.Notification.workspace_id == self.client_id
        ).filter(model.Notification.user_id == self.user_id).filter(
            model.Notification.read == read
        ).update(
            {"read": True}
        )
        self.db.commit()


class CourseBackupService:
    def __init__(
        self, course_service, notification_service, kontent, stats_service, s3
    ):
        self.course_service = course_service
        self.notification_service = notification_service
        self.kontent = kontent
        self.stats_service = stats_service
        self.s3 = s3

    def backup_and_delete(self, course_id):
        course = self.course_service.load(course_id)
        if course.status != model.CourseStatus.deleting:
            return None
        file_url = self.backup_course(course)
        self.delete_course(course, file_url)
        return file_url

    def backup_course(self, course):
        directory, files_directory = self.build_temp_dirs()

        contents_files_urls = self.download_files_and_urls(course, files_directory)
        quizzes_filename = self.save_quizzes(course, files_directory)
        self.save_structure(course, directory, contents_files_urls, quizzes_filename)
        file_url = self.upload_compacted_file(directory)

        self.clean_temp_dir(directory)
        return file_url

    @staticmethod
    def build_temp_dirs():
        directory = tempfile.mkdtemp(prefix="smartzap_")
        files_directory = os.path.join(directory, "files")
        os.mkdir(files_directory)
        return directory, files_directory

    def download_files_and_urls(self, course, directory):
        files_urls = {}
        for lesson_index, lesson in enumerate(course.lessons, start=1):
            for content_index, content in enumerate(lesson.contents, start=1):
                if content.type.name != "Question":
                    data = self.kontent.load_content(content.learn_content)
                    if data:
                        url = data.get("url", "")
                        if self.is_internal_content(url):
                            prefix = f"{lesson_index:03d}_{content_index:03d}"
                            filename = self.download_file(
                                content.name, directory, prefix, data.get("url", "")
                            )
                            files_urls[content.id] = {"filename": filename}
                        elif url:
                            files_urls[content.id] = {"url": url}
        return files_urls

    @staticmethod
    def is_internal_content(url):
        if not url:
            return False

        domain = urlparse(url).netloc
        internal_domains = ["s3.amazonaws.com", "keepsdev.com"]
        return any(domain.find(d) != -1 for d in internal_domains)

    def download_file(self, content_name, directory, prefix, url):
        download_name = self.define_file_shortname(content_name, url)
        download_name = f"{prefix}__{download_name}"
        local_filename = os.path.join(directory, download_name)
        with requests.get(url, stream=True) as r, open(local_filename, "wb") as f:
            shutil.copyfileobj(r.raw, f)
        return local_filename

    @staticmethod
    def define_file_shortname(content_name, url):
        if not content_name:
            return url.split("/")[-1]

        file_extension = url.split(".")[-1]
        shortname = re.sub(r"[^a-zA-Z0-9 ]", "", content_name)
        shortname = shortname.replace(" ", "_")
        return f"{shortname}.{file_extension}"

    def save_quizzes(self, course, directory):
        local_filename = os.path.join(directory, "quizzes.csv")
        with open(local_filename, "w+") as csv_file:
            self.stats_service.generate_quizzes_csv_to_file(course, csv_file)
            return local_filename

    @staticmethod
    def save_structure(course, directory, contents_files_urls, quizzes_filename):
        fields = [
            "lesson_name",
            "lesson_description",
            "content_name",
            "content_description",
            "content",
        ]

        local_filename = os.path.join(directory, "course_structure.csv")
        with open(local_filename, "w+") as csv_file:
            writer = csv.DictWriter(
                csv_file, fields, extrasaction="ignore", dialect="excel"
            )
            writer.writeheader()

            data_list = []
            for lesson in course.lessons:
                for content in lesson.contents:
                    content_info = ""
                    file_url = contents_files_urls.get(
                        content.id, {"filename": quizzes_filename}
                    )
                    if file_url.get("filename"):
                        content_filename = os.path.basename(file_url.get("filename"))
                        content_info = f"files/{content_filename}"
                    elif file_url.get("url"):
                        content_info = file_url.get("url")
                    data = {
                        "lesson_name": lesson.name,
                        "lesson_description": lesson.description,
                        "content_name": content.name,
                        "content_description": content.description,
                        "content": content_info,
                    }
                    data_list.append(data)
            writer.writerows(data_list)

    def upload_compacted_file(self, directory):
        with tempfile.NamedTemporaryFile(suffix="_smartzap", delete=False) as tmp_file:
            shutil.make_archive(tmp_file.name, "zip", directory)

            local_filename = f"{tmp_file.name}.zip"
            s3_data = self.s3.send_zip_file(file_path=local_filename)

            if os.path.isfile(local_filename):
                os.remove(local_filename)

        return s3_data["url"]

    def delete_course(self, course, file_url):
        self.notification_service.notify_course_download(
            "Your course backup is available for download", file_url
        )
        self.course_service.delete(course.id)

    @staticmethod
    def clean_temp_dir(directory):
        try:
            shutil.rmtree(directory)
        except OSError as e:
            logger.error(f"Error: {e.filename} - {e.strerror}")
