import urljoin
from domain.client import make_http_client
from domain.clients.token_service import TokenService


class LearnAnalyticsClient:
    def __init__(self, url: str, token_service: TokenService, http_timeout_sec: int):
        self.url = url
        self.token_service = token_service
        self._http_timeout_sec = http_timeout_sec

    def generate_user_activities_export(self, workspace_id: str, filters: dict, user_token: str) -> dict:
        url = urljoin.url_path_join(self.url, '/reports/smartzap-user-activities-exports')
        payload = {"report_format": "XLSX", "filters": filters}
        return self._make_request(payload, url, user_token, workspace_id)

    def generate_enrollments_export(self, workspace_id: str, filters: dict, user_token: str) -> dict:
        url = urljoin.url_path_join(self.url, '/reports/smartzap-enrollments-exports')
        payload = {"report_format": "XLSX", "filters": filters}
        return self._make_request(payload, url, user_token, workspace_id)

    def _make_request(self, payload, url, user_token, workspace_id):
        with make_http_client({
                'Authorization': user_token,
                'x-client': workspace_id,
                'Cache-Control': 'no-cache'
        }) as http:
            response = http.post(url, json=payload, timeout=self._http_timeout_sec)
            return response.json()

    def get_report(self, report_id: str, workspace_id: str) -> dict:
        url = urljoin.url_path_join(self.url, f'/reports/{report_id}')
        token = self.token_service.get_integration_token()
        with make_http_client({
            'Authorization': f"Bearer {token}",
            'x-client': workspace_id,
            'Cache-Control': 'no-cache'
        }) as http:
            response = http.get(url, timeout=self._http_timeout_sec)
            return response.json()
