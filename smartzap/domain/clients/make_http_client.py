import requests
from requests.adapters import HTTPAdapter

HTTP_MAX_RETRIES = 3


# Returns a http client session with retry capabilities.
# Ref.: https://requests.readthedocs.io/en/latest/user/advanced/#transport-adapters
def make_http_client(headers: dict = None) -> requests.Session:
    # print('get_http_client', headers)
    retry_adapter = HTTPAdapter(max_retries=HTTP_MAX_RETRIES)
    session = requests.Session()
    if headers:
        session.headers.update(headers)
    session.mount('http://', retry_adapter)
    session.mount('https://', retry_adapter)
    return session
