from tasks import task


class TasksClient:
    def dispatch_schedule(self, schedule_id):
        task.dispatch_message.apply_async(args=[schedule_id], countdown=5)

    def dispatch_message_by_external(self, schedule_id):
        task.dispatch_message_by_external.apply_async(args=[schedule_id], countdown=5)

    def send_message(
        self,
        phone_number,
        message_type,
        message_data,
        callback_url,
        lang='',
        private_channels=None,
        media_url=None
    ):
        args = [phone_number, message_type, message_data, callback_url, lang, private_channels, media_url]
        task.send_message.apply_async(args=args, countdown=5)
