import binascii
import json

import requests


class ReportGeneratorClient:
    def __init__(self, server_api_url: str):
        """
        Initializes the ReportGeneratorClient with the server API URL and temporary directory path.

        :param server_api_url: The base URL for the API that processes report generation.
        """
        self.base_url = server_api_url

    def clean_data_set(self, data_set: dict) -> dict:
        """
        Cleans the provided data set to ensure it’s JSON-compatible by converting non-serializable types.

        :param data_set: The data set to be cleaned for JSON compatibility.
        :return: A JSON-compatible dictionary with all values converted to strings where necessary.
        """
        return json.loads(json.dumps(data_set, default=str))

    def generate_report(
        self,
        jasper_file_url: str,
        dataset: dict,
        jasper_sub_file_url: str = None,
        params: dict = None
    ) -> str:
        """
        Generates a report by sending a POST request to the server with necessary Jasper report data,
        parameters, and data set, then decodes and saves the report as a PDF file.

        :param jasper_file_url: URL or path for the main Jasper report file.
        :param dataset: The data set to be included in the report, cleaned for JSON compatibility.
        :param jasper_sub_file_url: Optional URL or path for a Jasper subreport file if required.
        :param params: Optional parameters for report generation.
        :return: The report pdf file buffer.

        :raises binascii.Error: If there’s an error in decoding the report content or a server-side error occurs.
        """
        params = params or {}

        response = requests.post(
            f"{self.base_url}/",
            json={
                "jasper_file_url": f"/{jasper_file_url}",
                "jasper_sub_file_url": f"/{jasper_sub_file_url}" if jasper_sub_file_url else None,
                "dataset": self.clean_data_set(dataset),
                "params": params
            }
        )

        data = response.text
        if response.status_code != 200 or "error" in data:
            raise binascii.Error
        return data
