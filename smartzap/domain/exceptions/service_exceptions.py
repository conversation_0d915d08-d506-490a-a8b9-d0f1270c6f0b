class NotFoundException(Exception):
    def __init__(self, message):
        self.message = message


class IntegrityException(Exception):
    def __init__(self, message):
        self.message = message


class ServiceException(Exception):
    def __init__(self, message):
        self.message = message


class CourseNotFound(NotFoundException):
    def __init__(self, message="Course not found"):
        self.message = message


class UserNotFound(NotFoundException):
    def __init__(self, message="User not found"):
        self.message = message

class TokenException(Exception):
    pass
