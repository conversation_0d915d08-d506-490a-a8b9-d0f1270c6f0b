from domain.exceptions.service_exceptions import ServiceException


class NPSException(Exception):
    pass


class EnrollmentNotCompletedException(NPSException):
    def __init__(self):
        super().__init__("Unable to register score. The user's enrollment is not completed.")


class FeedbackAlreadyRegisteredException(NPSException):
    def __init__(self):
        super().__init__("Unable to register score. Feedback has already been registered for this enrollment.")


class CourseNotAcceptingNPSSurveyException(NPSException):
    def __init__(self):
        super().__init__("Unable to register score. The course is not accepting NPS surveys.")


class NPSScoreOutOfRangeException(NPSException, ServiceException):
    def __init__(self):
        super().__init__("Invalid NPS score. The score must be a number between 0 and 10.")
