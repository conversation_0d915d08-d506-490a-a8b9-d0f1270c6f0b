sonar.projectKey=Keeps-Learn_keeps-smartzap-server_AYm9l2639dESJCsQS4C2
sonar.sources=.
sonar.verbose=false
sonar.exclusions = **.html, smartzap/app.py, smartzap/application.py, smartzap/gunicorn_config.py, smartzap/cron_schedulers.py, smartzap/wsgi.py, smartzap/helper_certificate.py, smartzap/helper_schedulers_simulation.py, smartzap/helper_script_manual.py, smartzap/rounding_performance.py, smartzap/domain/config.py, smartzap/domain/database.py, smartzap/domain/di.py, smartzap/tasks/scheduler.py, smartzap/flask_extensions/auth.py, smartzap/flask_extensions/flask_ext.py, smartzap/flask_extensions/swagger.py, smartzap/flask_extensions/webargs.py, smartzap/flask_extensions/webfilters.py, smartzap/integration/*, templates/*, venv/*, smartzap/alembic/**, bot-clone/*, load_tests/*, smartzap/scripts/**

sonar.python.version=3
sonar.python.coverage.reportPaths=.reports/coverage/coverage.xml
sonar.python.xunit.reportPath=.reports/xunit/xunit.xml
sonar.python.flake8.reportPaths=.reports/output_flake.txt