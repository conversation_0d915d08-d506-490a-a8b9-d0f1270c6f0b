services:
  db:
    image: postgres:13.0-alpine
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=${POSTGRES_DB:-smartzap_dev_db}
    ports:
      - "5432:5432"
    networks:
      - smartzap_network

  web:
    build: 
      context: ./smartzap
      dockerfile: Dockerfile
    command: gunicorn --access-logfile=- --config gunicorn_config.py app --bind 0.0.0.0:8000
    volumes:
      - ./smartzap/:/usr/src/app/
    ports:
      - "8000:8000"
    env_file:
      - .env
    networks:
      - smartzap_network

  caixa-web:
    build:
      context: ./smartzap
      dockerfile: Dockerfile
    command: gunicorn --access-logfile=- --config gunicorn_config.py caixa-app --bind 0.0.0.0:8000
    volumes:
      - ./smartzap/:/usr/src/app/
    ports:
      - "8000:8000"
    env_file:
      - .env
    networks:
      - smartzap_network

  worker:
    build:
      context: ./smartzap
      dockerfile: Dockerfile
    entrypoint: /usr/bin/supervisord -c /etc/supervisord.conf
    volumes:
      - ./smartzap/:/usr/src/app/
    env_file:
      - .env
    networks:
      - smartzap_network
    tty: true
    stdin_open: true

  rabbitmq:
    image: rabbitmq:3-management-alpine
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_DEFAULT_USER:-admin}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_DEFAULT_PASS:-admin}
    networks:
      - smartzap_network

volumes:
  postgres_data:

networks:
  smartzap_network:
    driver: bridge
